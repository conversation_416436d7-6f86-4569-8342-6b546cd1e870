# 网络监测系统项目目录结构规范

## 新的目录结构设计

```
network_monitor/
├── README.md                           # 项目说明文档
├── docker-compose.yml                  # 主要的Docker Compose配置
├── docker-compose.override.yml         # 开发环境覆盖配置
├── .env.template                       # 环境变量模板
├── .env                               # 环境变量文件（不提交到版本控制）
├── .gitignore                         # Git忽略文件
├── Makefile                           # 项目管理命令
│
├── docs/                              # 文档目录
│   ├── 规范.md                        # 功能规范文档
│   ├── 设计概要.md                    # 设计概要文档
│   ├── deployment.md                  # 部署文档
│   └── api.md                         # API文档
│
├── scripts/                           # 脚本目录
│   ├── setup.sh                      # 初始化脚本
│   ├── start.sh                      # 启动脚本
│   ├── stop.sh                       # 停止脚本
│   ├── backup.sh                     # 备份脚本
│   └── restore.sh                    # 恢复脚本
│
├── services/                          # 服务定义目录
│   ├── opensearch/                    # OpenSearch服务
│   │   ├── Dockerfile                 # 自定义镜像构建文件
│   │   ├── config/                    # 配置文件
│   │   │   ├── opensearch.yml
│   │   │   └── jvm.options
│   │   └── plugins/                   # 插件目录
│   │
│   ├── logstash/                      # Logstash服务
│   │   ├── Dockerfile
│   │   ├── config/                    # 配置文件
│   │   │   ├── logstash.yml
│   │   │   ├── pipelines.yml
│   │   │   └── jvm.options
│   │   ├── pipeline/                  # 管道配置
│   │   │   ├── input/
│   │   │   ├── filter/
│   │   │   └── output/
│   │   ├── patterns/                  # Grok模式文件
│   │   ├── templates/                 # 索引模板
│   │   └── jdbc/                      # JDBC驱动
│   │
│   ├── kafka/                         # Kafka服务
│   │   ├── Dockerfile
│   │   └── config/
│   │       ├── server.properties
│   │       └── log4j.properties
│   │
│   ├── mariadb/                       # MariaDB服务
│   │   ├── Dockerfile
│   │   ├── config/
│   │   │   └── my.cnf
│   │   └── init/                      # 初始化SQL脚本
│   │       ├── 01-create-databases.sql
│   │       └── 02-create-users.sql
│   │
│   ├── flink/                         # Flink服务
│   │   ├── Dockerfile
│   │   ├── config/
│   │   │   ├── flink-conf.yaml
│   │   │   └── log4j.properties
│   │   └── jobs/                      # Flink作业JAR文件
│   │
│   ├── packetbeat/                    # Packetbeat服务
│   │   ├── Dockerfile
│   │   └── config/
│   │       └── packetbeat.yml
│   │
│   ├── suricata/                      # Suricata服务
│   │   ├── Dockerfile
│   │   ├── config/
│   │   │   ├── suricata.yaml
│   │   │   └── rules/
│   │   └── logs/
│   │
│   └── network-monitor-app/           # 主应用服务
│       ├── Dockerfile
│       ├── config/
│       │   ├── application.yml
│       │   └── logback.xml
│       └── src/                       # 应用源代码
│           ├── main/
│           └── test/
│
├── config/                            # 全局配置目录
│   ├── certificates/                  # 证书文件
│   │   ├── ca/
│   │   ├── server/
│   │   └── client/
│   ├── secrets/                       # 密钥文件
│   └── shared/                        # 共享配置
│
├── data/                              # 数据持久化目录
│   ├── opensearch/                    # OpenSearch数据
│   ├── mariadb/                       # MariaDB数据
│   ├── kafka/                         # Kafka数据
│   ├── flink/                         # Flink检查点和保存点
│   └── logs/                          # 应用日志
│       ├── opensearch/
│       ├── logstash/
│       ├── kafka/
│       ├── mariadb/
│       ├── flink/
│       └── app/
│
├── monitoring/                        # 监控配置
│   ├── prometheus/
│   │   └── prometheus.yml
│   ├── grafana/
│   │   ├── dashboards/
│   │   └── provisioning/
│   └── alertmanager/
│       └── alertmanager.yml
│
└── tests/                             # 测试目录
    ├── integration/                   # 集成测试
    ├── performance/                   # 性能测试
    └── fixtures/                      # 测试数据
```

## 目录说明

### 1. 根目录文件
- `docker-compose.yml`: 生产环境配置
- `docker-compose.override.yml`: 开发环境特定配置
- `.env.template`: 环境变量模板，包含所有必需的环境变量
- `Makefile`: 提供常用的项目管理命令

### 2. services/ 目录
- 每个服务一个子目录
- 包含Dockerfile、配置文件、初始化脚本等
- 配置文件按功能分类组织

### 3. config/ 目录
- 存放全局配置文件
- 证书和密钥文件
- 跨服务共享的配置

### 4. data/ 目录
- 所有持久化数据的统一存储位置
- 按服务分类组织
- 便于备份和恢复

### 5. scripts/ 目录
- 项目管理和运维脚本
- 自动化部署和维护工具

## 最佳实践原则

1. **分离关注点**: 配置、数据、日志、代码分别存放
2. **标准化命名**: 使用一致的命名规范
3. **环境隔离**: 通过环境变量和覆盖文件支持多环境
4. **安全性**: 敏感信息通过环境变量或密钥文件管理
5. **可维护性**: 清晰的目录结构便于维护和扩展
6. **版本控制**: 合理的.gitignore配置，避免提交敏感信息

## 迁移计划

1. 创建新的目录结构
2. 迁移现有配置文件
3. 更新Docker Compose配置
4. 创建环境变量模板
5. 编写管理脚本
6. 更新文档
