# 网络安全监测装置设计概要

## 1. 系统架构概述

本系统采用三层架构设计：感知层、应用层、交互层，基于现代化开源技术栈构建分布式网络安全监测装置。

### 1.1 整体架构图
```
┌─────────────────────────────────────────────────────────────┐
│                        交互层                                │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │   监测平台接口   │  │   本地管理界面   │  │   装置管理    │ │
│  │    (Java)      │  │    (Java)      │  │   (Java)     │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                        应用层                                │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐          │
│  │ 数据处理模块 │  │ 空间测绘模块 │  │ 行为分析模块 │          │
│  │   (Flink)   │  │   (Flink)   │  │   (Flink)   │          │
│  └─────────────┘  └─────────────┘  └─────────────┘          │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐          │
│  │ 对象管控模块 │  │ 插件应用模块 │  │ 装置管理模块 │          │
│  │   (Java)    │  │   (Java)    │  │   (Java)    │          │
│  └─────────────┘  └─────────────┘  └─────────────┘          │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                        感知层                                │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐          │
│  │ 主机监控模块 │  │ 登录交互模块 │  │通用日志采集  │          │
│  │   (Java)    │  │   (Java)    │  │ (Logstash)  │          │
│  └─────────────┘  └─────────────┘  └─────────────┘          │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐          │
│  │ 流量采集模块 │  │数据库采集模块│  │ 探测扫描模块 │          │
│  │(Packetbeat/ │  │   (Java)    │  │   (Java)    │          │
│  │ Suricata)   │  │             │  │             │          │
│  └─────────────┘  └─────────────┘  └─────────────┘          │
└─────────────────────────────────────────────────────────────┘
```

### 1.2 数据存储架构
```
┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐
│   配置数据库     │  │   消息队列       │  │  日志/模型存储   │
│   (MariaDB)     │  │   (Kafka)       │  │ (OpenSearch)    │
└─────────────────┘  └─────────────────┘  └─────────────────┘
```

## 2. 技术组件映射

### 2.1 感知层组件映射

| 功能模块 | 技术组件 | 主要职责 |
|---------|---------|---------|
| 主机监控 | Java应用 | 接收监测代理软件数据，管理代理注册 |
| 登录交互 | Java应用 | SSH登录网络设备，执行管控指令 |
| 通用日志采集 | Logstash | SNMP/SYSLOG/UDP日志采集和转换 |
| 流量采集 | Packetbeat/Suricata | 网络流量镜像采集和解析 |
| 数据库采集 | Logstash | 消息总线方式数据库数据采集 |
| 探测扫描 | Java应用 | ARP/ICMP/TCP/UDP扫描和资产发现 |

### 2.2 应用层组件映射

| 功能模块 | 技术组件 | 主要职责 |
|---------|---------|---------|
| 数据处理 | Flink | 资产/行为/连接模型处理，通用日志处理 |
| 空间测绘 | Flink | 资产测绘，物理连接关系分析 |
| 行为分析 | Flink + OpenSearch | 基线分析，规则分析，智能检测 |
| 对象管控 | Java应用 | 代理管控，直接管控 |
| 插件应用 | Java应用 | 插件生命周期管理，服务接口提供 |
| 装置管理 | Java应用 | 参数管理，资源管理，升级管理 |

### 2.3 交互层组件映射

| 功能模块 | 技术组件 | 主要职责 |
|---------|---------|---------|
| 平台交互 | Java应用 | 与监测平台的认证注册、数据交互 |
| 本地管理 | Java应用 | 本地图形化界面，用户权限管理 |
| 装置管理 | Java应用 | 装置状态监控，配置管理 |

### 2.4 数据存储组件映射

| 存储类型 | 技术组件 | 存储内容 |
|---------|---------|---------|
| 配置存储 | MariaDB | 系统配置，资产参数，证书信息 |
| 消息队列 | Kafka | 实时数据流，模块间通信 |
| 日志模型存储 | OpenSearch | 日志数据，资产/行为/连接模型 |
| 基线分析 | OpenSearch Anomaly Detection | 行为基线，异常检测 |

## 3. 核心模块设计

### 3.1 感知层设计

#### 3.1.1 通用日志采集模块 (Logstash)
- **输入插件配置**：
  - SNMP输入：支持SNMPv2c和SNMPv3
  - Syslog输入：UDP接收
  - SNMP-TRAP输入：异步告警接收
  - JDBC输入：数据库采集（支持MySQL、PostgreSQL、Oracle等）
  - HTTP输入：RESTful API数据采集
- **过滤器配置**：
  - 日志解析和标准化
  - GB/T31992标准转换
  - 数据清洗和验证
  - 数据库查询结果转换
- **输出配置**：
  - Kafka输出：实时数据流
  - OpenSearch输出：日志存储

#### 3.1.2 数据库采集模块 (Logstash)
- **JDBC输入配置**：
  - 支持多种数据库：MySQL、PostgreSQL、Oracle、SQL Server
  - 定时轮询：可配置采集周期
  - 增量采集：基于时间戳或ID的增量同步
  - SQL查询：自定义查询语句
- **消息总线集成**：
  - Kafka Connect：实时数据流采集
  - 数据库变更日志：Binlog、WAL等
  - 事件驱动：数据库触发器集成
- **数据转换**：
  - 字段映射和类型转换
  - 数据脱敏和过滤
  - 格式标准化

#### 3.1.3 流量采集模块 (Packetbeat/Suricata)
- **Packetbeat配置**：
  - 网络接口监听
  - 协议解析（HTTP、DNS、MySQL等）
  - 流量特征提取
- **Suricata配置**：
  - 深度包检测
  - 协议栈指纹识别
  - 行为模式识别

### 3.2 应用层设计

#### 3.2.1 数据处理模块 (Flink)
- **流处理作业**：
  - 资产模型更新作业
  - 行为模型生成作业
  - 连接模型计算作业
- **状态管理**：
  - 资产状态维护
  - 连接关系缓存
  - 行为历史记录

#### 3.2.2 行为分析模块 (Flink + OpenSearch)
- **基线分析**：
  - OpenSearch Anomaly Detection
  - 历史数据统计分析
  - 异常行为检测
- **规则分析**：
  - Flink CEP（复杂事件处理）
  - 规则引擎集成
  - 实时规则匹配

### 3.3 交互层设计

#### 3.3.1 平台交互模块 (Java)
- **认证注册**：
  - SM2/SM4加密通信
  - 证书管理
  - 密钥协商
- **数据交互**：
  - 行为上送
  - 消息通知
  - 数据调阅响应

## 4. 数据流设计

### 4.1 数据采集流
```
感知层采集 → Kafka → Flink处理 → OpenSearch存储
```

### 4.2 模型更新流
```
原始数据 → Flink模型处理 → 模型数据 → OpenSearch → 平台上送
```

### 4.3 告警处理流
```
异常检测 → 告警生成 → Kafka → 平台通知 → 管控响应
```

## 5. 部署架构

### 5.1 服务部署
- **容器化部署**：Docker + Docker Compose
- **单机部署**：所有服务运行在单台服务器上
- **配置管理**：Docker Compose环境变量和配置文件

### 5.2 单机架构设计
- **数据存储**：OpenSearch单节点，MariaDB单实例
- **消息队列**：Kafka单节点
- **应用服务**：单实例部署，通过Docker容器隔离

### 5.3 Docker Compose服务架构
```yaml
# 核心服务组件
services:
  - opensearch: 日志和模型数据存储
  - mariadb: 配置数据存储
  - kafka: 消息队列
  - zookeeper: Kafka协调服务
  - logstash: 日志采集和数据库采集
  - flink-jobmanager: Flink作业管理
  - flink-taskmanager: Flink任务执行
  - packetbeat: 流量采集
  - suricata: 深度包检测
  - network-monitor-app: 主应用服务(Java)
```

### 5.4 监控运维
- **容器监控**：Docker stats，容器健康检查
- **日志监控**：Docker logs，集中日志收集
- **服务监控**：应用内置健康检查接口

## 6. 安全设计

### 6.1 通信安全
- **加密传输**：SM2/SM4国密算法
- **身份认证**：双向证书认证
- **数据完整性**：数字签名验证

### 6.2 访问控制
- **三权分立**：管理员、操作员、审计员
- **权限隔离**：基于角色的访问控制
- **审计日志**：操作行为全程记录

## 7. 性能指标（单机部署）

### 7.1 处理能力
- **日志处理**：1万条/秒（单机Logstash）
- **流量分析**：100Mbps（单机Packetbeat/Suricata）
- **并发连接**：200个设备
- **数据库采集**：支持10个数据库实例并发采集

### 7.2 存储容量
- **日志存储**：7天热数据，30天冷数据（单机OpenSearch）
- **模型数据**：实时更新，7天历史保留
- **配置数据**：持久化存储（MariaDB）
- **原始流量**：24小时循环存储

### 7.3 系统资源要求
- **CPU**：8核心以上
- **内存**：32GB以上
- **存储**：1TB SSD（系统+数据）
- **网络**：千兆网卡

## 8. 扩展性设计

### 8.1 插件架构
- **插件接口**：标准化API
- **动态加载**：运行时插件管理
- **资源隔离**：插件沙箱机制

### 8.2 单机扩展策略
- **垂直扩展**：增加CPU、内存、存储资源
- **容器扩展**：通过Docker Compose调整容器资源限制
- **数据分区**：按时间或业务维度分区存储
- **服务优化**：调整Logstash、Flink并行度配置

### 8.3 未来集群化支持
- **多机部署**：支持Docker Swarm或Kubernetes部署
- **数据分片**：OpenSearch集群模式
- **负载均衡**：服务间负载分发
- **高可用**：主备切换和故障恢复
