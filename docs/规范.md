# 功能要求
## 功能架构
监测装置整体功能架构分为感知层、应用层、交互层三层。感知层接入所在网络空间资产，实现数据采集和资产控制。应用层对采集数据进行分析与处理，实现多种监测与管控功能应用。交互层接入相应监测平台，实现数据安全交互。

## 6.2 感知层
6.2.1 基本要求
感知层根据采集方式分为以下几大类:
a)主机监控:支持通过监测代理软件，对主机进行数据采集与控制;
b)登录交互:支持通过远程登录方式(SSH 登录)，对安防设备、网络设备进行数据采集和控制;
c)通用日志采集:支持通过 SNMP 采集和 SYSLOG 采集(UDP 接收)，对安防设备、网络设备进行数据采集，SNMP 采集支持 V2C 及 V3 版本;
d)流量采集:支持对网络设备镜像流量进行采集、分析;
e)数据库采集:支持通过消息总线方式对数据库进行数据采集;
f)探测扫描:支持通过 ARP 请求、ICMP 探测、UDP 扫描、TCP 全/半连接扫描等技术，对网络空间和指定设备进行扫描，发现存活资产，获取资产属性信息;
g)感知层数据采集与控制功能要求详见附录 A。
6.2.2 主机监控
主机监控要求包括:
a)支持接收监测代理软件的行为模型数据，包括:硬件行为、软件行为、网络行为、连接行为;
b)支持接收和调阅监测代理软件的资产模型数据;
c)支持对监测代理软件进行注册管理，并转发监测平台生成的资产ID;
d)支持对监测代理软件通过代理的方式转发管控指令，并获取结果;
e)支持向下兼容监测代理软件的通用采集信息，信息内容详见Q/GDW 11914-2018《电力监控系统网络安全监测装置技术规范》附录F.3.2.1-F.3.2.3 节。
6.2.3登录交互
登录交互要求包括:
a)支持通过 SSH协议登录网络设备与安全防护设备，获取交换机的路由、VLAN、ACL、ARP、网络邻居设备等信息，获取路由器的路由、ACL、网络邻居设备等信息，获取防火墙设备的路由、ARP、策略等信息;
b)支持通过 SSH 协议登录交换机、防火墙，执行管控指令，实现设备控制。
6.2.4通用日志采集通用日志采集要求包括:
a)支持通过 SNMP 协议对网络设备进行数据采集，包括:ARP 信息，MAC 地址表等;
b)支持通过 SNMP 协议对防火墙进行数据采集，包括:ARP 信息，路由信息、策略信息等;
c)支持通过 UDP 协议、SNMP-TRAP 协议采集原始日志，通过采集日志转化策略，转化为符合 GB/T31992 标准的通用采集信息，策略详见附录B。
6.2.5流量采集
流量采集要求包括:
a)支持对网络设备镜像流量进行采集;
b)支持对流量按网络层级逐一解析生成流量特征信息，包括:源MAC地址、目的MAC 地址、五元
组、通信持续时间、交互包数、交互字节数等特征;
c)支持对流量按网络层级逐一解析生成流量行为信息，包括:网络访问、远程登录、文件下载等行为。
6.2 6数据库采集
数据库采集要求包括:
a)支持通过消息总线方式对数据库进行数据采集。
6.2.7探测扫描
探测扫描要求包括:
a)支持对所辖网络空间进行主动的轻量级探测，发现存活资产的IP、MAC 地址;
b)支持对指定资产进行扫描，采集资产的操作系统、开放端口、通信协议、协议版本号、服务名称、协议栈指纹等信息;
c)探测扫描应不影响现场业务的生产运行。
## 6.3应用层
6.3.1数据处理
(1)资产模型
资产模型要求包括:
a)支持通过主机监控获取并更新资产模型数据;
b)支持模型化处理登录交互获取的运行和配置信息，更新资产模型数据，包括:硬件、网络模型
c)支持模型化处理主机监控、通用日志采集获取的通用采集信息，更新资产模型数据,包括硬件。
软件、网络模型;
d)支持模型化处理流量采集获取的流量特征信息，更新资产模型数据，包括:网络模型;
e)支持模型化处理探测扫描获取的存活资产指纹信息，更新资产模型数据，包括:软件、网络接
口、网络监听等模型;
f)支持模型化处理数据库采集获取的的运行信息，更新资产模型数据，包括:软件模型;
g)支持根据监测代理软件上送的网络接口模型或资产拼接指令，对未确认资产模型数据进行合并。
(2)行为模型
行为模型要求包括:
a)支持通过主机监控获取行为模型数据;
b)支持模型化处理主机监控、通用日志采集获取的通用采集信息，生成行为模型数据，包括:硬件、网络等模型;
c)支持模型化处理流量采集获取的流量行为信息，生成行为型数据，包括:网络型;
d)支持模型化处理数据库采集获取的运行信息，生成行为模型数据，包括:软件模型;
e)支持对行为模型数据内缺失信息进行补充。
(3)连接模型
连接模型要求包括:
a)支持基于资产模型中的网络接口、地址学习表、网络邻居等资产模型数据生成连接关系模型数
b)支持基于行为模型中的网络访问行为、会话登录/退出、网络文件传输等行为模型数据及流量采集的流量特征信息生成访问关系模型数据。
(4)通用日志
通用日志要求包括:
a)支持处理、转发主机监控、通用日志采集获取的通用采集信息，包括:资产运行状态通知、安防设备告警通知等数据，通用采集信息格式符合 GB/T 31992 标准。
(5)原始流量
原始流量要求包括:
a)支持对原始流量数据进行存储;
b)支持通过时间范围、源地址、 目的地址、源端口、目的端口、通信协议对原始流量进行筛选检索;
c)支持通过指定会话 ID(组)对原始流量进行选检索。
6.3.2空间测绘
(1) 资产测绘
资产测绘要求包括:
a)支持通过主动方式、被动方式对资产的存活状况进行感知，更新资产在线状态:主动方式包括:探测扫描、登录交互、通用日志采集中的SNMP 部分;被动方式包括:主机监控、流量采集、数据库采集、通用日志采集的 SNMP-TRAP 和 SYSLOG 采集部分;
b)支持对指定资产节点进行探测扫描，更新资产模型数据;
c)支持加载、更新统一的资产识别模块，通过资产模型、流量特征信息进行资产识别。
(2)物理连接关系
物理连接关系要求包括:
a)支持通过资产模型中网络模型计算出资产间物理连接关系，并更新相应的物理连接模型数据;
b)支持分析资产模型的变化调整物理连接关系，生成物理连接关系变更通知:资产模型的变化包括:存活状态变化、多 IP 资产合并、退运等。
(3)网络访问关系
网络访问关系要求包括:
a)支持通过已采集的网络访问行为计算出网络访问关系;
b)支持分析网络访问关系，生成网络访问关系变更通知。
6.3.3行为分析
(1)基线分析
基线分析要求包括:
a)支持通过资产模型、行为模型的历史数据统计生成资产运行基线，基线类型详见附录C;
b)支持对行为模型数据进行基线比对分析，出现越线情况，补充行为模型内的行为标签，作为可疑行为。
(2)规则分析
规则分析要求包括:
a)支持加载、更新规则库，通过行为模型数据进行规则匹配分析;
b)支持对行为模型进行基础规则分析，出现规则匹配情况下补充行为模型内的行为标签，作为可疑行为。
(3)智能检测
智能检测要求包括:
a)预置两种或以上智能检测框架;
b)支持使用算法程序加载配套的智能检测模型，在智能检测框架中进行推理分析，生成相应的安全告警;
c)支持对智能检测框架、智能检测模型、算法程序进行升级和替换。
6.3.4对象管控
(1)代理管控
代理管控要求包括:
a)支持转发平台对监测代理软件的管控指令，包括:数据调阅、应急处置、风险识别等。详见Q/GDWxxXXX.3-20口口《电力监控系统网络安全监测代理软件技术规范》
(2)直接管控
直接管控要求包括:
a)支持通过登录交互发送管控指令对交换机进行操作，包括:交换机网口状态阻断和恢复等;
b)支持通过登录交互发送管控指令对防火墙进行操作，包括:生成防火墙设备策略实现关闭或恢复源设备指定端口/协议对目的设备指定端口/协议的访问等
6.3.5插件应用
插件应用要求包括:
a)支持以插件形式扩展装置功能,插件形态为动态库，扩展功能包括:漏洞扫描、可信管理等;
b)支持向插件提供服务接口，包括:平台交互、对象交互、数据访问、插件存储;
c)插件应用要求详见附录 D。
6.3.6装置管理
(1)参数管理
参数管理要求包括:
a)支持对监测装置基础参数进行配置管理，包括系统参数、通信参数、设备运行参数;
b)支持对监测装置生成设备运行参数进行配置管理，包括连续登录失败阈值、归并事件归并周期
c)支持对监测装置数据传输参数进行配置管理，包括加密、验签、MAC、压缩的启停;
d)支持对监测装置的行为归并参数进行配置管理，可设置归并行为包括:网络访问、进程启停、文件访问;
e)支持对资产参数进行配置管理，包括资产信息的添加、注册、删除、修改、拼接、查看;
f)支持对证书参数进行配置管理，包括证书的添加、删除、修改、查看;
g)支持对采集日志转化策略进行配置管理，包括策略的添加、删除、修改、查看;
h)支持通过配置交互对参数进行备份和恢复，格式详见附录B;
i) 支持通过配置交互对策略进行备份和恢复，格式详见附录E。
(2) 资源管理
资源管理要求包括:
a)对装置自身硬件状态(CPU、内存、磁盘)进行监视和调度，保证主要业务正常运行。
(3)装置升级
装置升级要求包括:
a)支持通过配置交互对监测装置内模块、软件进行升级更新;
b)支持监测平台下发的远程升级指令，包括升级状态查询、升级包下载、执行升级三个子功能其中升级包下载包括升级包下载启动、升级包下载中、升级包下载结束三个连续过程，支持断点续传。
(4)基线管理
基线管理要求包括:
a)支持对本地资产基线库进行配置管理，包括:资产运行基线的添加、除、修改、查看;资产运行基线包括:服务端网络访问基线、进程网络访问基线、网络访问流量基线等;
b)支持对基线训练参数进行配置管理，包括:周期、范围等;
c)支持通过配置交互对基线进行备份和恢复，格式详见附录C。
(5)插件管理
插件管理要求包括:
a)支持对插件的全生命周期管理，包括:安装、升级、启动、停止、卸载、删除;安装、升级需对插件进行签名验签;
b)存在多个相同业务类型插件时，避免同一业务类型插件重复运行;
c)支持对插件的运行状态进行监视、审计、查询:
d)支持对插件进行资源调度，优先保障装置自身业务的正常运行;
e)插件管理要求详见附录 D。
## 6.4交互层
6.4.1基本要求
a)与监测平台交互的基本要求详见Q/GDW 12045-2024《电力监控系统网络安全监测平台技术规范》:
b)监测装置与监测平台通信的具体通信格式，认证注册流程要求详见附录F;
c)监测装置与监测平台的行为上送、消息通知、数据调阅、配置交互、命令控制功能要求详见附录G。
6.4.2认证注册
认证注册要求包括:
a)认证注册前保证监测装置内存在平台证书(包含公钥)、装置证书(包含公钥、私钥);
b)监测装置与平台的首次连接需进行协商认证，向平台传输装置公钥证书，并交换随机数，此过程中通过平台证书进行SM2加密，SM2 加密方法依照GM/T0009-2012:监测装置通过随机数计算出对称密钥用于后续数据交互使用，加密方式为 SM4 加密，SM4 加密方法依照 GM/T 0002-2012;
c)监测平台在完成协商认证后，根据自身情况与平台进行注册交互，同步监测装置资产 ID。
6.4.3 行为上送
行为上送要求包括:
a)文持通过归并策略对行为模型数据进行归并;
b)支持向监测平台发送行为模型数据，包括:硬件行为、软件行为、网络行为、连接行为。
6.4.4消息通知
消息通知要求包括:
a)支持向监测平台发送资产运行状态通知;
b)支持向监测平台发送连接模型变更通知;
c)支持向监测平台发送安防设备告警通知;
d)支持向监测平台转发监测装置内的插件应用数据。
6.4.5数据调阅
数据调阅要求包括:
a)支持响应监测平台的资产模型、行为模型和连接模型调阅请求:
b)支持响应监测平台的原始流量调阅请求;
c)支持响应监测平台的日志信息调阅请求;
d)支持响应监测平台的装置自身信息、监测对象状态、平台通信状态调阅请求。
6.4.6配置交互
配置交互要求包括:
a)支持响应监测平台的运行参数配置管理指令，包括:网口参数、路由配置参数、NTP 对时参数、通信参数、 自身运行参数、设备运行参数、数据传输参数、行为归并参数、资产参数、证书参数;
b)支持响应监测平台的资产注册、资产拼接指令;
c)支持响应监测平台的基线管理指令;
d)支持响应监测平台的软件升级指令和备份恢复指令;
e)支持响应监测平台的策略配置指令，包括:采集日志转化策略。
6.4.7命令控制
命令控制要求包括:
a)支持响应监测平台的探测扫描命令，包括:网段探测，指纹信息获取;
b)支持转发监测平台对监测代理软件管控指令，包括:采集周期配置、调阅配置、日志管理、安全策略管理、版本管理、插件管理、注册管理;
c)支持响应监测平台对网络设备、防火墙的管控命令;
d)支持转发监测平台对监测装置内插件管理指令。
6.4.8本地管理
应提供本地图形化界面对监测装置进行监视和管理，实现数据调阅、配置交互(资产注册、资产拼资产参数-添加/删除/修改除外)要求功能，具备以下本地专有功能:
a)支持生成装置私钥和公钥证书请求，公钥证书请求可导出;
b)支持本地用户管理，基于三权分立原则划分管理员、操作员、审计员等不同角色，并为不同角色分配不同权限:满足不同角色的权限相互制约要求，不存在拥有所有权限的超级管理员角色;
c)支持审计日志的查看，日志类型至少包括登录日志、操作日志、维护日志等，日志内容包括日志级别、 日志时间、日志类型、日志内容等信息，日志具备可读性。

