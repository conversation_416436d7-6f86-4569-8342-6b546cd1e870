package main

import (
	"encoding/json"
	"io/ioutil"
	"net/http"
	"strconv"
	"math"
	"os/exec"
	"fmt"

	"gopkg.in/yaml.v2"

	log "github.com/sirupsen/logrus"
)

type Allocation struct {
	Node     string `json:"node"`
	Percent string `json:"disk.percent"`
	Total	string `json:"disk.total"`
}

func checkDiskUsage(username, password string) (int, int, error) {
	url := "http://elasticsearch:9200/_cat/allocation?format=json&bytes=gb&local=true"
	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return 0, 0, err
	}
	req.SetBasicAuth(username, password)

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return 0, 0, err
	}
	defer resp.Body.Close()

	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return 0, 0, err
	}

	var allocations []Allocation
	err = json.Unmarshal(body, &allocations)
	if err != nil {
		return 0, 0,err
	}

	for _, alloc := range allocations {
		if alloc.Node == "elasticsearch" {
			percent, err := strconv.Atoi(alloc.Percent)
			if err != nil {
				return 0, 0, err
			}
			total, err := strconv.Atoi(alloc.Total)
			if err != nil {
				return 0, 0, err
			}
			return percent, total, nil
		}
	}

	return 0, 0, fmt.Errorf("node not found")
}

// 读取disk_check_cfg.yml文件，格式为yaml，获取配置
func readDiskCheckConfig() (int, int, error) {

	filePath := "/.curator/disk_check_cfg.yml"
	file, err := ioutil.ReadFile(filePath)
	if err != nil {
		return 0, 0, err
	}

	var config map[string]interface{}
	err = yaml.Unmarshal(file, &config)
	if err != nil {
		return 0, 0, err
	}

	warnThreshold := config["warn_threshold"].(int)
	deleteThreshold := config["delete_threshold"].(int)
	return warnThreshold, deleteThreshold, nil

}

func setCuratorConfig(deleteSize int) error {

	filePath := "/.curator/delete_log_with_disk.yml"
	file, err := ioutil.ReadFile(filePath)
	if err != nil {
		return err
	}

	var config map[string]interface{}
	err = yaml.Unmarshal(file, &config)
	if err != nil {
		return err
	}

	action := config["actions"].(map[interface{}]interface{})
	filter := action[1].(map[interface{}]interface{})["filters"].([]interface {})
	for i := 0; i < len(filter); i++ {
		if filter[i].(map[interface{}]interface{})["filtertype"] == "space" {
			filter[i].(map[interface{}]interface{})["disk_space"] = deleteSize
			break
		}
	}

	yamlData, err := yaml.Marshal(config)
	if err != nil {
		return err
	}

	err = ioutil.WriteFile(filePath, yamlData, 0644)
	if err != nil {
		return err
	}
	return nil

}
func main() {
	username := "elastic"
	password := "hykj@123"
	diskUsage, diskTotal, err := checkDiskUsage(username, password)
	if err != nil {
		log.Error(err)
		return
	}
	log.Infof("disk usage is %d", diskUsage)
	log.Infof("disk total is %d", diskTotal)

	warnThreshold, deleteThreshold, err := readDiskCheckConfig()
	if err != nil {
		log.Error(err)
		return
	}
	log.Infof("warnThreshold is %d", warnThreshold)
	log.Infof("deleteThreshold is %d", deleteThreshold)

	// 如果磁盘使用率超过删除阈值
	if diskUsage >= deleteThreshold {
		log.Info("disk usage is above delete threshold")
		// 计算需要删除多少空间，向上取整
		deleteSize := int(math.Ceil(float64(diskTotal) * (float64(diskUsage)-float64(warnThreshold)) / 100.0))
		log.Infof("delete size is %d", deleteSize)
		if deleteSize < 0 {
			log.Warnf("delete size is less than 0")
			return
		}
		// 修改curator配置
		err = setCuratorConfig(deleteSize)
		if err != nil {
			log.Error(err)
			return
		}
		log.Info("set curator config success")
		// 执行curator命令
		cmd := exec.Command("/curator/curator", "/.curator/delete_log_with_disk.yml")
		output, err := cmd.CombinedOutput()
		if err != nil {
			log.Error(err)
			return
		}
		log.Infof("%s\n", output)
		log.Info("curator command executed successfully")
	}
	return
}

