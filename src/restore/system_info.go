package main

import (
	"bufio"
	"database/sql"
	"fmt"
	"log"
	"os"
	"strings"

	_ "github.com/go-sql-driver/mysql"
)

const (
	systemInfoFile = "/usr/local/system_info.txt"
	dbPassword     = "Hykj@123"
)

type SystemInfo struct {
	DeviceModel          string
	HardwareLogo         string
	SoftwareVersion      string
	SoftwareSerialNumber string
}

func main() {
	if len(os.Args) != 2 || (os.Args[1] != "backup" && os.Args[1] != "restore") {
		log.Fatalf("使用方法: %s [backup|restore]", os.Args[0])
	}

	switch os.Args[1] {
	case "backup":
		if err := backupSystemInfo(); err != nil {
			log.Fatalf("备份系统信息失败: %v", err)
		}
		log.Println("系统信息备份完成")
	case "restore":
		lines, err := readBackupFile()
		if err != nil {
			log.Fatalf("读取备份文件失败: %v", err)
		}
		if err := restoreSystemInfo(lines); err != nil {
			log.Fatalf("恢复系统信息失败: %v", err)
		}
		log.Println("系统信息恢复完成")
	}
}

func backupSystemInfo() error {
	dsn := fmt.Sprintf("root:%s@tcp(127.0.0.1:3306)/rzsj", dbPassword)
	db, err := sql.Open("mysql", dsn)
	if err != nil {
		return fmt.Errorf("连接数据库失败: %v", err)
	}
	defer db.Close()

	var info SystemInfo
	err = db.QueryRow(`
		SELECT device_model, hard_ware_logo, software_version, software_serial_number 
		FROM system_info 
		WHERE id=1
	`).Scan(&info.DeviceModel, &info.HardwareLogo, &info.SoftwareVersion, &info.SoftwareSerialNumber)
	if err != nil {
		return fmt.Errorf("查询系统信息失败: %v", err)
	}

	file, err := os.Create(systemInfoFile)
	if err != nil {
		return fmt.Errorf("创建备份文件失败: %v", err)
	}
	defer file.Close()

	writer := bufio.NewWriter(file)
	_, err = fmt.Fprintf(writer, "%s\n%s\n%s\n%s\n",
		info.DeviceModel,
		info.HardwareLogo,
		info.SoftwareVersion,
		info.SoftwareSerialNumber)
	if err != nil {
		return fmt.Errorf("写入备份文件失败: %v", err)
	}

	return writer.Flush()
}

func readBackupFile() ([]string, error) {
	file, err := os.Open(systemInfoFile)
	if err != nil {
		return nil, fmt.Errorf("打开备份文件失败: %v", err)
	}
	defer file.Close()

	var lines []string
	scanner := bufio.NewScanner(file)
	for scanner.Scan() {
		lines = append(lines, strings.TrimSpace(scanner.Text()))
	}

	if len(lines) != 4 {
		return nil, fmt.Errorf("备份文件格式错误")
	}

	return lines, scanner.Err()
}

func restoreSystemInfo(lines []string) error {
	dsn := fmt.Sprintf("root:%s@tcp(127.0.0.1:3306)/rzsj", dbPassword)
	db, err := sql.Open("mysql", dsn)
	if err != nil {
		return fmt.Errorf("连接数据库失败: %v", err)
	}
	defer db.Close()

	query := `
		UPDATE system_info 
		SET device_model = ?, 
			hard_ware_logo = ?, 
			software_version = ?, 
			software_serial_number = ? 
		WHERE id = 1
	`

	_, err = db.Exec(query, lines[0], lines[1], lines[2], lines[3])
	if err != nil {
		return fmt.Errorf("更新数据库失败: %v", err)
	}

	return nil
}
