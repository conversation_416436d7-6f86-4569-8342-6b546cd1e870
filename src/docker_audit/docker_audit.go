package main

import (
	"database/sql"
	"fmt"
	"log"
	"os"
	"time"

	"github.com/docker/docker/api/types"
	"github.com/docker/docker/api/types/container"
	"github.com/docker/docker/api/types/filters"
	"github.com/docker/docker/client"
	_ "github.com/go-sql-driver/mysql"
	"golang.org/x/net/context"
)

type ContainerAuditor struct {
	dockerClient     *client.Client
	db               *sql.DB
	targetContainers map[string]bool
}

func newContainerAuditor() (*ContainerAuditor, error) {
	// 初始化Docker客户端，指定API版本为1.43
	dockerClient, err := client.NewClientWithOpts(
		client.FromEnv,
		client.WithVersion("1.43"),
	)
	if err != nil {
		return nil, fmt.Errorf("failed to create Docker client: %v", err)
	}

	// 从环境变量读取数据库配置
	dbUser := "root"
	dbPassword := os.Getenv("MYSQL_ROOT_PASSWORD")
	dbHost := "docker-elk-mysql-1"
	dbPort := "3306"
	dbName := "rzsj"

	// 构建数据库连接字符串
	dsn := fmt.Sprintf("%s:%s@tcp(%s:%s)/%s?charset=utf8mb4&parseTime=True&loc=Local",
		dbUser, dbPassword, dbHost, dbPort, dbName,
	)

	db, err := sql.Open("mysql", dsn)
	if err != nil {
		return nil, fmt.Errorf("failed to connect database: %v", err)
	}

	// 验证数据库连接
	if err = db.Ping(); err != nil {
		return nil, fmt.Errorf("database connection test failed: %v", err)
	}

	// 定义目标容器
	targetContainers := map[string]bool{
		"docker-elk-elastalert-1":    true,
		"docker-elk-elasticsearch-1": true,
	}

	return &ContainerAuditor{
		dockerClient:     dockerClient,
		db:               db,
		targetContainers: targetContainers,
	}, nil
}

func (a *ContainerAuditor) logSystemEvent(moduleName, info string, level int) error {
	_, err := a.db.Exec(
		"INSERT INTO system_log (module_name, info, level, timestamp) VALUES (?, ?, ?, ?)",
		moduleName, info, level, time.Now(),
	)
	return err
}

// Record the initial state of existing containers
func (a *ContainerAuditor) logInitialContainerStates() error {
	ctx := context.Background()

	// 创建过滤器
	filterArgs := filters.NewArgs()
	filterArgs.Add("name", "docker-elk-elastalert-1")
	filterArgs.Add("name", "docker-elk-elasticsearch-1")

	// 列出容器
	containers, err := a.dockerClient.ContainerList(ctx, container.ListOptions{
		Filters: filterArgs,
	})
	if err != nil {
		return fmt.Errorf("failed to list containers: %v", err)
	}

	// 记录每个目标容器的初始状态
	for _, container := range containers {
		containerName := container.Names[0][1:] // Remove the leading "/"

		var moduleName string
		switch containerName {
		case "docker-elk-elastalert-1":
			moduleName = "审计告警中心"
		case "docker-elk-elasticsearch-1":
			moduleName = "数据分析器"
		default:
			continue // Skip unknown containers
		}

		// 获取容器详细信息
		containerJSON, err := a.dockerClient.ContainerInspect(ctx, container.ID)
		if err != nil {
			log.Printf("Failed to get detailed information for container %s: %v", containerName, err)
			continue
		}

		// 计算容器运行时间
		startedAt, err := time.Parse(time.RFC3339Nano, containerJSON.State.StartedAt)
		if err != nil {
			log.Printf("Failed to parse the start time of container %s: %v", containerName, err)
			continue
		}
		currentTime := time.Now()
		runningDuration := currentTime.Sub(startedAt)

		// 判断容器启动时间是否小于10秒
		var level int
		var info string
		if runningDuration.Seconds() <= 10 {
			level = 4
			info = "当前组件已启动"
		} else {
			// 如果运行时间超过10秒，我们不记录任何事件
			continue
		}

		err = a.logSystemEvent(
			moduleName,
			info,
			level,
		)
		if err != nil {
			log.Printf("Failed to record initial container state: %v", err)
		}
	}

	return nil
}

func (a *ContainerAuditor) monitorDockerEvents() error {
	// 首先记录初始容器状态
	if err := a.logInitialContainerStates(); err != nil {
		log.Printf("Failed to record initial container state: %v", err)
	}

	ctx := context.Background()
	msgChan, errChan := a.dockerClient.Events(ctx, types.EventsOptions{})

	for {
		select {
		case msg := <-msgChan:
			if msg.Type == "container" {
				containerName := msg.Actor.Attributes["name"]

				// 只处理目标容器的事件
				if a.targetContainers[containerName] {
					var moduleName string
					switch containerName {
					case "docker-elk-elastalert-1":
						moduleName = "审计告警中心"
					case "docker-elk-elasticsearch-1":
						moduleName = "数据分析器"
					default:
						continue // Skip unknown containers
					}

					switch msg.Action {
					case "start":
						err := a.logSystemEvent(
							moduleName,
							"当前组件已启动",
							4,
						)
						if err != nil {
							log.Printf("Failed to record container start event: %v", err)
						}

					case "stop":
						err := a.logSystemEvent(
							moduleName,
							"当前组件已停止",
							4,
						)
						if err != nil {
							log.Printf("Failed to record container stop event: %v", err)
						}
					}
				}
			}
		case err := <-errChan:
			log.Printf("Docker event listener error: %v", err)
			return err
		}
	}
}

func main() {
	auditor, err := newContainerAuditor()
	if err != nil {
		log.Fatalf("Failed to initialize auditor: %v", err)
	}
	defer auditor.db.Close()
	defer auditor.dockerClient.Close()

	log.Println("Starting to listen for events of specified Docker containers...")
	if err := auditor.monitorDockerEvents(); err != nil {
		log.Fatalf("Failed to listen for Docker events: %v", err)
	}
}
