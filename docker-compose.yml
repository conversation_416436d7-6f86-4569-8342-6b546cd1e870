version: '3.8'

services:
  # OpenSearch - 替代Elasticsearch
  opensearch:
    build:
      context: ./services/opensearch
      dockerfile: Dockerfile
    container_name: network-monitor-opensearch
    environment:
      - cluster.name=network-monitor-cluster
      - node.name=opensearch-node1
      - discovery.type=single-node
      - bootstrap.memory_lock=true
      - "OPEN<PERSON>ARCH_JAVA_OPTS=-Xms${OPENSEARCH_HEAP_SIZE:-1g} -Xmx${OPENSEARCH_HEAP_SIZE:-1g}"
      - OPENSEARCH_INITIAL_ADMIN_PASSWORD=${OPENSEARCH_ADMIN_PASSWORD:-admin123}
    ulimits:
      memlock:
        soft: -1
        hard: -1
      nofile:
        soft: 65536
        hard: 65536
    volumes:
      - ./services/opensearch/config/opensearch.yml:/usr/share/opensearch/config/opensearch.yml:ro
      - ./data/opensearch:/usr/share/opensearch/data
      - ./data/logs/opensearch:/usr/share/opensearch/logs
    ports:
      - "${OP<PERSON><PERSON>ARCH_PORT:-9200}:9200"
      - "${OP<PERSON><PERSON>ARCH_PERF_PORT:-9600}:9600"
    networks:
      - network-monitor
    healthcheck:
      test: ["CMD-SHELL", "curl -s http://localhost:9200/_cluster/health >/dev/null || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 5
    restart: unless-stopped

  # MariaDB - 配置数据存储
  mariadb:
    image: mariadb:${MARIADB_VERSION:-10.11}
    container_name: network-monitor-mariadb
    environment:
      - MYSQL_ROOT_PASSWORD=${MYSQL_ROOT_PASSWORD}
      - MYSQL_DATABASE=${MYSQL_DATABASE:-network_monitor}
      - MYSQL_USER=${MYSQL_USER:-monitor_user}
      - MYSQL_PASSWORD=${MYSQL_PASSWORD}
    volumes:
      - ./services/mariadb/config/my.cnf:/etc/mysql/my.cnf:ro
      - ./services/mariadb/init:/docker-entrypoint-initdb.d:ro
      - ./data/mariadb:/var/lib/mysql
      - ./data/logs/mariadb:/var/log/mysql
    ports:
      - "${MYSQL_PORT:-3306}:3306"
    networks:
      - network-monitor
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "root", "-p${MYSQL_ROOT_PASSWORD}"]
      interval: 30s
      timeout: 10s
      retries: 5
    restart: unless-stopped

  # Zookeeper - Kafka依赖
  zookeeper:
    image: confluentinc/cp-zookeeper:${KAFKA_VERSION:-7.4.0}
    container_name: network-monitor-zookeeper
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000
    volumes:
      - ./data/kafka/zookeeper-data:/var/lib/zookeeper/data
      - ./data/kafka/zookeeper-logs:/var/lib/zookeeper/log
    networks:
      - network-monitor
    restart: unless-stopped

  # Kafka - 消息队列
  kafka:
    image: confluentinc/cp-kafka:${KAFKA_VERSION:-7.4.0}
    container_name: network-monitor-kafka
    depends_on:
      - zookeeper
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://kafka:9092
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
      KAFKA_AUTO_CREATE_TOPICS_ENABLE: "true"
      KAFKA_LOG_RETENTION_HOURS: 168
      KAFKA_LOG_SEGMENT_BYTES: **********
    volumes:
      - ./data/kafka/kafka-data:/var/lib/kafka/data
      - ./data/logs/kafka:/var/log/kafka
    ports:
      - "${KAFKA_PORT:-9092}:9092"
    networks:
      - network-monitor
    healthcheck:
      test: ["CMD", "kafka-broker-api-versions", "--bootstrap-server", "localhost:9092"]
      interval: 30s
      timeout: 10s
      retries: 5
    restart: unless-stopped

  # Logstash - 日志采集和数据库采集
  logstash:
    build:
      context: ./services/logstash
      dockerfile: Dockerfile
    container_name: network-monitor-logstash
    depends_on:
      opensearch:
        condition: service_healthy
      kafka:
        condition: service_healthy
    environment:
      - "LS_JAVA_OPTS=-Xms${LOGSTASH_HEAP_SIZE:-1g} -Xmx${LOGSTASH_HEAP_SIZE:-1g}"
      - OPENSEARCH_HOSTS=http://opensearch:9200
      - OPENSEARCH_USERNAME=${OPENSEARCH_USERNAME:-admin}
      - OPENSEARCH_PASSWORD=${OPENSEARCH_ADMIN_PASSWORD:-admin123}
    volumes:
      - ./services/logstash/config:/usr/share/logstash/config:ro
      - ./services/logstash/pipeline:/usr/share/logstash/pipeline:ro
      - ./services/logstash/patterns:/usr/share/logstash/patterns:ro
      - ./services/logstash/templates:/usr/share/logstash/templates:ro
      - ./services/logstash/jdbc:/usr/share/logstash/jdbc:ro
      - ./data/logstash:/usr/share/logstash/data
      - ./data/logs/logstash:/usr/share/logstash/logs
    ports:
      - "${LOGSTASH_BEATS_PORT:-5044}:5044"
      - "${LOGSTASH_SYSLOG_PORT:-5514}:5514/udp"
      - "${LOGSTASH_SNMP_TRAP_PORT:-162}:162/udp"
      - "${LOGSTASH_API_PORT:-9600}:9600"
    networks:
      - network-monitor
    restart: unless-stopped

networks:
  network-monitor:
    driver: bridge
    name: network_monitor_network

volumes:
  opensearch-data:
    driver: local
  mariadb-data:
    driver: local
  kafka-data:
    driver: local
