# python version 1.0						DO NOT EDIT
#
# Generated by smidump version 0.4.8:
#
#   smidump -f python StationSW-MIB

FILENAME = "/home/<USER>/audit_log/docker-elk/logstash/mibs/H3CPublicMIB/station-sw.mib"

MIB = {
    "moduleName" : "StationSW-MIB",

    "StationSW-MIB" : {
        "nodetype" : "module",
        "language" : "SMIv2",
        "organization" :    
            """NARI Group Co., Ltd""",
        "contact" : 
            """Add: NO.19 Chengxin Avenue, Nanjing, CHINA
Postalcode: 211106""",
        "description" :
            """Items witch are associated with StationSW project, include some definitions
and traps """,
        "revisions" : (
            {
                "date" : "2017-11-22 00:00",
                "description" :
                    """[Revision added by libsmi due to a LAST-UPDATED clause.]""",
            },
        ),
        "identity node" : "nari",
    },

    "imports" : (
        {"module" : "SNMPv2-SMI", "name" : "enterprises"},
        {"module" : "SNMPv2-SMI", "name" : "Integer32"},
        {"module" : "SNMPv2-SMI", "name" : "OBJECT-TYPE"},
        {"module" : "SNMPv2-SMI", "name" : "MODULE-IDENTITY"},
        {"module" : "SNMPv2-SMI", "name" : "NOTIFICATION-TYPE"},
        {"module" : "SNMPv2-TC", "name" : "DisplayString"},
        {"module" : "SNMPv2-TC", "name" : "TEXTUAL-CONVENTION"},
    ),

    "typedefs" : {
        "UserTypeEnum" : {
            "basetype" : "Enumeration",
            "status" : "current",
            "http" : {
                "nodetype" : "namednumber",
                "number" : "1"
            },
            "https" : {
                "nodetype" : "namednumber",
                "number" : "2"
            },
            "snmp" : {
                "nodetype" : "namednumber",
                "number" : "3"
            },
            "telnet" : {
                "nodetype" : "namednumber",
                "number" : "4"
            },
            "ssh" : {
                "nodetype" : "namednumber",
                "number" : "5"
            },
            "cli" : {
                "nodetype" : "namednumber",
                "number" : "6"
            },
            "iec61850" : {
                "nodetype" : "namednumber",
                "number" : "7"
            },
            "console" : {
                "nodetype" : "namednumber",
                "number" : "8"
            },
            "description" :
                """Device user type.""",
        },
        "UserStatusEnum" : {
            "basetype" : "Enumeration",
            "status" : "current",
            "logIn" : {
                "nodetype" : "namednumber",
                "number" : "1"
            },
            "logOut" : {
                "nodetype" : "namednumber",
                "number" : "2"
            },
            "changePassword" : {
                "nodetype" : "namednumber",
                "number" : "3"
            },
            "loginFail" : {
                "nodetype" : "namednumber",
                "number" : "4"
            },
            "description" :
                """Device user status.""",
        },
    }, # typedefs

    "nodes" : {
        "nari" : {
            "nodetype" : "node",
            "moduleName" : "StationSW-MIB",
            "oid" : "*******.4.1.49763",
            "status" : "current",
        }, # node
        "stationSW" : {
            "nodetype" : "node",
            "moduleName" : "StationSW-MIB",
            "oid" : "*******.4.1.49763.1",
        }, # node
        "stationSWMgmt" : {
            "nodetype" : "node",
            "moduleName" : "StationSW-MIB",
            "oid" : "*******.4.1.49763.1.1",
        }, # node
        "ucMacChange" : {
            "nodetype" : "scalar",
            "moduleName" : "StationSW-MIB",
            "oid" : "*******.4.1.49763.1.1.1",
            "status" : "current",
            "syntax" : {
                "type" :                 {
                    "basetype" : "OctetString",
                    "parent module" : {
                        "name" : "SNMPv2-TC",
                        "type" : "DisplayString",
                    },
                    "ranges" : [
                    {
                        "min" : "0",
                        "max" : "512"
                    },
                    ],
                    "range" : {
                        "min" : "0",
                        "max" : "512"
                    },
                },
            },
            "access" : "notifyonly",
            "description" :
                """Describe unicaset mac address change.
The format is as follows:
port_id<space>macAddress1&macAddress2&...<space>vlan_id1
<space>macAddress3&macAddress4&...<space>vlan_id2...""",
        }, # scalar
        "userTable" : {
            "nodetype" : "table",
            "moduleName" : "StationSW-MIB",
            "oid" : "*******.4.1.49763.1.1.2",
            "status" : "current",
            "description" :
                """Describe all user status.""",
        }, # table
        "userEntry" : {
            "nodetype" : "row",
            "moduleName" : "StationSW-MIB",
            "oid" : "*******.4.1.49763.*******",
            "status" : "current",
            "linkage" : [
                "userIndex",
            ],
            "description" :
                """Table entry for userTable.""",
        }, # row
        "userIndex" : {
            "nodetype" : "column",
            "moduleName" : "StationSW-MIB",
            "oid" : "*******.4.1.49763.*******.1",
            "status" : "current",
            "syntax" : {
                "type" :                 {
                    "basetype" : "Integer32",
                    "ranges" : [
                    {
                        "min" : "1",
                        "max" : "2147483647"
                    },
                    ],
                    "range" : {
                        "min" : "1",
                        "max" : "2147483647"
                    },
                },
            },
            "access" : "notifyonly",
            "description" :
                """Index of userTable.""",
        }, # column
        "userName" : {
            "nodetype" : "column",
            "moduleName" : "StationSW-MIB",
            "oid" : "*******.4.1.49763.*******.2",
            "status" : "current",
            "syntax" : {
                "type" :                 {
                    "basetype" : "OctetString",
                    "parent module" : {
                        "name" : "SNMPv2-TC",
                        "type" : "DisplayString",
                    },
                    "ranges" : [
                    {
                        "min" : "0",
                        "max" : "64"
                    },
                    ],
                    "range" : {
                        "min" : "0",
                        "max" : "64"
                    },
                },
            },
            "access" : "notifyonly",
            "description" :
                """Device user name.""",
        }, # column
        "userType" : {
            "nodetype" : "column",
            "moduleName" : "StationSW-MIB",
            "oid" : "*******.4.1.49763.*******.3",
            "status" : "current",
            "syntax" : {
                "type" : { "module" :"StationSW-MIB", "name" : "UserTypeEnum"},
            },
            "access" : "notifyonly",
            "description" :
                """Device user type.""",
        }, # column
        "userStatus" : {
            "nodetype" : "column",
            "moduleName" : "StationSW-MIB",
            "oid" : "*******.4.1.49763.*******.4",
            "status" : "current",
            "syntax" : {
                "type" : { "module" :"StationSW-MIB", "name" : "UserStatusEnum"},
            },
            "access" : "notifyonly",
            "description" :
                """Device user type.""",
        }, # column
        "userModified" : {
            "nodetype" : "column",
            "moduleName" : "StationSW-MIB",
            "oid" : "*******.4.1.49763.*******.5",
            "status" : "current",
            "syntax" : {
                "type" :                 {
                    "basetype" : "OctetString",
                    "parent module" : {
                        "name" : "SNMPv2-TC",
                        "type" : "DisplayString",
                    },
                    "ranges" : [
                    {
                        "min" : "0",
                        "max" : "64"
                    },
                    ],
                    "range" : {
                        "min" : "0",
                        "max" : "64"
                    },
                },
            },
            "access" : "notifyonly",
            "description" :
                """changePassword: Username who's password was modified.
other operation: None.""",
        }, # column
        "userIP" : {
            "nodetype" : "column",
            "moduleName" : "StationSW-MIB",
            "oid" : "*******.4.1.49763.*******.6",
            "status" : "current",
            "syntax" : {
                "type" :                 {
                    "basetype" : "OctetString",
                    "parent module" : {
                        "name" : "SNMPv2-TC",
                        "type" : "DisplayString",
                    },
                    "ranges" : [
                    {
                        "min" : "0",
                        "max" : "64"
                    },
                    ],
                    "range" : {
                        "min" : "0",
                        "max" : "64"
                    },
                },
            },
            "access" : "notifyonly",
            "description" :
                """Device user IP. If no IP, use 0.0.0.0.""",
        }, # column
        "userOperTable" : {
            "nodetype" : "table",
            "moduleName" : "StationSW-MIB",
            "oid" : "*******.4.1.49763.1.1.3",
            "status" : "current",
            "description" :
                """Describe user operate command.""",
        }, # table
        "userOperEntry" : {
            "nodetype" : "row",
            "moduleName" : "StationSW-MIB",
            "oid" : "*******.4.1.49763.*******",
            "status" : "current",
            "linkage" : [
                "userOperIndex",
            ],
            "description" :
                """Table entity for userOperTable.""",
        }, # row
        "userOperIndex" : {
            "nodetype" : "column",
            "moduleName" : "StationSW-MIB",
            "oid" : "*******.4.1.49763.*******.1",
            "status" : "current",
            "syntax" : {
                "type" :                 {
                    "basetype" : "Integer32",
                    "ranges" : [
                    {
                        "min" : "1",
                        "max" : "2147483647"
                    },
                    ],
                    "range" : {
                        "min" : "1",
                        "max" : "2147483647"
                    },
                },
            },
            "access" : "notifyonly",
            "description" :
                """Index of userOperTable.""",
        }, # column
        "userOperName" : {
            "nodetype" : "column",
            "moduleName" : "StationSW-MIB",
            "oid" : "*******.4.1.49763.*******.2",
            "status" : "current",
            "syntax" : {
                "type" :                 {
                    "basetype" : "OctetString",
                    "parent module" : {
                        "name" : "SNMPv2-TC",
                        "type" : "DisplayString",
                    },
                    "ranges" : [
                    {
                        "min" : "0",
                        "max" : "64"
                    },
                    ],
                    "range" : {
                        "min" : "0",
                        "max" : "64"
                    },
                },
            },
            "access" : "notifyonly",
            "description" :
                """Device user name.""",
        }, # column
        "userOperIP" : {
            "nodetype" : "column",
            "moduleName" : "StationSW-MIB",
            "oid" : "*******.4.1.49763.*******.3",
            "status" : "current",
            "syntax" : {
                "type" :                 {
                    "basetype" : "OctetString",
                    "parent module" : {
                        "name" : "SNMPv2-TC",
                        "type" : "DisplayString",
                    },
                    "ranges" : [
                    {
                        "min" : "0",
                        "max" : "64"
                    },
                    ],
                    "range" : {
                        "min" : "0",
                        "max" : "64"
                    },
                },
            },
            "access" : "notifyonly",
            "description" :
                """Device user IP. If no IP, use 0.0.0.0.""",
        }, # column
        "userOperCommand" : {
            "nodetype" : "column",
            "moduleName" : "StationSW-MIB",
            "oid" : "*******.4.1.49763.*******.4",
            "status" : "current",
            "syntax" : {
                "type" :                 {
                    "basetype" : "OctetString",
                    "parent module" : {
                        "name" : "SNMPv2-TC",
                        "type" : "DisplayString",
                    },
                    "ranges" : [
                    {
                        "min" : "0",
                        "max" : "256"
                    },
                    ],
                    "range" : {
                        "min" : "0",
                        "max" : "256"
                    },
                },
            },
            "access" : "notifyonly",
            "description" :
                """Device user Command.""",
        }, # column
        "stationTrap" : {
            "nodetype" : "node",
            "moduleName" : "StationSW-MIB",
            "oid" : "*******.4.1.49763.1.2",
        }, # node
    }, # nodes

    "notifications" : {
        "ucMacChangeTrap" : {
            "nodetype" : "notification",
            "moduleName" : "StationSW-MIB",
            "oid" : "*******.4.1.49763.1.2.1",
            "status" : "current",
            "objects" : {
                "ucMacChange" : {
                    "nodetype" : "object",
                    "module" : "StationSW-MIB"
                },
            },
            "description" :
                """Traps of unicaset mac address change.""",
        }, # notification
        "userTrap" : {
            "nodetype" : "notification",
            "moduleName" : "StationSW-MIB",
            "oid" : "*******.4.1.49763.1.2.2",
            "status" : "current",
            "objects" : {
                "userIndex" : {
                    "nodetype" : "object",
                    "module" : "StationSW-MIB"
                },
                "userName" : {
                    "nodetype" : "object",
                    "module" : "StationSW-MIB"
                },
                "userType" : {
                    "nodetype" : "object",
                    "module" : "StationSW-MIB"
                },
                "userStatus" : {
                    "nodetype" : "object",
                    "module" : "StationSW-MIB"
                },
                "userModified" : {
                    "nodetype" : "object",
                    "module" : "StationSW-MIB"
                },
                "userIP" : {
                    "nodetype" : "object",
                    "module" : "StationSW-MIB"
                },
            },
            "description" :
                """Traps of user operation.""",
        }, # notification
        "userOperTrap" : {
            "nodetype" : "notification",
            "moduleName" : "StationSW-MIB",
            "oid" : "*******.4.1.49763.1.2.3",
            "status" : "current",
            "objects" : {
                "userOperIndex" : {
                    "nodetype" : "object",
                    "module" : "StationSW-MIB"
                },
                "userOperName" : {
                    "nodetype" : "object",
                    "module" : "StationSW-MIB"
                },
                "userOperIP" : {
                    "nodetype" : "object",
                    "module" : "StationSW-MIB"
                },
                "userOperCommand" : {
                    "nodetype" : "object",
                    "module" : "StationSW-MIB"
                },
            },
            "description" :
                """Traps of user operate command.""",
        }, # notification
    }, # notifications

}
