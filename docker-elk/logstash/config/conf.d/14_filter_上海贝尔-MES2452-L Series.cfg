filter {
  ruby {
    path => "/usr/share/logstash/config/conf.d/ruby/event_processor.rb"
    script_params => {
      "patterns" => [
        {
          "regex" => 'User\s+(?<user>\S+)\s+logged\s+in\s+from\s+(?<srcIp>\S+).*?$'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => ["user", "srcIp"]
        },
        {
          "regex" => 'User\s+(?<user>\S+)\s+logouted\s+from\s+(?<srcIp>\S+).*$'
          "event_name" => "退出登录"
          "event_type" => "1000068"
          "event_subtype" => "1000000201"
          "extract_fields" => ["user", "srcIp"]
        }
      ]
    }
  }

  # 添加通用字段
  mutate {
    replace => {
      "device_type" => "1000079"
      "product_name" => "上海贝尔-MES2452-L Series"
    }
  }

  # 保存提取字段
  mutate {
    rename => {
      "user" => "related_object_name"
      "srcIp" => "source_address"
    }
  }
}
