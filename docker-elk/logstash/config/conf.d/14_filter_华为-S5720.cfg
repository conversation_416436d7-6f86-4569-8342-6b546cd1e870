filter {
  ruby {
    path => "/usr/share/logstash/config/conf.d/ruby/event_processor.rb"
    script_params => {
      "patterns" => [
        {
          "regex" => ''
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => []
        },
        {
          "regex" => 'LOGIN.*?succeeded\s+in\s+logging\s+in\s+to\s+\S+?\.\s\(UserType=(?<LoginType>.*?)\,\sUserName=(?<username>.*?),(\sAuthenticationMethod=\".*?\"\,|)\sIp=(?<srcIp>\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})\,\sVpnName=.*\).*$'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => ["LoginType", "username", "srcIp"]
        },
        {
          "regex" => 'LOGIN.*?succeeded\s+in\s+logging\s+in\s+to\s+\S+?\.\s\(UserType=(?<LoginType>.*?)\,\s+UserName=(?<username>.*?).*Ip=(?<srcIp>\d{1,3}\S+),.*$'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => ["LoginType", "username", "srcIp"]
        },
        {
          "regex" => 'LOGIN.*?succeeded\s+in\s+logging\s+in\s+to\s+\S+?\.\s+\(UserType=(?<LoginType>\S+),\s+UserName=(?<username>\S+),.*Ip=(?<srcIp>\S+),.*$'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => ["LoginType", "username", "srcIp"]
        },
        {
          "regex" => 'user\s+login.+\S+.*\s+UserName=+(?<username>.+)\S+\s+UserIP=+(?<ipaddress>.+),.*$'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => ["username", "ipaddress"]
        },
        {
          "regex" => 'FAIL.*?Failed\s+to\s+login\s+through\s+(?<LoginType>.+?)\.\s+\(IP=(?<srcIp>\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})\,\s+VpnInstanceName=.*?\,\s+UserName=(?<username>.*)\,\s+Times=\d+?\,\s+FailedReason=.*?\).*$'
          "event_name" => "登录失败"
          "event_type" => "1000061"
          "event_subtype" => "1000000140"
          "extract_fields" => ["LoginType", "srcIp", "username"]
        },
        {
          "regex" => '%%.*Failed\sto\slogin\sthrough\s+(?<type>\S+)\s+.*Ip=(?<srcIp>\S+),.*$'
          "event_name" => "登录失败"
          "event_type" => "1000061"
          "event_subtype" => "1000000140"
          "extract_fields" => ["type", "srcIp"]
        },
        {
          "regex" => '%%.*LOGOUT.*succeeded\sin\slogging\sout\s.*UserType=(?<type>\S+),\s+UserName=(?<user>\S+),\sIp=(?<srcIp>\S+),.*$'
          "event_name" => "退出登录"
          "event_type" => "1000068"
          "event_subtype" => "1000000201"
          "extract_fields" => ["type", "user", "srcIp"]
        },
        {
          "regex" => '%%.*DISPLAY_CMDRECORD.*Ip=(?<DeviceIp>\S+),\s.*User=(?<Username>\S+),.*\sCommand="(?<Command>.*)"\).*$'
          "event_name" => "配置变更"
          "event_type" => "1000026"
          "event_subtype" => "1000000094"
          "extract_fields" => ["DeviceIp", "Username", "Command"]
        },
        {
          "regex" => 'IFNET.*LINKDOWN:.*Interface\s+(?<interface>\S+)\s+turned\s+into\s+DOWN.*$'
          "event_name" => "网口拔出"
          "event_type" => "1000065"
          "event_subtype" => "1000000215"
          "extract_fields" => ["interface"]
        },
        {
          "regex" => 'IFNET.*LINKDOWN:.*Interface\s(?<interface>\S+)\sturned\sinto\sDOWN.*$'
          "event_name" => "网口拔出"
          "event_type" => "1000065"
          "event_subtype" => "1000000215"
          "extract_fields" => ["interface"]
        },
        {
          "regex" => 'IF_STATE.*?Interface\s(?<interface>\S+?)\shas\sturned\sinto\sDOWN\sstate.*$'
          "event_name" => "网口拔出"
          "event_type" => "1000065"
          "event_subtype" => "1000000215"
          "extract_fields" => ["interface"]
        },
        {
          "regex" => 'IFNET.*LINKUP:.*Interface\s+(?<interface>\S+)\s+turned\s+into\s+UP.*$'
          "event_name" => "网口接入"
          "event_type" => "1000065"
          "event_subtype" => "1000000214"
          "extract_fields" => ["interface"]
        },
        {
          "regex" => 'IFNET.*LINKUP:.*Interface\s(?<interface>\S+)\sturned\sinto\sUP.*$'
          "event_name" => "网口接入"
          "event_type" => "1000065"
          "event_subtype" => "1000000214"
          "extract_fields" => ["interface"]
        },
        {
          "regex" => 'IF_STATE.*?Interface\s(?<interface>\S+?)\shas\sturned\sinto\sUP\sstate.*$'
          "event_name" => "网口接入"
          "event_type" => "1000065"
          "event_subtype" => "1000000214"
          "extract_fields" => ["interface"]
        }
      ]
    }
  }

  # 添加通用字段
  mutate {
    replace => {
      "device_type" => "1000079"
      "product_name" => "华为-S5720"
    }
  }

  # 保存提取字段
  mutate {
    rename => {
      "username" => "related_object_name"
      "Command" => "action_details"
      "srcIp" => "source_address"
      "DeviceIp" => "device_name"
      "user" => "related_object_name"
      "interface" => "action_object_name"
      "Username" => "related_object_name"
    }
  }
}
