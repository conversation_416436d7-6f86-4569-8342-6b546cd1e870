filter {
  ruby {
    path => "/usr/share/logstash/config/conf.d/ruby/event_processor.rb"
    script_params => {
      "patterns" => [
        {
          "regex" => 'user\((?<user>\S+)\)\s+try\s+to\s+login\s+from\s+(?<type>\S+)\s+(?<srcIp>\S+),\s+verified\s+failed.*$'
          "event_name" => "登录失败"
          "event_type" => "1000061"
          "event_subtype" => "1000000140"
          "extract_fields" => ["user", "srcIp", "type"]
        },
        {
          "regex" => 'USER\((?<user>\S+)\):(?<srcIp>\S+)\s+exit\s+from\s+(?<type>\S+).*?$'
          "event_name" => "退出登录"
          "event_type" => "1000068"
          "event_subtype" => "1000000201"
          "extract_fields" => ["user", "srcIp", "type"]
        }
      ]
    }
  }

  # 添加通用字段
  mutate {
    replace => {
      "device_type" => "1000079"
      "product_name" => "南瑞继保-PCS-9882"
    }
  }

  # 保存提取字段
  mutate {
    rename => {
      "user" => "related_object_name"
      "srcIp" => "source_address"
    }
  }
}
