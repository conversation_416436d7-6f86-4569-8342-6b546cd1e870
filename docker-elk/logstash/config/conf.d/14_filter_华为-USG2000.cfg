filter {
  ruby {
    path => "/usr/share/logstash/config/conf.d/ruby/event_processor.rb"
    script_params => {
      "patterns" => [
        {
          "regex" => 'USG2160.*?\s+(?<lgType>\w+)/\d+/LOGIN:\s+(?<Username>\S+).*?$'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => ["lgType", "Username"]
        },
        {
          "regex" => 'USG2160-B\s+(?<lgType>\w+)/\d+/PASS:User\s+(?<Username>\S+)\(IP:(?<srcIp>\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3})\s+ID\S+\)\s+login\s+succeeded$'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => ["lgType", "Username", "srcIp"]
        },
        {
          "regex" => 'Received\s+connection\s+from\s+(?<srcIp>\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3})..*$'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => ["srcIp"]
        },
        {
          "regex" => 'vrf.*user:(?<user>\S+)\s+login\s+from(?<srcIp>.*)..*$'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => ["user", "srcIp"]
        },
        {
          "regex" => 'Failed\s+to\s+login\s+through.*(?i)IP=(?<srcIp>\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3}),.*UserName=(?<user>\S+),.*$'
          "event_name" => "登录失败"
          "event_type" => "1000061"
          "event_subtype" => "1000000140"
          "extract_fields" => ["srcIp", "user"]
        },
        {
          "regex" => '^(?i)user\s+login\s+failed\s+from\s+(?<srcIp>.*)\(.*$'
          "event_name" => "登录失败"
          "event_type" => "1000061"
          "event_subtype" => "1000000140"
          "extract_fields" => ["srcIp"]
        },
        {
          "regex" => 'USG2160.*?\s+(?<lgType>\w+)/\d+/LOGOUT:\s+(?<Username>\S+).*?$'
          "event_name" => "退出登录"
          "event_type" => "1000068"
          "event_subtype" => "1000000201"
          "extract_fields" => ["lgType", "Username"]
        },
        {
          "regex" => 'USG2160.*?\s+(?<lgType>\w+)/\d+/OUT:User\s+(?<Username>\S+)\(IP:(?<srcIp>\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})\s+ID\S+\)\s+logout$'
          "event_name" => "退出登录"
          "event_type" => "1000068"
          "event_subtype" => "1000000201"
          "extract_fields" => ["lgType", "Username", "srcIp"]
        },
        {
          "regex" => 'vrf.*user:(?<user>\S+)\s+logout\s+from(?<srcIp>.*)..*$'
          "event_name" => "退出登录"
          "event_type" => "1000068"
          "event_subtype" => "1000000201"
          "extract_fields" => ["user", "srcIp"]
        },
        {
          "regex" => '^task:co0\s+ip:(?<srcIp>.*)\s+user:(?<user>\S+)\s+vrf:\S+\s+command:(?<Command>.*)..*$'
          "event_name" => "配置变更"
          "event_type" => "1000026"
          "event_subtype" => "1000000094"
          "extract_fields" => ["srcIp", "user", "Command"]
        },
        {
          "regex" => 'FILTER\/\d+\/ACLDENY.*?source-ip=(?<srcIp>\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}).*\s+source-port=(?<srcPort>\d+).*\s+destination-ip=(?<dstIp>\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}).*\s+destination-port=(?<dstPort>\d+).*?$'
          "event_name" => "不符合安全策略访问"
          "event_type" => "1000065"
          "event_subtype" => "1000000241"
          "extract_fields" => ["srcIp", "srcPort", "dstIp", "dstPort"]
        },
        {
          "regex" => 'protocol.*sourceip=(?<srcIp>\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3});\s+source-port=(?<srcPort>\d+);\s+destination-ip=(?<dstIp>\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3});\s+destination-port=(?<dstPort>\d+).*'
          "event_name" => "不符合安全策略访问"
          "event_type" => "1000065"
          "event_subtype" => "1000000241"
          "extract_fields" => ["srcIp", "srcPort", "dstIp", "dstPort"]
        },
        {
          "regex" => 'protocol=\d+;\s+sourceip=(?<srcIp>\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3});\s+source-port=(?<srcPort>\d+);\s+destination-ip=(?<dstIp>\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3});\s+destination-port=(?<dstPort>\d+);.*$'
          "event_name" => "符合安全策略访问"
          "event_type" => "1000065"
          "event_subtype" => "1000000241"
          "extract_fields" => ["srcIp", "srcPort", "dstIp", "dstPort"]
        },
        {
          "regex" => '(?<interface>\S+)\s+changed\s+status\s+to\s+down.*$'
          "event_name" => "网口拔出"
          "event_type" => "1000065"
          "event_subtype" => "1000000215"
          "extract_fields" => ["interface"]
        },
        {
          "regex" => 'IFNET.*interface\s+(?<interface>\S+)\s+turns\s+into\s+DOWN\s+state.*$'
          "event_name" => "网口拔出"
          "event_type" => "1000065"
          "event_subtype" => "1000000215"
          "extract_fields" => ["interface"]
        },
        {
          "regex" => 'PHY:\s+(?<interface>\S+):\s+change\s+status\s+to\s+down.*$'
          "event_name" => "网口拔出"
          "event_type" => "1000065"
          "event_subtype" => "1000000215"
          "extract_fields" => ["interface"]
        },
        {
          "regex" => 'Interface\s(?<interface>\S+?)\shas\sturned\sinto\sDOWN\sstate.*$'
          "event_name" => "网口拔出"
          "event_type" => "1000065"
          "event_subtype" => "1000000215"
          "extract_fields" => ["interface"]
        },
        {
          "regex" => 'CGYGZJ-FSZ-NAT-USG2160-B IFNET\/4\/TRAP:1\.3\.6\.1\.6\.3\.1\.1\.5\.4'
          "event_name" => "网口接入"
          "event_type" => "1000065"
          "event_subtype" => "1000000214"
          "extract_fields" => []
        },
        {
          "regex" => '^(?<interface>\S+)\s+changed\s+status\s+to\s+up.*$'
          "event_name" => "网口接入"
          "event_type" => "1000065"
          "event_subtype" => "1000000214"
          "extract_fields" => ["interface"]
        },
        {
          "regex" => 'IFNET.*interface\s+(?<interface>\S+)\s+turns\s+into\s+UP\s+state.*$'
          "event_name" => "网口接入"
          "event_type" => "1000065"
          "event_subtype" => "1000000214"
          "extract_fields" => ["interface"]
        },
        {
          "regex" => 'PHY:\s+(?<interface>\S+):\s+change\s+status\s+to\s+up.*$'
          "event_name" => "网口接入"
          "event_type" => "1000065"
          "event_subtype" => "1000000214"
          "extract_fields" => ["interface"]
        },
        {
          "regex" => 'Interface\s(?<interface>\S+?)\shas\sturned\sinto\sUP\sstate.*$'
          "event_name" => "网口接入"
          "event_type" => "1000065"
          "event_subtype" => "1000000214"
          "extract_fields" => ["interface"]
        }
      ]
    }
  }

  # 添加通用字段
  mutate {
    replace => {
      "device_type" => "1000035"
      "product_name" => "华为-USG2000"
    }
  }

  # 保存提取字段
  mutate {
    rename => {
      "dstIp" => "dest_address"
      "Command" => "action_details"
      "dstPort" => "dest_port"
      "srcIp" => "source_address"
      "interface" => "action_object_name"
      "user" => "related_object_name"
      "srcPort" => "source_port"
      "Username" => "related_object_name"
    }
  }
}
