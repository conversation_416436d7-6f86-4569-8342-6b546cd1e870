filter {
  ruby {
    path => "/usr/share/logstash/config/conf.d/ruby/event_processor.rb"
    script_params => {
      "patterns" => [
        {
          "regex" => ':\s+Login\s+Success\s+\[user:\s+(?<user>\S+)\]\s+\[Source:\s+(?<srcIp>\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3})\].*$'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => ["user", "srcIp"]
        },
        {
          "regex" => 'Login\s+Success\s+\[user:\s+(?<user>\S+)\]\s+\[Source:\s+(?<srcIp>\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})\].*$'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => ["user", "srcIp"]
        },
        {
          "regex" => '^%SEC_LOGIN.*watching failures.*user:\s+(?<user>\S+)\].*Source:\s+(?<srcIp>\S+)\].*Login\s+Authentication\s+Failed.*$'
          "event_name" => "登录失败"
          "event_type" => "1000061"
          "event_subtype" => "1000000140"
          "extract_fields" => ["user", "srcIp"]
        },
        {
          "regex" => '^%.*\sLogin\sfailed\s\[user:(?<user>.*)\]\s\[Source:\s(?<srcIp>\S+)\]\s\[localport:\s(?<port>\d+)\].*$'
          "event_name" => "登录失败"
          "event_type" => "1000061"
          "event_subtype" => "1000000140"
          "extract_fields" => ["user", "srcIp", "port"]
        },
        {
          "regex" => '^%.*:\s+Configured\s+from.*by\s+(?<user>\S+).*\((?<srcIp>\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3}).*$'
          "event_name" => "退出登录"
          "event_type" => "1000068"
          "event_subtype" => "1000000201"
          "extract_fields" => ["user", "srcIp"]
        },
        {
          "regex" => '^%.*Interface\s(?<interface>\S+),\s+changed\sstate\sto\sdown.*$'
          "event_name" => "网口拔出"
          "event_type" => "1000065"
          "event_subtype" => "1000000215"
          "extract_fields" => ["interface"]
        },
        {
          "regex" => '%.*Interface\s(?<interface>\S+),\s+changed\sstate\sto\sup.*$'
          "event_name" => "网口接入"
          "event_type" => "1000065"
          "event_subtype" => "1000000214"
          "extract_fields" => ["interface"]
        }
      ]
    }
  }

  # 添加通用字段
  mutate {
    replace => {
      "device_type" => "1000079"
      "product_name" => "思科-4500 L3"
    }
  }

  # 保存提取字段
  mutate {
    rename => {
      "port" => "source_port"
      "srcIp" => "source_address"
      "interface" => "action_object_name"
      "user" => "related_object_name"
    }
  }
}
