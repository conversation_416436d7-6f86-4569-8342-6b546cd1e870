filter {
  ruby {
    path => "/usr/share/logstash/config/conf.d/ruby/event_processor.rb"
    script_params => {
      "patterns" => [
        {
          "regex" => 'SHELL_LOGIN: (?<Username>\S+?) logged in from (?<DeviceIp>\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}).*$'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => ["Username", "DeviceIp"]
        },
        {
          "regex" => 'SSHS_LOG: Accepted password for (?<Username>\S+?) from (?<DeviceIp>\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}).*$'
          "event_name" => "登录失败"
          "event_type" => "1000061"
          "event_subtype" => "1000000140"
          "extract_fields" => ["Username", "DeviceIp"]
        },
        {
          "regex" => 'SHELL_LOGOUT: (?<Username>\S+?) logged out from (?<DeviceIp>\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}).*$'
          "event_name" => "退出登录"
          "event_type" => "1000068"
          "event_subtype" => "1000000201"
          "extract_fields" => ["Username", "DeviceIp"]
        }
      ]
    }
  }

  # 添加通用字段
  mutate {
    replace => {
      "device_type" => "1000079"
      "product_name" => "华三-S5130-30C-HI"
    }
  }

  # 保存提取字段
  mutate {
    rename => {
      "DeviceIp" => "device_name"
      "Username" => "related_object_name"
    }
  }
}
