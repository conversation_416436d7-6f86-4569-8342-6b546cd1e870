filter {
  ruby {
    path => "/usr/share/logstash/config/conf.d/ruby/event_processor.rb"
    script_params => {
      "patterns" => [
        {
          "regex" => '^0\s+1\s+(?<user>\S+).*'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => ["user"]
        },
        {
          "regex" => 'VEAD\s0\s1\s(?<user>.*)$'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => ["user"]
        },
        {
          "regex" => 'VEAD\s0\s2\s(?<user>.*)$'
          "event_name" => "登录失败"
          "event_type" => "1000061"
          "event_subtype" => "1000000140"
          "extract_fields" => ["user"]
        },
        {
          "regex" => '^\s*0\s+2\s+user\s+logout!.*'
          "event_name" => "退出登录"
          "event_type" => "1000068"
          "event_subtype" => "1000000201"
          "extract_fields" => []
        },
        {
          "regex" => '^0\s+4\s+(?<user>\S+).*$'
          "event_name" => "退出登录"
          "event_type" => "1000068"
          "event_subtype" => "1000000201"
          "extract_fields" => ["user"]
        },
        {
          "regex" => 'VEAD\s0\s4\s(?<user>.*)$'
          "event_name" => "退出登录"
          "event_type" => "1000068"
          "event_subtype" => "1000000201"
          "extract_fields" => ["user"]
        },
        {
          "regex" => '^VEAD\s2\s7\s8\s+(?<src_ip>.+)\s+(?<src_port>.+)\s+(?<dest_ip>.+)\s+(?<dest_port>.+).*$'
          "event_name" => "不符合安全策略访问"
          "event_type" => "1000065"
          "event_subtype" => "1000000241"
          "extract_fields" => ["src_ip", "src_port", "dest_ip", "dest_port"]
        },
        {
          "regex" => 'VEAD\s1\s6\s(?<Eth>.+)\sdown$'
          "event_name" => "网口拔出"
          "event_type" => "1000065"
          "event_subtype" => "1000000215"
          "extract_fields" => ["Eth"]
        },
        {
          "regex" => '^VEAD\s1\s7\s(?<Eth>.+)\sup$'
          "event_name" => "网口接入"
          "event_type" => "1000065"
          "event_subtype" => "1000000214"
          "extract_fields" => ["Eth"]
        },
        {
          "regex" => '(?<MemRate>.+%)\smemory.+$'
          "event_name" => "性能监控"
          "event_type" => "1000025"
          "event_subtype" => "1000000089"
          "extract_fields" => ["MemRate"]
        },
        {
          "regex" => 'VEAD\s1\s3\s(?<MemRate>.+%)$'
          "event_name" => "性能监控"
          "event_type" => "1000025"
          "event_subtype" => "1000000089"
          "extract_fields" => ["MemRate"]
        },
        {
          "regex" => '^\s*1\s2\s(?<CPURate>.+%) cpu.+$'
          "event_name" => "性能监控"
          "event_type" => "1000025"
          "event_subtype" => "1000000089"
          "extract_fields" => ["CPURate"]
        },
        {
          "regex" => 'VEAD\s1\s2\s(?<CPURate>.+%)\s+cpu.+$'
          "event_name" => "性能监控"
          "event_type" => "1000025"
          "event_subtype" => "1000000089"
          "extract_fields" => ["CPURate"]
        },
        {
          "regex" => '^(?<MgmtCPU>.+)\scpu.*$'
          "event_name" => "性能监控"
          "event_type" => "1000025"
          "event_subtype" => "1000000089"
          "extract_fields" => ["MgmtCPU"]
        },
        {
          "regex" => '^\s*1\s3\s(?<MemUsg>.*)%\smemory\sfree$'
          "event_name" => "性能监控"
          "event_type" => "1000025"
          "event_subtype" => "1000000089"
          "extract_fields" => ["MemUsg"]
        },
        {
          "regex" => '^1\s2\s(?<MgmtCPU>.*)%\scpu\sloadavg$'
          "event_name" => "性能监控"
          "event_type" => "1000025"
          "event_subtype" => "1000000089"
          "extract_fields" => ["MgmtCPU"]
        }
      ]
    }
  }

  # 添加通用字段
  mutate {
    replace => {
      "device_type" => "1000080"
      "product_name" => "卫士通-SJW77"
    }
  }

  # 保存提取字段
  mutate {
    rename => {
      "src_port" => "source_port"
      "dest_ip" => "dest_address"
      "dest_port" => "dest_port"
      "user" => "related_object_name"
      "src_ip" => "source_address"
    }
  }
}
