filter {
    grok {
        match => {
            "message" => [
                "%{IPORHOST:product_name}#%{WORD:product_version} SVR %{INT:temp_event_type} %{INT:temp_event_subtype} %{GREEDYDATA:event_details}$"
            ]
        }
    }
    mutate {
        replace => {
            "device_type" => "1000078"
        }
    }

    # 根据不同的event_type设置具体的事件类型描述
    if [temp_event_type] == "3" {
        mutate {
            replace => { "event_type" => "1000066" }
        }
    } else if [temp_event_type] == "4" {
        mutate {
            replace => { "event_type" => "1000067" }
        }
    } else if [temp_event_type] == "5" {
        mutate {
            replace => { "event_type" => "1000063" }
        }
    }

    # 根据不同的event_subtype设置具体的事件子类型描述
    if [event_type] == "1000066" {
        if [temp_event_subtype] == "1" {
            mutate {
                replace => { "event_subtype" => "1000000157" }
            }
            # CPU 配置信息
            grok {
                match => { "event_details" => "%{NUMBER:cpu_count} %{NUMBER:cpu_frequency} %{NUMBER:cpu_cache}" }
            }
        } else if [temp_event_subtype] == "2" {
            mutate {
                replace => { "event_subtype" => "1000000158" }
            }
            # 内存配置信息
            grok {
                match => { "event_details" => "%{NUMBER:physical_memory} %{NUMBER:virtual_memory}" }
            }
        } else if [temp_event_subtype] == "3" {
            mutate {
                replace => { "event_subtype" => "1000000159" }
            }
            # 硬盘容量信息
            grok {
                match => { "event_details" => "%{NUMBER:disk_capacity}" }
            }
        } else if [temp_event_subtype] == "4" {
            mutate {
                replace => { "event_subtype" => "1000000164" }
            }
            # 网卡配置信息
            grok {
                match => { "event_details" => "%{NUMBER:nic_count} %{GREEDYDATA:nic_details}" }
            }
        } else if [temp_event_subtype] == "5" {
            mutate {
                replace => { "event_subtype" => "1000000160" }
            }
            # 硬盘配置信息
            grok {
                match => { "event_details" => "%{NUMBER:internal_modem_count} %{NUMBER:external_modem_count}" }
            }
        } else if [temp_event_subtype] == "6" {
            mutate {
                replace => { "event_subtype" => "1000000161" }
            }
            # 移动介质数量
            grok {
                match => { "event_details" => "%{NUMBER:removable_media_count} %{NUMBER:removable_media1_capacity} %{NUMBER:removable_media2_capacity}" }
            }
        } else if [temp_event_subtype] == "7" {
            mutate {
                replace => { "event_subtype" => "1000000162" }
            }
            # 串口数量信息
            grok {
                match => { "event_details" => "%{NUMBER:serial_port_count}" }
            }
        } else if [temp_event_subtype] == "8" {
            mutate {
                replace => { "event_subtype" => "1000000163" }
            }
            # 并口数量信息
            grok {
                match => { "event_details" => "%{NUMBER:parallel_port_count}" }
            }
        } else if [temp_event_subtype] == "10" {
            mutate {
                replace => { "event_subtype" => "1000000165" }
            }
            # 操作系统版本信息
            grok {
                match => { "event_details" => "%{NUMBER:os_type_id} %{DATA:os_version} %{DATA:kernel_version}" }
            }
            translate {
                field => "[os_type_id]"
                destination => "[os_type_translated]"
                dictionary => {
                    "0" => "凝思"
                    "1" => "麒麟"
                    "2" => "其他"
                }
            }
            mutate {
                replace => { "action_details" => "%{os_type_translated} %{os_version} %{kernel_version}" }
            }
        }
    } else if [event_type] == "1000067" {
        if [temp_event_subtype] == "1" {
            mutate {
                replace => { "event_subtype" => "1000000166" }
            }
            # CPU 平均负载
            grok {
                match => { "event_details" => "%{NUMBER:cpu_load}%%" }
            }
        } else if [temp_event_subtype] == "2" {
            mutate {
                replace => { "event_subtype" => "1000000167" }
            }
            # 内存使用情况
            grok {
                match => { "event_details" => "%{NUMBER:memory_type} %{NUMBER:memory_usage}%%" }
            }
            translate {
                field => "[memory_type]"
                destination => "[memory_type_translated]"
                dictionary => {
                    "1" => "物理内存"
                    "2" => "虚拟内存"
                }
            }
            mutate {
                replace => { "action_details" => "%{memory_type_translated} %{memory_usage}%%" }
            }
        } else if [temp_event_subtype] == "3" {
            mutate {
                replace => { "event_subtype" => "1000000168" }
            }
            # 硬盘使用情况
            grok {
                match => { "event_details" => "%{NUMBER:disk_usage}%%" }
            }
        } else if [temp_event_subtype] == "4" {
            mutate {
                replace => { "event_subtype" => "1000000169" }
            }
            # 网卡使用情况
            grok {
                match => { "event_details" => "%{DATA:nic_name} %{NUMBER:nic_status} %{NUMBER:nic_received_data} %{NUMBER:nic_sent_data}" }
            }
        } else if [temp_event_subtype] == "5" {
            mutate {
                replace => { "event_subtype" => "1000000170" }
            }
            # Modem 使用情况
            grok {
                match => { "event_details" => "%{NUMBER:modem_status} %{DATA:modem_name}" }
            }
            translate {
                field => "[modem_status]"
                destination => "[modem_status_translated]"
                dictionary => {
                    "0" => "无连接"
                    "1" => "已拨号连接"
                }
            }
            mutate {
                replace => { "action_details" => "%{modem_status_translated} %{modem_name}" }
            }
        } else if [temp_event_subtype] == "11" {
            mutate {
                replace => { "event_subtype" => "1000000171" }
            }
            # 僵尸进程数量
            grok {
                match => { "event_details" => "%{NUMBER:zombie_process_count} %{GREEDYDATA:zombie_process_details}" }
            }

        } else if [temp_event_subtype] == "12" {
            mutate {
                replace => { "event_subtype" => "1000000172" }
            }
            # TCP 链接 CLOSE_WAIT 数量
            grok {
                match => { "event_details" => "%{NUMBER:close_wait_count}" }
            }

        } else if [temp_event_subtype] == "13" {
            mutate {
                replace => { "event_subtype" => "1000000173" }
            }
            # 网络端口监听情况
            grok {
                match => { "event_details" => "%{WORD:protocol} %{IP:source_address} %{NUMBER:source_port} %{IP:dest_address} %{NUMBER:dest_port} %{WORD:connection_status} %{NUMBER:process_id} %{DATA:service_name}" }
            }
        } else if [temp_event_subtype] == "15" {
            mutate {
                replace => { "event_subtype" => "1000000174" }
            }
            # 主板温度状态
            grok {
                match => { "event_details" => "%{NUMBER:board_point} %{NUMBER:board_temp}" }
            }
        } else if [temp_event_subtype] == "16" {
            mutate {
                replace => { "event_subtype" => "1000000175" }
            }
            # 风扇转数状态
            grok {
                match => { "event_details" => "%{NUMBER:fan_point} %{NUMBER:fan_speed}" }
            }
        } else if [temp_event_subtype] == "17" {
            mutate {
                replace => { "event_subtype" => "1000000176" }
            }
            # USB 使用情况
            grok {
                match => { "event_details" => "%{NUMBER:usb_storage_count} %{NUMBER:non_storage_usb_count}" }
            }
            translate {
                field => "[storage_usb_count]"
                destination => "[storage_usb_count_translated]"
                dictionary => {
                    "1" => "存储类"
                    "2" => "非存储类(键盘鼠标)"
                    "3" => "非存储类(其他)"
                }
            }
            mutate {
                replace => { "action_details" => "%{storage_usb_count_translated} %{non_storage_usb_count}" }
            }
        } else if [temp_event_subtype] == "18" {
            mutate {
                replace => { "event_subtype" => "1000000177" }
            }
            # 光驱使用情况
            grok {
                match => { "event_details" => "%{NUMBER:cdrom_status} %{DATA:cdrom_name}" }
            }
        }
    } else if [event_type] == "1000063" {
        if [temp_event_subtype] == "17" {
            mutate {
                replace => { "event_subtype" => "1000000178" }
            }
            # USB 接入情况
            grok {
                match => { "event_details" => "%{NUMBER:usb_insert_state} %{DATA:usb_interface} %{DATA:device_name} %{DATA:vendor_name} %{NUMBER:device_id} %{NUMBER:vendor_id} %{DATA:interface_id_protocol} %{NUMBER:usb_type}" }
            }
        } else if [temp_event_subtype] == "18" {
            mutate {
                replace => { "event_subtype" => "1000000179" }
            }
            # USB 拔出情况
            grok {
                match => { "event_details" => "%{NUMBER:usb_remove_state} %{DATA:usb_interface} %{DATA:device_name} %{DATA:vendor_name} %{NUMBER:device_id} %{NUMBER:vendor_id} %{DATA:interface_id_protocol} %{NUMBER:usb_type}" }
            }
        } else if [temp_event_subtype] == "19" {
            mutate {
                replace => { "event_subtype" => "1000000180" }
            }
            # 串口接入
            grok {
                match => { "event_details" => "%{DATA:serial_port_name}" }
            }
        } else if [temp_event_subtype] == "20" {
            mutate {
                replace => { "event_subtype" => "1000000181" }
            }
            # 串口拔出
            grok {
                match => { "event_details" => "%{DATA:serial_port_name}" }
            }
        } else if [temp_event_subtype] == "21" {
            mutate {
                replace => { "event_subtype" => "1000000182" }
            }
            # 并口插入
            grok {
                match => { "event_details" => "%{DATA:parallel_port_name}" }
            }
        } else if [temp_event_subtype] == "22" {
            mutate {
                replace => { "event_subtype" => "1000000183" }
            }
            # 并口拔出
            grok {
                match => { "event_details" => "%{DATA:parallel_port_name}" }
            }
        } else if [temp_event_subtype] == "23" {
            mutate {
                replace => { "event_subtype" => "1000000184" }
            }
            # 光驱加载
            grok {
                match => { "event_details" => "%{DATA:cdrom_name} %{DATA:cdrom_disc}" }
            }
        } else if [temp_event_subtype] == "24" {
            mutate {
                replace => { "event_subtype" => "1000000185" }
            }
            # 光驱托盘弹出
            grok {
                match => { "event_details" => "%{DATA:cdrom_name}" }
            }
        } else if [temp_event_subtype] == "25" {
            mutate {
                replace => { "event_subtype" => "1000000186" }
            }
            # 非法外联
            grok {
                match => { "event_details" => "%{DATA:protocol} %{IP:source_address} %{NUMBER:source_port} %{IP:dest_address} %{NUMBER:dest_port}" }
            }
        } else if [temp_event_subtype] == "26" {
            mutate {
                replace => { "event_subtype" => "1000000187" }
            }
            # 网口状态异常
            grok {
                match => { "event_details" => "%{DATA:network_interface} down" }
            }
        } else if [temp_event_subtype] == "27" {
            mutate {
                replace => { "event_subtype" => "1000000188" }
            }
            # 网口状态恢复
            grok {
                match => { "event_details" => "%{DATA:network_interface} up" }
            }
        } else if [temp_event_subtype] == "30" {
            mutate {
                replace => { "event_subtype" => "1000000189" }
            }
            # 主板温度过高
            grok {
                match => { "event_details" => "%{NUMBER:temperature1} %{NUMBER:sensor1} %{NUMBER:temperature2} %{NUMBER:sensor2}" }
            }
        } else if [temp_event_subtype] == "31" {
            mutate {
                replace => { "event_subtype" => "1000000190" }
            }
            # 风扇故障
            grok {
                match => { "event_details" => "%{NUMBER:fan_point1} %{NUMBER:fan_speed1} %{NUMBER:fan_point2} %{NUMBER:fan_speed2}" }
            }
        } else if [temp_event_subtype] == "32" {
            mutate {
                replace => { "event_subtype" => "1000000191" }
            }
            # 电源故障
            grok {
                match => { "event_details" => "" }  # 没有具体的event_details字段
            }
        } else if [temp_event_subtype] == "33" {
            mutate {
                replace => { "event_subtype" => "1000000192" }
            }
            # 非法登录尝试
            grok {
                match => { "event_details" => "%{IP:source_address} %{IP:dest_address} %{NUMBER:dest_port} %{DATA:related_object_name}" }
            }
        } else if [temp_event_subtype] == "34" {
            mutate {
                replace => { "event_subtype" => "1000000193" }
            }
            # 关键文件/目录变更
            grok {
                match => { "event_details" => "%{IP:action_object_address} %{DATA:related_object_name} %{DATA:file_path} %{NUMBER:change_type} %{NUMBER:old_permission} %{NUMBER:new_permission}" }
            }
        } else if [temp_event_subtype] == "35" {
            mutate {
                replace => { "event_subtype" => "1000000194" }
            }
            # 用户权限变更
            grok {
                match => { "event_details" => "%{IP:action_object_address} %{DATA:operator} %{DATA:related_object_name} %{DATA:change_type}" }
            }
        } else if [temp_event_subtype] == "36" {
            mutate {
                replace => { "event_subtype" => "1000000195" }
            }
            # USB 存储功能开启
            grok {
                match => { "event_details" => "%{NUMBER:usb_storage_status}" }
            }
        } else if [temp_event_subtype] == "39" {
            mutate {
                replace => { "event_subtype" => "1000000196" }
            }
            # 主机系统命令调用
            grok {
                match => { "event_details" => "%{NUMBER:pid} %{DATA:process_name} %{DATA:command}" }
            }
        } else if [temp_event_subtype] == "21" {
            mutate {
                replace => { "event_subtype" => "1000000197" }
            }
            # 主机网络服务开启
            grok {
                match => { "event_details" => "%{DATA:service_name} %{NUMBER:service_status}" }
            }
        }
    }

}