filter {
    grok {
      match => {
        "message" => [
          # 登录成功 - Console
          "^<\d+>(?<timestamp>%{MONTHNUM}%{SPACE}%{MONTHDAY}%{SPACE}%{TIME})\s+WitNet.*CONFIG.*-Console:user:\s+(?<user>\S+)\s+Login.*$",
          
          # 登录成功 - Telnet/SSH
          "^<\d+>(?<timestamp>%{MONTHNUM}%{SPACE}%{MONTHDAY}%{SPACE}%{TIME})\s+WitNet.*CONFIG.*-(?<login_type>\S+):user:\s+(?<user>\S+)\s+Login\s+from\s+IP:(?<src_ip>\S+).*$",
          
          # 登录成功 - Web
          "^<\d+>(?<timestamp>%{MONTHNUM}%{SPACE}%{MONTHDAY}%{SPACE}%{TIME})\s+WitNet.*CONFIG.*:\s+(?<user>\S+?)\s+.*welcome\.html.*$",
          
          # 登录失败
          "^<\d+>(?<timestamp>%{MONTHNUM}%{SPACE}%{MONTHDAY}%{SPACE}%{TIME})\s+WitNet.*CONFIG.*-(?<login_type>\S+):.*failed\s+from\s+IP:(?<src_ip>\S+).*$",
          
          # 退出登录 - Console
          "^<\d+>(?<timestamp>%{MONTHNUM}%{SPACE}%{MONTHDAY}%{SPACE}%{TIME})\s+WitNet.*CONFIG.*-Console:user:\s+(?<user>\S+)\s+Logout.*$",
          
          # 退出登录 - Telnet/SSH
          "^<\d+>(?<timestamp>%{MONTHNUM}%{SPACE}%{MONTHDAY}%{SPACE}%{TIME})\s+WitNet.*CONFIG.*-(?<login_type>\S+):user:\s+(?<user>\S+)\s+Logout\s+from\s+IP:(?<src_ip>\S+).*$",
          
          # 退出登录 - Web
          "^<\d+>(?<timestamp>%{MONTHNUM}%{SPACE}%{MONTHDAY}%{SPACE}%{TIME})\s+WitNet.*CONFIG.*:\s+(?<user>\S+?)\s+.*Logout\.html.*$",
          
          # 网口拔出
          "^<\d+>(?<timestamp>%{MONTHNUM}%{SPACE}%{MONTHDAY}%{SPACE}%{TIME})\s+WitNet.*LINK_D:(?<interface>port\s+\S+)\s.*$",
          
          # 网口插入
          "^<\d+>(?<timestamp>%{MONTHNUM}%{SPACE}%{MONTHDAY}%{SPACE}%{TIME})\s+WitNet.*LINK_U:(?<interface>port\s+\S+)\s.*$"
        ]
      }
    }

    # 添加通用字段
    mutate {
      replace => {
        "device_type" => "1000034"
        "product_name" => "博维亚讯"
        "product_name" => "IAS-3028"
      }
    }

    # 设置具体的事件类型
    if [message] =~ /Login/ and ![message] =~ /failed/ {
      mutate {
        replace => { 
          "event_type" => "1000028"
          "event_subtype" => "1000000112"
          "event_name" => "登录成功"
        }
      }
    } else if [message] =~ /failed/ {
      mutate {
        replace => { 
          "event_type" => "1000028"
          "event_subtype" => "1000000112"
          "event_name" => "登录失败"
        }
      }
    } else if [message] =~ /Logout/ {
      mutate {
        replace => { 
          "event_type" => "1000028"
          "event_subtype" => "1000000113"
          "event_name" => "退出登录"
        }
      }
    } else if [message] =~ /LINK_D/ {
      mutate {
        replace => { 
          "event_type" => "1000025"
          "event_subtype" => "1000000091"
          "event_name" => "网口拔出"
        }
      }
    } else if [message] =~ /LINK_U/ {
      mutate {
        replace => { 
          "event_type" => "1000025"
          "event_subtype" => "1000000091"
          "event_name" => "网口插入"
        }
      }
    }

    # 时间格式转换
    date {
      match => [ "timestamp", "MMM dd HH:mm:ss" ]
      target => "@timestamp"
    }
  }