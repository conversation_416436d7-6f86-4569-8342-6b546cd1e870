filter {
    grok {
        match => {
            "message" => [
                "VEAD %{INT:temp_event_type} %{INT:temp_event_subtype} %{GREEDYDATA:event_details}"
            ]
        }
    }
    if [hostname] {
        mutate {
            replace => {
                device_name => "%{hostname}"
            }
        }
    }

    mutate {
        replace => {
            "device_type" => "1000080"
        }
    }

    # 根据不同的event_type和event_subtype设置具体的事件类型和子类型描述
    if [temp_event_type] == "0" {
        mutate {
            replace => { "event_type" => "1000068" }
        }
        if [temp_event_subtype] == "1" {
            mutate {
                replace => { "event_subtype" => "1000000198" }
            }
            # 用户登录成功
            grok {
                match => { "event_details" => "%{DATA:related_object_name}" }
            }
        } else if [temp_event_subtype] == "2" {
            mutate {
                replace => { "event_subtype" => "1000000202" }
            }
            # 用户登录失败
            grok {
                match => { "event_details" => "%{DATA:related_object_name}" }
            }
        } else if [temp_event_subtype] == "3" {
            mutate {
                replace => { "event_subtype" => "1000000245" }
            }
            # 修改配置
            grok {
                match => { "event_details" => "%{NUMBER:config_id}" }
            }
        } else if [temp_event_subtype] == "4" {
            mutate {
                replace => { "event_subtype" => "1000000201" }
            }
            # 用户退出
            grok {
                match => { "event_details" => "%{DATA:related_object_name}" }
            }
        }
    } else if [temp_event_type] == "1" {
        mutate {
            replace => { "event_type" => "1000071" }
        }
        if [temp_event_subtype] == "2" {
            mutate {
                replace => { "event_subtype" => "1000000235" }
            }
            # CPU利用率
            grok {
                match => { "event_details" => "%{NUMBER:cpu_usage}%%" }
            }
        } else if [temp_event_subtype] == "3" {
            mutate {
                replace => { "event_subtype" => "1000000236" }
            }
            # 内存使用率
            grok {
                match => { "event_details" => "%{NUMBER:memory_usage}%%" }
            }
        } else if [temp_event_subtype] == "6" {
            mutate {
                replace => { "event_subtype" => "1000000240" }
            }
            # 网口状态异常
            grok {
                match => { "event_details" => "%{DATA:interface} %{WORD:interface_status}" }
            }
        } else if [temp_event_subtype] == "7" {
            mutate {
                replace => { "event_subtype" => "1000000246" }
            }
            # 网口状态恢复
            grok {
                match => { "event_details" => "%{DATA:interface} %{WORD:interface_status}" }
            }
        }
    } else if [temp_event_type] == "2" {
        mutate {
            replace => { "event_type" => "1000065" }
        }
        if [temp_event_subtype] == "1" {
            mutate {
                replace => { "event_subtype" => "1000000247" }
            }
            # 隧道建立错误
            grok {
                match => { "event_details" => "%{NUMBER:error_id} %{IP:source_address} %{IP:dest_address} %{GREEDYDATA:error_description}" }
            }
        } else if [temp_event_subtype] == "7" {
            mutate {
                replace => { "event_subtype" => "1000000241" }
            }
            # 不符合安全策略的访问告警
            grok {
                match => { "event_details" => "%{NUMBER:anomaly_id} %{IP:source_address} %{NUMBER:source_port} %{IP:dest_address} %{NUMBER:dest_port}" }
            }
        }
    }

    # 解析配置ID
    if [config_id] {
        translate {
            field => "config_id"
            destination => "action_details"
            dictionary => {
                "1" => "系统配置"
                "2" => "IP配置"
                "3" => "路由配置"
                "4" => "策略配置"
                "5" => "隧道配置"
                "6" => "装置管理配置"
                "7" => "更新证书"
            }
            fallback => "未知配置类型"
        }
    }

    # 解析隧道错误ID
    if [error_id] {
        translate {
            field => "error_id"
            destination => "action_details"
            dictionary => {
                "1" => "私钥解密错误"
                "2" => "验证签名错误"
                "3" => "证书不存在"
                "4" => "隧道没有配置"
                "5" => "公钥加密错误"
                "6" => "私钥签名错误"
                "7" => "隧道由密通变明通"
                "8" => "隧道由明通变密通"
            }
            fallback => "未知错误类型"
        }
    }

    # 解析异常ID
    if [anomaly_id] {
        translate {
            field => "anomaly_id"
            destination => "action_details"
            dictionary => {
                "8" => "不符合安全策略的访问"
            }
            fallback => "未知异常类型"
        }
    }
}