filter {
  ruby {
    path => "/usr/share/logstash/config/conf.d/ruby/event_processor.rb"
    script_params => {
      "patterns" => [
        {
          "regex" => 'client-type\(\d+\):(?<type>\S+);user-name\(\d+\):(?<username>\S+);host-ip\(\d+\):(?<srcIp>\S+);error-code\(\d+\):0.*?登录.*$'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => ["type", "username", "srcIp"]
        },
        {
          "regex" => 'client-type\(\d+\):(?<type>\S+);user-name\(\d+\):(?<user>\S+);host-ip\(\d+\):(?<srcIp>\S+);error-code\(87\):0.*\slogged\sin.*$'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => ["type", "user", "srcIp"]
        },
        {
          "regex" => 'client-type\(\d+\):(?<type>\S+);user-name\(85\):(?<username>\S+);host-ip\(\d+\):(?<srcIp>\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3});error-code\(\d+\).*?$'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => ["type", "username", "srcIp"]
        },
        {
          "regex" => 'client-type\(84\):(?<type>\S+);user-name\(85\):(?<username>\S+);host-ip\(86\):(?<srcIp>\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3});error-code\(87\):0.*\slogged\sin.*$'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => ["type", "username", "srcIp"]
        },
        {
          "regex" => 'client-type\(\d+\):(?<LoginType>\S+);user-name\(\d+\):(?<username>\S+);host-ip\(\d+\):(?<srcIp>\S+);error-code\(87\):[^0].*?$'
          "event_name" => "登录失败"
          "event_type" => "1000061"
          "event_subtype" => "1000000140"
          "extract_fields" => ["LoginType", "username", "srcIp"]
        },
        {
          "regex" => 'client-type\(\d+\):(?<LoginType>\S+);user-name\(\d+\):(?<username>\S+);host-ip\(\d+\):(?<srcIp>\S+);error-code\(87\):0.*?退出。.*$'
          "event_name" => "退出登录"
          "event_type" => "1000068"
          "event_subtype" => "1000000201"
          "extract_fields" => ["LoginType", "username", "srcIp"]
        },
        {
          "regex" => 'client-type\(84\):(?<type>\S+);user-name\(85\):(?<username>\S+);host-ip\(86\):(?<srcIp>\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3});error-code\(87\):0.*\slogged\sout.$'
          "event_name" => "退出登录"
          "event_type" => "1000068"
          "event_subtype" => "1000000201"
          "extract_fields" => ["type", "username", "srcIp"]
        },
        {
          "regex" => 'client-type\(84\):(?<type>\S+);user-name\(85\):(?<username>\S+);host-ip\(86\):(?<srcIp>\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3});error-code\(87\):0;Modified\soperation\slog.*$'
          "event_name" => "配置变更"
          "event_type" => "1000026"
          "event_subtype" => "1000000094"
          "extract_fields" => ["type", "username", "srcIp"]
        },
        {
          "regex" => 'Interface\s(?<interface>\S+)\s+is\s+down$'
          "event_name" => "USB设备拔出"
          "event_type" => "1000061"
          "event_subtype" => "1000000250"
          "extract_fields" => ["interface"]
        },
        {
          "regex" => 'Interface\s(?<interface>\S+)\s+is\s+up$'
          "event_name" => "USB设备接入"
          "event_type" => "1000065"
          "event_subtype" => "1000000203"
          "extract_fields" => ["interface"]
        },
        {
          "regex" => 'POLICY:Deny\s(?<Protocol>\d{1,2})\|(?<SourceIP>\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3})\|(?<SourcePort>\d{1,5})\|(?<DestinationIP>\d{1,5}.\d{1,3}.\d{1,3}.\d{1,3})\|(?<DestinationPort>\d{1,5}).*$'
          "event_name" => "不符合安全策略访问"
          "event_type" => "1000065"
          "event_subtype" => "1000000241"
          "extract_fields" => ["Protocol", "SourceIP", "SourcePort", "DestinationIP", "DestinationPort"]
        },
        {
          "regex" => '/PFPOLICY/0/SRVLOG\(l\):\s+log-type:pf_policy;\S+src_ip:(?<SourceIP>\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3})\S+dst_ip:(?<DestinationIP>\d{1,5}.\d{1,3}.\d{1,3}.\d{1,3})\S+src_port:(?<SourcePort>\d{1,5})\S+dst_port:(?<DestinationPort>\d{1,5}).*action:block.*$'
          "event_name" => "不符合安全策略访问"
          "event_type" => "1000065"
          "event_subtype" => "1000000241"
          "extract_fields" => ["SourceIP", "DestinationIP", "SourcePort", "DestinationPort"]
        },
        {
          "regex" => 'POLICY:Allow\s(?<Protocol>\d{1,2})\|(?<SourceIP>\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3})\|(?<SourcePort>\d{1,5})\|(?<DestinationIP>\d{1,5}.\d{1,3}.\d{1,3}.\d{1,3})\|(?<DestinationPort>\d{1,5}).*$'
          "event_name" => "符合安全策略访问"
          "event_type" => "1000065"
          "event_subtype" => "1000000241"
          "extract_fields" => ["Protocol", "SourceIP", "SourcePort", "DestinationIP", "DestinationPort"]
        },
        {
          "regex" => '接口(?<interface>\w+)当前状态:down$'
          "event_name" => "网口拔出"
          "event_type" => "1000065"
          "event_subtype" => "1000000215"
          "extract_fields" => ["interface"]
        },
        {
          "regex" => 'Interface\s+(?<interface>\S+)\s+link\s+status\s+is\s+down$'
          "event_name" => "网口拔出"
          "event_type" => "1000065"
          "event_subtype" => "1000000215"
          "extract_fields" => ["interface"]
        },
        {
          "regex" => '接口(?<interface>\w+)当前状态:up$'
          "event_name" => "网口接入"
          "event_type" => "1000065"
          "event_subtype" => "1000000214"
          "extract_fields" => ["interface"]
        },
        {
          "regex" => 'Interface\s+(?<interface>\S+)\s+link\s+status\s+is\s+up$'
          "event_name" => "网口接入"
          "event_type" => "1000065"
          "event_subtype" => "1000000214"
          "extract_fields" => ["interface"]
        }
      ]
    }
  }

  # 添加通用字段
  mutate {
    replace => {
      "device_type" => "1000035"
      "product_name" => "迪普-FW1000-GC-N"
    }
  }

  # 保存提取字段
  mutate {
    rename => {
      "Protocol" => "protocol"
      "username" => "related_object_name"
      "SourceIP" => "source_address"
      "DestinationIP" => "dest_address"
      "srcIp" => "source_address"
      "user" => "related_object_name"
      "interface" => "action_object_name"
      "DestinationPort" => "dest_port"
    }
  }
}
