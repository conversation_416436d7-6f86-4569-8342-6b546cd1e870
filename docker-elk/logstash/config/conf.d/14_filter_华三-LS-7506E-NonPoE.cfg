filter {
  ruby {
    path => "/usr/share/logstash/config/conf.d/ruby/event_processor.rb"
    script_params => {
      "patterns" => [
        {
          "regex" => 'Syslog message:\s\S+?\s(?<time>\S+?\s\d{1,2}\s\d{2}:\d{2}:\d{2}\s\d{4}).*?LOGIN: (?<user>\S+?) logged in from.*$'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => ["user"]
        },
        {
          "regex" => 'Failed to login.*?IP=(?<ipaddress>.*?), UserName=(?<Username>.*?),'
          "event_name" => "登录失败"
          "event_type" => "1000061"
          "event_subtype" => "1000000140"
          "extract_fields" => ["ipaddress", "Username"]
        },
        {
          "regex" => 'Syslog message:\s\S+?\s(?<time>\S+?\s\d{1,2}\s\d{2}:\d{2}:\d{2}\s\d{4}).*?LOGIN_FAILED: (?<user>\S+?) failed to log in from.*$'
          "event_name" => "登录失败"
          "event_type" => "1000061"
          "event_subtype" => "1000000140"
          "extract_fields" => ["user"]
        },
        {
          "regex" => 'Syslog message:\s\S+?\s(?<time>\S+?\s\d{1,2}\s\d{2}:\d{2}:\d{2}\s\d{4}).*?LOGOUT: (?<user>\S+?) logged out from.*$'
          "event_name" => "退出登录"
          "event_type" => "1000068"
          "event_subtype" => "1000000201"
          "extract_fields" => ["user"]
        },
        {
          "regex" => 'The user succeeded in logging out.*?UserName=(?<Username>.*?), Ip=(?<ipaddress>.*?),'
          "event_name" => "退出登录"
          "event_type" => "1000068"
          "event_subtype" => "1000000201"
          "extract_fields" => ["Username", "ipaddress"]
        },
        {
          "regex" => 'Physical\s.+interface\s(?<GE>.+?)\schanged to down.+'
          "event_name" => "网口拔出"
          "event_type" => "1000065"
          "event_subtype" => "1000000215"
          "extract_fields" => ["GE"]
        },
        {
          "regex" => 'Syslog message:\s\S+?\s(?<time>\S+?\s\d{1,2}\s\d{2}:\d{2}:\d{2}\s\d{4}).*?PHY_UPDOWN: Physical state on the interface (?<ifname>\S+?) changed to down.*$'
          "event_name" => "网口拔出"
          "event_type" => "1000065"
          "event_subtype" => "1000000215"
          "extract_fields" => ["ifname"]
        },
        {
          "regex" => 'Physical\s.+interface\s(?<GE>.+?)\schanged to up.+'
          "event_name" => "网口接入"
          "event_type" => "1000065"
          "event_subtype" => "1000000214"
          "extract_fields" => ["GE"]
        },
        {
          "regex" => 'Syslog message:\s\S+?\s(?<time>\S+?\s\d{1,2}\s\d{2}:\d{2}:\d{2}\s\d{4}).*?PHY_UPDOWN: Physical state on the interface (?<ifname>\S+?) changed to up.*$'
          "event_name" => "网口接入"
          "event_type" => "1000065"
          "event_subtype" => "1000000214"
          "extract_fields" => ["ifname"]
        }
      ]
    }
  }

  # 添加通用字段
  mutate {
    replace => {
      "device_type" => "1000079"
      "product_name" => "华三-LS-7506E-NonPoE"
    }
  }

  # 保存提取字段
  mutate {
    rename => {
      "ifname" => "action_object_name"
      "user" => "related_object_name"
      "Username" => "related_object_name"
    }
  }
}
