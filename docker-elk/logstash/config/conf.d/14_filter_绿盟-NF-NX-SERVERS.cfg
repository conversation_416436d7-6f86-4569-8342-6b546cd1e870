filter {
  ruby {
    path => "/usr/share/logstash/config/conf.d/ruby/event_processor.rb"
    script_params => {
      "patterns" => [
        {
          "regex" => '^<\d+>user:(?<Username>\S+);loginip:(?<SourceIP>\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3});time:(?<timestamp>\d+);type:1;.*?$'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => ["Username", "SourceIP", "timestamp"]
        },
        {
          "regex" => '^<\d+>user:(?<Username>\S+);loginip:(?<SourceIP>\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3});time:(?<timestamp>\d+);type:3;.*?$'
          "event_name" => "登录失败"
          "event_type" => "1000061"
          "event_subtype" => "1000000140"
          "extract_fields" => ["Username", "SourceIP", "timestamp"]
        },
        {
          "regex" => '^user:(?<Username>\S+);loginip:(?<SourceIP>\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3});type:1;.*?注销成功.*'
          "event_name" => "退出登录"
          "event_type" => "1000068"
          "event_subtype" => "1000000201"
          "extract_fields" => ["Username", "SourceIP"]
        },
        {
          "regex" => '^<\d+>rule_id:\d;time:(?<time>\d{4}-\d{2}-\d{2}\s\d{2}:\d{2}:\d{2});module:fw;src_intf:.*;dst_intf:.*;action:drop;proto:(?<proto>\S+?);src_addr:(?<src_ip>\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3});src_port:(?<src_port>\d+);dst_addr:(?<dest_ip>\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3});dst_port:(?<dest_port>\d+);src_addr_nat:.*;src_port_nat:.*;dst_addr_nat:.*;dst_port_nat:.*;.*'
          "event_name" => "不符合安全策略访问"
          "event_type" => "1000065"
          "event_subtype" => "1000000241"
          "extract_fields" => ["proto", "src_ip", "src_port", "dest_ip", "dest_port"]
        },
        {
          "regex" => '^<\d+>rule_id:\d;time:(?<time>\d{4}-\d{2}-\d{2}\s\d{2}:\d{2}:\d{2});module:fw;src_intf:.*;dst_intf:.*;action:accept;proto:(?<proto>\S+?);src_addr:(?<src_ip>\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3});src_port:(?<src_port>\d+);dst_addr:(?<dest_ip>\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3});dst_port:(?<dest_port>\d+);src_addr_nat:.*;src_port_nat:.*;dst_addr_nat:.*;dst_port_nat:.*;.*'
          "event_name" => "符合安全策略访问"
          "event_type" => "1000065"
          "event_subtype" => "1000000241"
          "extract_fields" => ["proto", "src_ip", "src_port", "dest_ip", "dest_port"]
        }
      ]
    }
  }

  # 添加通用字段
  mutate {
    replace => {
      "device_type" => "1000035"
      "product_name" => "绿盟-NF-NX-SERVERS"
    }
  }

  # 保存提取字段
  mutate {
    rename => {
      "src_port" => "source_port"
      "timestamp" => "action_details"
      "proto" => "protocol"
      "SourceIP" => "source_address"
      "dest_ip" => "dest_address"
      "dest_port" => "dest_port"
      "src_ip" => "source_address"
      "Username" => "related_object_name"
    }
  }
}
