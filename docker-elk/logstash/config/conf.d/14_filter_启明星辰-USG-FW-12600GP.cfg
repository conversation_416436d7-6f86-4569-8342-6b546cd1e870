filter {
  ruby {
    path => "/usr/share/logstash/config/conf.d/ruby/event_processor.rb"
    script_params => {
      "patterns" => [
        {
          "regex" => '^<\d+>kernel:.*date="(?<time>.*)"\s.*pri=5.*mod=pf\ssa=(?<srcIp>\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3})\ssport=(?<srcPort>\S+)\stype=\S+\sda=(?<dstIp>\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3})\sdport=(?<dstPort>\S+).*policy=POLICY_DENY.*$'
          "event_name" => "不符合安全策略访问"
          "event_type" => "1000065"
          "event_subtype" => "1000000241"
          "extract_fields" => ["srcIp", "srcPort", "dstIp", "dstPort"]
        }
      ]
    }
  }

  # 添加通用字段
  mutate {
    replace => {
      "device_type" => "1000035"
      "product_name" => "启明星辰-USG-FW-12600GP"
    }
  }

  # 保存提取字段
  mutate {
    rename => {
      "dstIp" => "dest_address"
      "dstPort" => "dest_port"
      "srcIp" => "source_address"
      "srcPort" => "source_port"
    }
  }
}
