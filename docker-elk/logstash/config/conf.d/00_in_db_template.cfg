input {
  jdbc {
    jdbc_driver_library => "/usr/share/logstash/jdbc/${jdbc_driver_library}"
    jdbc_driver_class => "${jdbc_driver_class}"
    jdbc_connection_string => "${jdbc_connection_string}"
    jdbc_user => "${jdbc_user}"
    jdbc_password => "${jdbc_password}"
    jdbc_paging_enabled => "true"
    jdbc_page_size => "5000"
    schedule => "* * * * *"
    statement => "${statement}"
    record_last_run => true
    use_column_value => true
    tracking_column => "${tracking_column}"
    tracking_column_type => "${tracking_column_type}"
    last_run_metadata_path => "/usr/share/logstash/last_run_metadata/last_run_metadata_${id}"
    jdbc_default_timezone => "Asia/Shanghai"
    target => "sql_feilds"
  }
}

filter {
  mutate {
    replace => {
      device_address => "${device_address}"
    }
  }
  ruby {
    path => "/usr/share/logstash/config/conf.d/ruby/sql_join_fields.rb"
    script_params => {
      "separator" => "|||"  # 可以自定义分隔符
    }
  }
}