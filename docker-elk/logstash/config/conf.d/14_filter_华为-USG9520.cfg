filter {
  ruby {
    path => "/usr/share/logstash/config/conf.d/ruby/event_processor.rb"
    script_params => {
      "patterns" => [
        {
          "regex" => '%%\S+POLICY/\d/POLICYDENY.*source-ip=(?<srcIp>\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3}),\ssource-port=(?<srcPort>\S+),\sdestination-ip=(?<dstIp>\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3}),\sdestination-port=(?<dstPort>\S+),.*$'
          "event_name" => "不符合安全策略访问"
          "event_type" => "1000065"
          "event_subtype" => "1000000241"
          "extract_fields" => ["srcIp", "srcPort", "dstIp", "dstPort"]
        },
        {
          "regex" => 'POLICYPERMIT.*protocol=(?<protocol>\d+),\ssource-ip=(?<srcIP>\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3}),\ssource-port=(?<srcPort>\d+?),\sdestination-ip=(?<dstIP>\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3}),\sdestination-port=(?<dstport>\d+).*?$'
          "event_name" => "符合安全策略访问"
          "event_type" => "1000065"
          "event_subtype" => "1000000241"
          "extract_fields" => ["protocol", "srcIP", "srcPort", "dstIP", "dstport"]
        },
        {
          "regex" => '%%\d+\w+/\d+/\S+\[\d+\]:链路协议IP在接口(?<NetPort>\S+)上的状态变为DOWN.*$'
          "event_name" => "网口拔出"
          "event_type" => "1000065"
          "event_subtype" => "1000000215"
          "extract_fields" => ["NetPort"]
        },
        {
          "regex" => '%%\d+\w+/\d+/\S+\[\d+\]:链路协议IP在接口(?<NetPort>\S+)上的状态变为UP.*$'
          "event_name" => "网口接入"
          "event_type" => "1000065"
          "event_subtype" => "1000000214"
          "extract_fields" => ["NetPort"]
        }
      ]
    }
  }

  # 添加通用字段
  mutate {
    replace => {
      "device_type" => "1000035"
      "product_name" => "华为-USG9520"
    }
  }

  # 保存提取字段
  mutate {
    rename => {
      "dstIP" => "dest_address"
      "dstIp" => "dest_address"
      "protocol" => "protocol"
      "dstPort" => "dest_port"
      "srcIp" => "source_address"
      "srcPort" => "source_port"
      "NetPort" => "dest_port"
    }
  }
}
