filter {
  ruby {
    path => "/usr/share/logstash/config/conf.d/ruby/event_processor.rb"
    script_params => {
      "patterns" => [
        {
          "regex" => 'id=tos.*time="(?<time>\S+\s+\S+)".*user=(?<user>\S+)\s+src=(?<srcIp>.*)\s+op="login"\s+result=.*success.*$'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => ["user", "srcIp"]
        },
        {
          "regex" => 'time="(?<time>\S+\s+\S+)".*user=(?<user>\S+)\s+src=(?<srcIp>.*)\s+op="login".*msg="faild".*$'
          "event_name" => "登录失败"
          "event_type" => "1000061"
          "event_subtype" => "1000000140"
          "extract_fields" => ["user", "srcIp"]
        },
        {
          "regex" => 'time="(?<time>\S+\s+\S+)".*user=(?<user>\S+)\s+src=(?<srcIp>.*)\s+op="logout".*$'
          "event_name" => "退出登录"
          "event_type" => "1000068"
          "event_subtype" => "1000000201"
          "extract_fields" => ["user", "srcIp"]
        },
        {
          "regex" => 'src=(?<srcIp>.*)\s+dst=(?<dstIp>.*)\s+sport=(?<srcPort>\d+)\s+dport=(?<dstPort>\d+).*rule=\S*[Dd]eny.*$'
          "event_name" => "不符合安全策略访问"
          "event_type" => "1000065"
          "event_subtype" => "1000000241"
          "extract_fields" => ["srcIp", "dstIp", "srcPort", "dstPort"]
        },
        {
          "regex" => 'src=(?<srcIp>.*)\s+dst=(?<dstIp>.*)\s+sport=(?<srcPort>\d+)\s+dport=(?<dstPort>\d+).*rule=\S*[Dd]eny.*$'
          "event_name" => "不符合安全策略访问"
          "event_type" => "1000065"
          "event_subtype" => "1000000241"
          "extract_fields" => ["srcIp", "dstIp", "srcPort", "dstPort"]
        },
        {
          "regex" => 'time="(?<time>\S+\s+\S+)".*src=(?<srcIp>.*)\s+dst=(?<dstIp>\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3})\s+sport=(?<srcPort>\d+)\s+dport=(?<dstPort>\d+).*rule=Accept\s+policyid.*$'
          "event_name" => "符合安全策略访问"
          "event_type" => "1000065"
          "event_subtype" => "1000000241"
          "extract_fields" => ["srcIp", "dstIp", "srcPort", "dstPort"]
        },
        {
          "regex" => 'time="(?<time>\S+\s+\S+)".*src=(?<srcIp>.*)\s+dst=(?<dstIp>\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3})\strans_dst.*sport=(?<srcPort>\d+)\s+dport=(?<dstPort>\d+).*$'
          "event_name" => "符合安全策略访问"
          "event_type" => "1000065"
          "event_subtype" => "1000000241"
          "extract_fields" => ["srcIp", "dstIp", "srcPort", "dstPort"]
        },
        {
          "regex" => '^id=tos\s+time="(?<time>.*)"\s+fw.*op="CHECK\s+LINK"\s+result=-1.*dev=(?<interface>\S+)\s+no\s+linked.*$'
          "event_name" => "网口拔出"
          "event_type" => "1000065"
          "event_subtype" => "1000000215"
          "extract_fields" => ["interface"]
        },
        {
          "regex" => '(?<time>\d{4}-\d+-\d+\s+\S+)\s+Interface\s+(?<interface>\S+)\s+link\s+state\s+up.*$'
          "event_name" => "网口拔出"
          "event_type" => "1000065"
          "event_subtype" => "1000000215"
          "extract_fields" => ["interface"]
        },
        {
          "regex" => '(?<time>\d{4}-\d+-\d+\s+\S+)\s+Interface\s+(?<interface>\S+)\s+link\s+state\s+down.*$'
          "event_name" => "网口接入"
          "event_type" => "1000065"
          "event_subtype" => "1000000214"
          "extract_fields" => ["interface"]
        }
      ]
    }
  }

  # 添加通用字段
  mutate {
    replace => {
      "device_type" => "1000035"
      "product_name" => "天融信-NGFW4000"
    }
  }

  # 保存提取字段
  mutate {
    rename => {
      "dstIp" => "dest_address"
      "dstPort" => "dest_port"
      "srcIp" => "source_address"
      "interface" => "action_object_name"
      "user" => "related_object_name"
      "srcPort" => "source_port"
    }
  }
}
