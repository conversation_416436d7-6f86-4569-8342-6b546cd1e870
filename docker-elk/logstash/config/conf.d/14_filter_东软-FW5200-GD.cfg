filter {
  ruby {
    path => "/usr/share/logstash/config/conf.d/ruby/event_processor.rb"
    script_params => {
      "patterns" => [
        {
          "regex" => '^(?:<\d+>\s)?(?<Message>.*?用户\s(?<Username>\S+?)从\S+?登录成功\,\sIP为(?<SourceIP>\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}).*)$'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => ["Message", "Username", "SourceIP"]
        },
        {
          "regex" => '用户\s(?<Username>\S+?)从\S+?登录失败\,\sIP为(?<SourceIP>\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}).*$'
          "event_name" => "登录失败"
          "event_type" => "1000061"
          "event_subtype" => "1000000140"
          "extract_fields" => ["Username", "SourceIP"]
        },
        {
          "regex" => '用户\s(?<Username>\S+?)从\S+?登录失败\,\sIP为(?<SourceIP>\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}).*$'
          "event_name" => "登录失败"
          "event_type" => "1000061"
          "event_subtype" => "1000000140"
          "extract_fields" => ["Username", "SourceIP"]
        },
        {
          "regex" => '访问策略\,\s名称:\S+?,\s原始地址:(?<srcIp>\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})\((?<srcPort>\d+)\)-(?<dstIp>\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})\((?<dstPort>\d+)\)\,\s协议:(?<protocol>\S*?)\,\s地址转换:\S*?\,\s安全域:\S*?\,\s动作:drop.*$'
          "event_name" => "不符合安全策略访问"
          "event_type" => "1000065"
          "event_subtype" => "1000000241"
          "extract_fields" => ["srcIp", "srcPort", "dstIp", "dstPort", "protocol"]
        },
        {
          "regex" => '访问策略\,\s名称:\S+?,\s原始地址:(?<srcIp>\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})\((?<srcPort>\d+)\)-(?<dstIp>\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})\((?<dstPort>\d+)\)\,\s协议:(?<protocol>\S*?)\,\s地址转换:\S*?\,\s安全域:\S*?\,\s动作:accept.*$'
          "event_name" => "符合安全策略访问"
          "event_type" => "1000065"
          "event_subtype" => "1000000241"
          "extract_fields" => ["srcIp", "srcPort", "dstIp", "dstPort", "protocol"]
        },
        {
          "regex" => 'Warning FW.*?接口 (?<interface>\S+) 断开.*'
          "event_name" => "网口拔出"
          "event_type" => "1000065"
          "event_subtype" => "1000000215"
          "extract_fields" => ["interface"]
        },
        {
          "regex" => 'Warning FW.*?接口 (?<interface>\S+) 连接.*'
          "event_name" => "网口接入"
          "event_type" => "1000065"
          "event_subtype" => "1000000214"
          "extract_fields" => ["interface"]
        }
      ]
    }
  }

  # 添加通用字段
  mutate {
    replace => {
      "device_type" => "1000035"
      "product_name" => "东软-FW5200-GD"
    }
  }

  # 保存提取字段
  mutate {
    rename => {
      "dstIp" => "dest_address"
      "protocol" => "protocol"
      "SourceIP" => "source_address"
      "dstPort" => "dest_port"
      "srcIp" => "source_address"
      "interface" => "action_object_name"
      "srcPort" => "source_port"
      "Username" => "related_object_name"
    }
  }
}
