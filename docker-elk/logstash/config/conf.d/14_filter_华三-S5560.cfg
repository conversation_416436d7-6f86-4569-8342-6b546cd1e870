filter {
  ruby {
    path => "/usr/share/logstash/config/conf.d/ruby/event_processor.rb"
    script_params => {
      "patterns" => [
        {
          "regex" => '%%.*SHELL_LOGIN:.*\s+(?<Username>\S+)\s+logged\s+in\s+from\s+(?<srcIp>\S+)\..*$'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => ["Username", "srcIp"]
        },
        {
          "regex" => 'SSHS_LOG:.*Accepted\s+password\s+for\s+(?<user>\S+)\s+from\s+(?<srcIp>\S+).*$'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => ["user", "srcIp"]
        },
        {
          "regex" => 'SHELL\/\d+\/LOGIN.*(DevIP=|).*\s+Console\s+login\s+from\s+(?<srcIp>\S+).*$'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => ["srcIp"]
        },
        {
          "regex" => 'LOGINFAIL.*DevIP=\S+;\s+SSH\s+user\s+(?<Username>\S+)\s+failed\s+to\s+login\s+from\s+(?<srcIp>\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}).*$'
          "event_name" => "登录失败"
          "event_type" => "1000061"
          "event_subtype" => "1000000140"
          "extract_fields" => ["Username", "srcIp"]
        },
        {
          "regex" => 'LOGINFAIL.*DevIP=\S+;\s+AUX\s+user\s+failed\s+to\s+login\s+on\s+AUX0.*$'
          "event_name" => "登录失败"
          "event_type" => "1000061"
          "event_subtype" => "1000000140"
          "extract_fields" => []
        },
        {
          "regex" => '%%.*SSHS_LOG:\s-DevIP=\S+;\sAuthentication\sfailed\sfor\s(?<user>\S+)\sfrom\s(?<srcIp>\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3})\sport.*$'
          "event_name" => "登录失败"
          "event_type" => "1000061"
          "event_subtype" => "1000000140"
          "extract_fields" => ["user", "srcIp"]
        },
        {
          "regex" => '%%.*SHELL_LOGOUT:.*\s+(?<user>\S+)\s+logged\s+out\s+from\s+(?<srcIp>\S+)\..*$'
          "event_name" => "退出登录"
          "event_type" => "1000068"
          "event_subtype" => "1000000201"
          "extract_fields" => ["user", "srcIp"]
        },
        {
          "regex" => 'SSHS_LOG:.*User\s+(?<user>\S+)\s+logged\s+out\s+from\s+(?<srcIp>\S+)\s+port.*$'
          "event_name" => "退出登录"
          "event_type" => "1000068"
          "event_subtype" => "1000000201"
          "extract_fields" => ["user", "srcIp"]
        },
        {
          "regex" => 'LOGOUT.*DevIP=\S+;\s+Console\s+logout\s+from\s+aux0.*$'
          "event_name" => "退出登录"
          "event_type" => "1000068"
          "event_subtype" => "1000000201"
          "extract_fields" => []
        },
        {
          "regex" => '%%.*SHELL_CMD:\s-DevIP=(?<DeviceIp>\S+);\s.*-User=(?<Username>\S+);\sCommand\sis\s(?<Command>.*)$'
          "event_name" => "配置变更"
          "event_type" => "1000026"
          "event_subtype" => "1000000094"
          "extract_fields" => ["DeviceIp", "Username", "Command"]
        },
        {
          "regex" => '%%.*SHELL_CMD.*IPAddr=(?<DeviceIp>\S+)-User=(?<Username>\w+)-{0,1}.*;.*Command\si{0,1}s{0,1}\s{0,1}(?<Command>.*)$'
          "event_name" => "配置变更"
          "event_type" => "1000026"
          "event_subtype" => "1000000094"
          "extract_fields" => ["DeviceIp", "Username", "Command"]
        },
        {
          "regex" => '%%.*SHELL_CMD:\s-DevIP=(?<DeviceIp>\S+);\s.*-User=(?<Username>\S+);\sCommand\sis\s(?<Command>.*)$'
          "event_name" => "配置变更"
          "event_type" => "1000026"
          "event_subtype" => "1000000094"
          "extract_fields" => ["DeviceIp", "Username", "Command"]
        },
        {
          "regex" => '%%.*UPDOWN.*;{0,1}\s+(?<interface>\S+).*link\s+status\s+is\s+DOWN..*$'
          "event_name" => "网口拔出"
          "event_type" => "1000065"
          "event_subtype" => "1000000215"
          "extract_fields" => ["interface"]
        },
        {
          "regex" => 'interface\s+(?<interface>\S+)\s+changed\s+to\s+down.*$'
          "event_name" => "网口拔出"
          "event_type" => "1000065"
          "event_subtype" => "1000000215"
          "extract_fields" => ["interface"]
        },
        {
          "regex" => '%%.*UPDOWN:\s-DevIP=(?<devIp>\S+);\s(?<interface>\S+)\slink\sstatus\sis\sdown.*$'
          "event_name" => "网口拔出"
          "event_type" => "1000065"
          "event_subtype" => "1000000215"
          "extract_fields" => ["devIp", "interface"]
        },
        {
          "regex" => '%%.*UPDOWN.*;{0,1}\s+(?<interface>\S+).*link\s+status\s+is\s+UP..*$'
          "event_name" => "网口接入"
          "event_type" => "1000065"
          "event_subtype" => "1000000214"
          "extract_fields" => ["interface"]
        },
        {
          "regex" => '%%.*UPDOWN:\s+-DevIP=(?<devIp>\S+);\s+(?<interface>\S+)\slink\s+status\s+is\s+up.*$'
          "event_name" => "网口接入"
          "event_type" => "1000065"
          "event_subtype" => "1000000214"
          "extract_fields" => ["devIp", "interface"]
        },
        {
          "regex" => 'interface\s+(?<interface>\S+)\s+changed\s+to\s+up.*$'
          "event_name" => "网口接入"
          "event_type" => "1000065"
          "event_subtype" => "1000000214"
          "extract_fields" => ["interface"]
        }
      ]
    }
  }

  # 添加通用字段
  mutate {
    replace => {
      "device_type" => "1000079"
      "product_name" => "华三-S5560"
    }
  }

  # 保存提取字段
  mutate {
    rename => {
      "Command" => "action_details"
      "srcIp" => "source_address"
      "DeviceIp" => "device_name"
      "interface" => "action_object_name"
      "user" => "related_object_name"
      "Username" => "related_object_name"
    }
  }
}
