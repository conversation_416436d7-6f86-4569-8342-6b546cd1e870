filter {
  ruby {
    path => "/usr/share/logstash/config/conf.d/ruby/event_processor.rb"
    script_params => {
      "patterns" => [
        {
          "regex" => '%%\d+(?<lgType>\w+)/\d+/PASS\(\S+\):\s用户(?<username>.*)\(IP:(?<srcIp>\S+)\sID:\d+\)登录成功$'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => ["lgType", "username", "srcIp"]
        },
        {
          "regex" => '%%\d+(?<lgType>\w+)/\d+/FAIL\(\S+\):\s用户(?<username>.*)\(IP:(?<srcIp>\S+)\sID:\d+\)登录失败$'
          "event_name" => "登录失败"
          "event_type" => "1000061"
          "event_subtype" => "1000000140"
          "extract_fields" => ["lgType", "username", "srcIp"]
        },
        {
          "regex" => '%%\d+(?<lgType>\w+)/\d+/OUT\(\S+\):\s用户(?<username>.*)\(IP:(?<srcIp>\S+)\sID:\d+\)注销$'
          "event_name" => "退出登录"
          "event_type" => "1000068"
          "event_subtype" => "1000000201"
          "extract_fields" => ["lgType", "username", "srcIp"]
        },
        {
          "regex" => 'POLICYDENY\(\S+\):\s+协议号=\d+,\s+源地址=(?<srcIp>\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3}),\s+源端口=(?<srcPort>\d+?),\s目的地址=(?<dstIp>\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3}),\s+目的端口=(?<dstPort>\d+?),\s时间=.*?,\s+域间-.*?,\s+策略=.*$'
          "event_name" => "不符合安全策略访问"
          "event_type" => "1000065"
          "event_subtype" => "1000000241"
          "extract_fields" => ["srcIp", "srcPort", "dstIp", "dstPort"]
        },
        {
          "regex" => 'POLICYPERMIT\(\S+\):\s协议号=(?<protocol>\d+),\s源地址=(?<srcIp>\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3}),\s源端口=(?<srcPort>\d+?),\s目的地址=(?<dstIp>\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3}),\s目的端口=(?<dstport>\d+?),\s时间=.*?, 域间-.*?, 策略=(?<ply>\d+).*$'
          "event_name" => "符合安全策略访问"
          "event_type" => "1000065"
          "event_subtype" => "1000000241"
          "extract_fields" => ["protocol", "srcIp", "srcPort", "dstIp", "dstport", "ply"]
        },
        {
          "regex" => '%%\d+\w+/\d+/.*在接口(?<NetPort>\S+)上状态变为DOWN。'
          "event_name" => "网口拔出"
          "event_type" => "1000065"
          "event_subtype" => "1000000215"
          "extract_fields" => ["NetPort"]
        },
        {
          "regex" => '%%\d+\w+/\d+/.*在接口(?<NetPort>\S+)上状态变为UP。'
          "event_name" => "网口接入"
          "event_type" => "1000065"
          "event_subtype" => "1000000214"
          "extract_fields" => ["NetPort"]
        },
        {
          "regex" => '%%\d+SYSTATE/\d+/.*:\sMgmtplane\sCPU\sUsage=(?<MgmtCPU>\d+%)\sDataplane\sCPU\sUsage=(?<DataCPU>\d+%)\sMemory\sUsage=(?<MemUsg>\d+%)$'
          "event_name" => "性能监控"
          "event_type" => "1000025"
          "event_subtype" => "1000000089"
          "extract_fields" => ["MgmtCPU", "DataCPU", "MemUsg"]
        }
      ]
    }
  }

  # 添加通用字段
  mutate {
    replace => {
      "device_type" => "1000035"
      "product_name" => "华为-USG5150HSR"
    }
  }

  # 保存提取字段
  mutate {
    rename => {
      "username" => "related_object_name"
      "dstIp" => "dest_address"
      "protocol" => "protocol"
      "dstPort" => "dest_port"
      "srcIp" => "source_address"
      "srcPort" => "source_port"
      "NetPort" => "dest_port"
    }
  }
}
