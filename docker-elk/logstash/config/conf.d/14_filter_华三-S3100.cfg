filter {
  ruby {
    path => "/usr/share/logstash/config/conf.d/ruby/event_processor.rb"
    script_params => {
      "patterns" => [
        {
          "regex" => 'SHELL.*logged\s+in\s+from.*$'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => []
        },
        {
          "regex" => '%%\d+SHELL\/\d\/\S+\s\d\s-\s(?<logType>.*)\sin.*\slogin$'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => ["logType"]
        },
        {
          "regex" => '^(.*LOGIN_FAILED.*failed.*from\s+(?<srcIp>\S+).*)$'
          "event_name" => "登录失败"
          "event_type" => "1000061"
          "event_subtype" => "1000000140"
          "extract_fields" => ["srcIp"]
        },
        {
          "regex" => 'LOGIN_FAILED.*failed.*from\s+(?<interface>\S+).*$'
          "event_name" => "登录失败"
          "event_type" => "1000061"
          "event_subtype" => "1000000140"
          "extract_fields" => ["interface"]
        },
        {
          "regex" => 'SHELL.*logged\s+out\s+from.*$'
          "event_name" => "退出登录"
          "event_type" => "1000068"
          "event_subtype" => "1000000201"
          "extract_fields" => []
        },
        {
          "regex" => '%%\d+SHELL\/\d\/\S+\s\d\s-\s(?<logType>.*)\sin.*\slogout$'
          "event_name" => "退出登录"
          "event_type" => "1000068"
          "event_subtype" => "1000000201"
          "extract_fields" => ["logType"]
        },
        {
          "regex" => '%%\d+CFM\S+\s\d+\s\S+\s(?<Command>.*)\..*$'
          "event_name" => "配置变更"
          "event_type" => "1000026"
          "event_subtype" => "1000000094"
          "extract_fields" => ["Command"]
        },
        {
          "regex" => '%%.*PHY_UPDOWN.*the\s+(?<interface>\S+)\s+changed\s+to\s+down..*$'
          "event_name" => "网口拔出"
          "event_type" => "1000065"
          "event_subtype" => "1000000215"
          "extract_fields" => ["interface"]
        },
        {
          "regex" => '%%.*LINK_UPDOWN.*interface\s+(?<interface>\S+)\s+changed\s+to\s+down..*$'
          "event_name" => "网口拔出"
          "event_type" => "1000065"
          "event_subtype" => "1000000215"
          "extract_fields" => ["interface"]
        },
        {
          "regex" => '%%10L2INF/5/PORT LINK STATUS CHANGE\(l\):-\s*\d+\s*-\s*(?<interface>\S+)\s*is\s*DOWN$'
          "event_name" => "网口拔出"
          "event_type" => "1000065"
          "event_subtype" => "1000000215"
          "extract_fields" => ["interface"]
        },
        {
          "regex" => '%%.*PROTOCOL_UPDOWN.*interface\s+(?<interface>\S+)\s+changed\s+to\s+up..*$'
          "event_name" => "网口接入"
          "event_type" => "1000065"
          "event_subtype" => "1000000214"
          "extract_fields" => ["interface"]
        },
        {
          "regex" => '%%.*\s(?<interface>\S+)\sis\sUP$'
          "event_name" => "网口接入"
          "event_type" => "1000065"
          "event_subtype" => "1000000214"
          "extract_fields" => ["interface"]
        }
      ]
    }
  }

  # 添加通用字段
  mutate {
    replace => {
      "device_type" => "1000079"
      "product_name" => "华三-S3100"
    }
  }

  # 保存提取字段
  mutate {
    rename => {
      "Command" => "action_details"
      "srcIp" => "source_address"
      "interface" => "action_object_name"
    }
  }
}
