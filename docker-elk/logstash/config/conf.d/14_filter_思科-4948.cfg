filter {
  ruby {
    path => "/usr/share/logstash/config/conf.d/ruby/event_processor.rb"
    script_params => {
      "patterns" => [
        {
          "regex" => 'CHANGED.*Interface\s(?<interface>\S+),\s+changed\sstate\sto.*down.*$'
          "event_name" => "网口拔出"
          "event_type" => "1000065"
          "event_subtype" => "1000000215"
          "extract_fields" => ["interface"]
        },
        {
          "regex" => '^<\d+?>.*UPDOWN.*Interface\s(?<interface>\S+),\s+changed\sstate\sto\sdown.*$'
          "event_name" => "网口拔出"
          "event_type" => "1000065"
          "event_subtype" => "1000000215"
          "extract_fields" => ["interface"]
        },
        {
          "regex" => '^<\d+?>.*UPDOWN.*Interface\s(?<interface>\S+),\s+changed\sstate\sto\sup.*$'
          "event_name" => "网口接入"
          "event_type" => "1000065"
          "event_subtype" => "1000000214"
          "extract_fields" => ["interface"]
        }
      ]
    }
  }

  # 添加通用字段
  mutate {
    replace => {
      "device_type" => "1000079"
      "product_name" => "思科-4948"
    }
  }

  # 保存提取字段
  mutate {
    rename => {
      "interface" => "action_object_name"
    }
  }
}
