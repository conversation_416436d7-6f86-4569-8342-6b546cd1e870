filter {
  grok {
    match => {
      "message" => [
        # 登录成功-SSH
        ".*sshd[\d+]:.*\s+Accepted\s+\S+\s+for\s+(user_name:\s*|)(?<Username>\S+)\s+from\s+(source_ip:\s*|)(?<srcIp>\S+)\s+port.*$",
        
        # 登录成功-本地
        ".*\((?<loginType>(?!sshd|su|[Cc]ron|CRON).*?)(-password|-l|-welcome|):session\).*session\s+opened\s+for\s+user\s+(?<Username>\S+).*?$",
        
        # 登录失败-SSH
        ".*sshd[\d+]:.*\s+Failed\s+\S+\s+for\s+(invalid\s+user\s+|)(?<Username>\S+)\s+from\s+(?<srcIp>\S+)\s+port.*$",
        
        # 登录失败-SSH-backup
        ".*\((?<loginType>sshd)\S*:auth\).*authentication\s+failure.*rhost=+(?<srcIp>.*)\s+user=+(?<Username>\S+).*$",
        
        # 登录失败-本地
        ".*\((?<loginType>(?!sshd|su|[Cc]ron|CRON).*?)(-password|-l|-welcome|):auth\).*authentication\s+failure.*rhost=+(?<srcIp>.*)\s+user=+(?<Username>\S+).*$",
        
        # 登录退出-本地
        ".*\((?<loginType>(?!sshd|su|[Cc]ron|CRON).*?)(-password|-l|-welcome|):session\).*session\s+closed\s+for\s+user\s+(?<Username>\S+).*?$",
        
        # 登录退出-SSH-backup
        ".*sshd[\d+]:.*\s+Received\s+disconnect\s+from\s+(?<srcIp>\S+)(:|s+port).*$",
        
        # 登录退出-SSH
        ".*sshd[\d+]:.*\s+Disconnected\s+from\s+(user\s+(?<Username>\S+)\s+|)(?<srcIp>\S+)\s+port.*$",

        # USB设备拔出
        "kernel:.*(?<UsbNumber>usb\s+\S+):\s+USB\s+disconnect.+(device\s+number|address).*$",
        
        # USB设备接入 - 网络摄像头
        "kernel:.*(?<UsbNumber>usb\s+.+):\s+Product:\s+(?<deviceName>.*[Ww]ebcam.*).*?$",
        
        # USB设备接入 - 其他转接器
        "kernel:.*(?<UsbNumber>usb\s+.+):\s+Product:\s+(?<deviceName>.*(Hub|SWITCH|KVM|Rextron).*).*?$",
        
        # USB设备接入 - 其他
        "kernel:.*(?<UsbNumber>usb\s+.+):\s+Product:\s+(?<deviceName>(?!UHCI|EHCI|OHCI|xHCI|DT|LQ-\S+K|Ultra|External\s+USB|TransMemory|Elements|AX88\d+|[Bb]ack[Uu]p|.*[Kk]eyboard|.*KEYBOARD|.*[Mm]ouse|.*MOUSE|.*DVD|.*[Pp]rinter|.*USB|.*[Aa]udio|.*Officejet|.*[Ww]ebcam|.*Hub|.*SWITCH|.*KVM|.*[Dd]isk|.*DataTraveler|.*Floppy|.*CoolFlash|.*x\d+w|.*[Ss]torage|.*[Ee]xpansion).*).*?$",
        
        # USB设备接入 - 打印机
        "kernel:.*(?<UsbNumber>usb\s+.+):\s+Product:\s+(?<deviceName>.*([Pp]rinter|LQ-\S+K|Officejet).*).*?$",
        
        # USB设备接入 - 音频设备
        "kernel:.*(?<UsbNumber>usb\s+.+):\s+Product:\s+(?<deviceName>.*([Aa]udio|Ear|[Ss]ound).*).*?$",
        
        # USB设备接入 - 键盘
        "kernel:.*(?<UsbNumber>usb\s+.+):\s+Product:\s+(?<deviceName>.*([Kk]eyboard|KEYBOARD).*).*?$",
        
        # USB设备接入 - 鼠标
        "kernel:.*(?<UsbNumber>usb\s+.+):\s+Product:\s+(?<deviceName>.*([Mm]ouse|MOUSE).*).*?$",
        
        # USB设备接入 - DVD
        "kernel:.*(?<UsbNumber>usb\s+.+):\s+Product:\s+(?<deviceName>.*DVD.*).*?$",
        
        # USB设备接入 - 存储设备
        "kernel:.*(?<UsbNumber>usb\s+.+):\s+Product:\s+(?<deviceName>(DT|Ultra|External\s+USB|TransMemory|Elements|[Bb]ack[Uu]p|.*[Dd]isk|.*DataTraveler|.*Floppy|.*CoolFlash|.*x\d+w|.*[Ss]torage|.*[Ee]xpansion).*).*?$"
      ]
    }
  }

  # 添加通用字段
  mutate {
    replace => {
      "device_type" => "1000032"
      "product_name" => "Centos"
      "event_type" => "1000023"
      "event_subtype" => "1000000081"
    }
  }

  # 设置具体的事件类型
    if [message] =~ /Accepted/ {
    mutate {
      replace => { 
        "event_name" => "登录成功"
      }
    }
  } else if [message] =~ /Failed|authentication\s+failure/ {
    mutate {
      replace => { 
        "event_name" => "登录失败"
      }
    }
  } else if [message] =~ /Disconnected|disconnect/ {
    mutate {
      replace => { 
        "event_name" => "退出登录"
      }
    }
  }

  if [message] =~ /USB\s+disconnect/ {
    mutate { 
      replace => { "event_name" => "USB拔出" }
    }
  } else {
    mutate { 
      replace => { "event_name" => "USB插入" }
    }
    
    if [deviceName] =~ /[Ww]ebcam/ {
      mutate { replace => { "action_object_type" => "网络摄像头" } }
    } else if [deviceName] =~ /(Hub|SWITCH|KVM|Rextron)/ {
      mutate { replace => { "action_object_type" => "其他-转接器" } }
    } else if [deviceName] =~ /([Pp]rinter|LQ-\S+K|Officejet)/ {
      mutate { replace => { "action_object_type" => "打印机" } }
    } else if [deviceName] =~ /([Aa]udio|Ear|[Ss]ound)/ {
      mutate { replace => { "action_object_type" => "音频设备" } }
    } else if [deviceName] =~ /([Kk]eyboard|KEYBOARD)/ {
      mutate { replace => { "action_object_type" => "键盘" } }
    } else if [deviceName] =~ /([Mm]ouse|MOUSE)/ {
      mutate { replace => { "action_object_type" => "鼠标" } }
    } else if [deviceName] =~ /DVD/ {
      mutate { replace => { "action_object_type" => "DVD" } }
    } else if [deviceName] =~ /(DT|Ultra|External\s+USB|TransMemory|Elements|[Bb]ack[Uu]p|.*[Dd]isk|.*DataTraveler|.*Floppy|.*CoolFlash|.*x\d+w|.*[Ss]torage|.*[Ee]xpansion)/ {
      mutate { replace => { "action_object_type" => "存储设备" } }
    } else {
      mutate { replace => { "action_object_type" => "其他" } }
    }
  }

  # 保存提取字段
  mutate {
    rename => { 
        "UsbNumber" => "action_object_name" 
        "Username" => "related_object_account"
        "srcIp" => "related_object_address"
        "loginType" => "related_object_category"
    }
  }
}