filter {
  ruby {
    path => "/usr/share/logstash/config/conf.d/ruby/event_processor.rb"
    script_params => {
      "patterns" => [
        {
          "regex" => '^<\d+>.*date="(?<time>.*)".*from=(?<srcIp>\S+).* admin=(?<user>\S+).*msg="成功".*$'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => ["srcIp", "user"]
        },
        {
          "regex" => 'SrcIP=(?<DeviceIp>\S+)\sUserName=(?<Username>\w+)\sOperate="login"\sManageStyle=(?<logType>.*)Content="success"$'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => ["DeviceIp", "Username", "logType"]
        },
        {
          "regex" => '^\d{4}-\d{2}-\d{2}\s\d{2}:\d{2}:\d{2}.*-DevIP=(?<DeviceIp>\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3}).*user:(?<Username>\S+)\slogin from con\d+ failed'
          "event_name" => "登录失败"
          "event_type" => "1000061"
          "event_subtype" => "1000000140"
          "extract_fields" => ["DeviceIp", "Username"]
        },
        {
          "regex" => '^<\d+>.*date="(?<time>.*)".*from=(?<srcIp>\S+).* admin="(?<user>\S+)".*账号登录错误".*$'
          "event_name" => "登录失败"
          "event_type" => "1000061"
          "event_subtype" => "1000000140"
          "extract_fields" => ["srcIp", "user"]
        },
        {
          "regex" => '\s.*\sSrcIP=(?<srcIp>\S+)\sUserName=(?<user>\S+)\s.*"login"\sManageStyle=(?<logType>.*)Content="failed\sfor\suser\sname\sor\spassword\serror".*$'
          "event_name" => "登录失败"
          "event_type" => "1000061"
          "event_subtype" => "1000000140"
          "extract_fields" => ["srcIp", "user", "logType"]
        },
        {
          "regex" => '^<\d+>.*date="(?<time>.*)".*from=(?<srcIp>\S+).* admin=(?<user>\S+).*退出".*$'
          "event_name" => "退出登录"
          "event_type" => "1000068"
          "event_subtype" => "1000000201"
          "extract_fields" => ["srcIp", "user"]
        },
        {
          "regex" => '^\S+\s.*\sSrcIP=(?<srcIp>\S+)\sUserName=(?<user>\S+)\s.*ManageStyle=(?<logType>.*)\sContent="Logout"$'
          "event_name" => "退出登录"
          "event_type" => "1000068"
          "event_subtype" => "1000000201"
          "extract_fields" => ["srcIp", "user", "logType"]
        },
        {
          "regex" => 'SrcIP=(?<srcIp>.*)\s+DstIP=(?<dstIp>.*)\s+\S+\s+SrcPort=(?<srcPort>.*)\s+DstPort=(?<dstPort>\d+).*Action=DENY.*\s+policy\s+is\s+deny.*$'
          "event_name" => "不符合安全策略访问"
          "event_type" => "1000065"
          "event_subtype" => "1000000241"
          "extract_fields" => ["srcIp", "dstIp", "srcPort", "dstPort"]
        },
        {
          "regex" => '^\d{4}-\d{2}-\d{2}\s\d{2}:\d{2}:\d{2}.*?(?<src>\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3})->(?<dst>\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3})'
          "event_name" => "不符合安全策略访问"
          "event_type" => "1000065"
          "event_subtype" => "1000000241"
          "extract_fields" => ["src", "dst"]
        },
        {
          "regex" => 'Content="interface\s(?<interface>.*)\slinkdown".*$'
          "event_name" => "网口拔出"
          "event_type" => "1000065"
          "event_subtype" => "1000000215"
          "extract_fields" => ["interface"]
        },
        {
          "regex" => 'Content="interface\s(?<interface>.*)\slinkup".*$'
          "event_name" => "网口接入"
          "event_type" => "1000065"
          "event_subtype" => "1000000214"
          "extract_fields" => ["interface"]
        }
      ]
    }
  }

  # 添加通用字段
  mutate {
    replace => {
      "device_type" => "1000035"
      "product_name" => "启明星辰-USG200B"
    }
  }

  # 保存提取字段
  mutate {
    rename => {
      "dstIp" => "dest_address"
      "dstPort" => "dest_port"
      "srcIp" => "source_address"
      "DeviceIp" => "device_name"
      "interface" => "action_object_name"
      "user" => "related_object_name"
      "srcPort" => "source_port"
      "src" => "source_address"
      "Username" => "related_object_name"
    }
  }
}
