filter {
  ruby {
    path => "/usr/share/logstash/config/conf.d/ruby/event_processor.rb"
    script_params => {
      "patterns" => [
        {
          "regex" => 'GenTime="(?<time>.*?)"\sCpuUsage=(?<cpuUsage>\S+?)\sMemoryUsage=(?<memUsage>\S+?)\s.*?\sContent="System running info\.".*$'
          "event_name" => "性能监控"
          "event_type" => "1000025"
          "event_subtype" => "1000000089"
          "extract_fields" => ["cpuUsage", "memUsage"]
        }
      ]
    }
  }

  # 添加通用字段
  mutate {
    replace => {
      "device_type" => "1000035"
      "product_name" => "启明星辰-USG-820C"
    }
  }

  # 保存提取字段
  mutate {
    rename => {
      
    }
  }
}
