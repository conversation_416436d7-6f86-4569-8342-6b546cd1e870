filter {
  ruby {
    path => "/usr/share/logstash/config/conf.d/ruby/event_processor.rb"
    script_params => {
      "patterns" => [
        {
          "regex" => 'sshd\[\d+\]:.*\s+Accepted\s+\S+\s+for\s+(user_name:\s*|)(?<Username>\S+)\s+from\s+(source_ip:\s*|)(?<srcIp>\S+)\s+port.*$'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => ["Username", "srcIp"]
        },
        {
          "regex" => '\((?<loginType>(?!sshd|su|[Cc]ron|CRON).*?)(-password|-l|-welcome|):session\).*session\s+opened\s+for\s+user\s+(?<Username>\S+)\s+by\s+\(uid=\d+\)$'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => ["loginType", "Username"]
        },
        {
          "regex" => 'forwarded\s+from\s+\S+:\s+sshd\[\d+\]:\s+Failed\s+\S+\s+for\s+(invalid\s+user\s+|)(?<Username>\S+)\s+from\s+(?<srcIp>\S+)\s+port.*$'
          "event_name" => "登录失败"
          "event_type" => "1000061"
          "event_subtype" => "1000000140"
          "extract_fields" => ["Username", "srcIp"]
        },
        {
          "regex" => '\((?<loginType>sshd)\S*:auth\).*authentication\s+failure.*rhost=+(?<srcIp>.*)\s+user=+(?<Username>\S+).*'
          "event_name" => "登录失败"
          "event_type" => "1000061"
          "event_subtype" => "1000000140"
          "extract_fields" => ["loginType", "srcIp", "Username"]
        },
        {
          "regex" => '\((?<loginType>(?!sshd|su|[Cc]ron|CRON).*?)(-password|-l|-welcome|):auth\).*authentication\s+failure.*user=+(?<Username>\S+).*$'
          "event_name" => "登录失败"
          "event_type" => "1000061"
          "event_subtype" => "1000000140"
          "extract_fields" => ["loginType", "Username"]
        },
        {
          "regex" => 'Disconnected\s+from\s+user\s+(?<Username>\S+)\s+(?<srcIp>\S+)\s+port.*$'
          "event_name" => "退出登录"
          "event_type" => "1000068"
          "event_subtype" => "1000000201"
          "extract_fields" => ["Username", "srcIp"]
        },
        {
          "regex" => 'sshd\[\d+\]:.*\s+Received\s+disconnect\s+from\s+(?<srcIp>\S+)(:|\s+port).*?$'
          "event_name" => "退出登录"
          "event_type" => "1000068"
          "event_subtype" => "1000000201"
          "extract_fields" => ["srcIp"]
        },
        {
          "regex" => '\((?<loginType>(?!sshd|su|[Cc]ron|CRON).*?)(-password|-l|-welcome|):session\).*session\s+closed\s+for\s+user\s+(?<Username>\S+).*?$'
          "event_name" => "退出登录"
          "event_type" => "1000068"
          "event_subtype" => "1000000201"
          "extract_fields" => ["loginType", "Username"]
        },
        {
          "regex" => '^(\[\d+\.\d+\]\[\s*\d+\]|\s*).*usb\s+\S+:\s+USB\s+disconnect.+(device\s+number|address).*?$'
          "event_name" => "USB设备拔出"
          "event_type" => "1000061"
          "event_subtype" => "1000000250"
          "extract_fields" => []
        },
        {
          "regex" => '(?<UsbNumber>usb\s+.+):\s+Product:\s+(?<deviceName>.*(Hub|SWITCH|KVM|Rextron).*).*?$'
          "event_name" => "USB设备接入"
          "event_type" => "1000065"
          "event_subtype" => "1000000203"
          "extract_fields" => ["UsbNumber", "deviceName"]
        },
        {
          "regex" => '(?<UsbNumber>usb\s+.+):\s+Product:\s+(?<deviceName>.*([Aa]udio|Ear|[Ss]ound).*).*?$'
          "event_name" => "USB设备接入"
          "event_type" => "1000065"
          "event_subtype" => "1000000203"
          "extract_fields" => ["UsbNumber", "deviceName"]
        },
        {
          "regex" => '(?<UsbNumber>usb\s+.+):\s+Product:\s+(?<deviceName>[^\s:]+(?:\s+[^\s:]+)*).*?$'
          "event_name" => "USB设备接入"
          "event_type" => "1000065"
          "event_subtype" => "1000000203"
          "extract_fields" => ["UsbNumber", "deviceName"]
        },
        {
          "regex" => 'kernel:.*(?<UsbNumber>usb\s+.+):\s+Product:\s+(?<deviceName>.*[Ww]ebcam.*).*?$'
          "event_name" => "USB设备接入"
          "event_type" => "1000065"
          "event_subtype" => "1000000203"
          "extract_fields" => ["UsbNumber", "deviceName"]
        },
        {
          "regex" => '^(?<UsbNumber>usb\s+.+):\s+Product:\s+(?<deviceName>.*([Pp]rinter|LQ-\S+K|Officejet).*).*?$'
          "event_name" => "USB设备接入"
          "event_type" => "1000065"
          "event_subtype" => "1000000203"
          "extract_fields" => ["UsbNumber", "deviceName"]
        },
        {
          "regex" => '^(\[\d+\.\d+\])\[\s*\d+\]\s*usb\s+\S+:\s+Product:\s+(?<deviceName>.*([Kk]eyboard|KEYBOARD).*).*?$'
          "event_name" => "USB设备接入"
          "event_type" => "1000065"
          "event_subtype" => "1000000203"
          "extract_fields" => ["deviceName"]
        },
        {
          "regex" => 'kernel:.*(?<UsbNumber>usb\s+.+):\s+Product:\s+(?<deviceName>(DT|Ultra|External\s+USB|TransMemory|Elements|[Bb]ack[Uu]p|.*[Dd]isk|.*DataTraveler|.*Floppy|.*CoolFlash|.*x\d+w|.*[Ss]torage|.*[Ee]xpansion).*).*?$'
          "event_name" => "USB设备接入"
          "event_type" => "1000065"
          "event_subtype" => "1000000203"
          "extract_fields" => ["UsbNumber", "deviceName"]
        },
        {
          "regex" => '^\[.*?\]\[.*?\]\s+(?<UsbNumber>usb\s+.+):\s+Product:\s+(?<deviceName>.*DVD.*).*?$'
          "event_name" => "USB设备接入"
          "event_type" => "1000065"
          "event_subtype" => "1000000203"
          "extract_fields" => ["UsbNumber", "deviceName"]
        },
        {
          "regex" => '^\[.*\]\[.*\]\s+(?<UsbNumber>usb\s+.+):\s+Product:\s+(?<deviceName>.*([Mm]ouse|MOUSE).*).*?$'
          "event_name" => "USB设备接入"
          "event_type" => "1000065"
          "event_subtype" => "1000000203"
          "extract_fields" => ["UsbNumber", "deviceName"]
        }
      ]
    }
  }

  # 添加通用字段
  mutate {
    replace => {
      "device_type" => "1000078"
      "product_name" => "Fedora-Linux"
    }
  }

  # 保存提取字段
  mutate {
    rename => {
      "deviceName" => "device_name"
      "srcIp" => "source_address"
      "Username" => "related_object_name"
    }
  }
}
