filter {
  ruby {
    path => "/usr/share/logstash/config/conf.d/ruby/event_processor.rb"
    script_params => {
      "patterns" => [
        {
          "regex" => '^<\d+?>.*?logtype=9\s.*?from=(?<SourceIP>\S+)\s.*?admin=(?<Username>\S+)\s.*?act=登录.*?result=0.*?'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => ["SourceIP", "Username"]
        },
        {
          "regex" => 'authentication failure;\s.*?tty=(?<lgType>ssh).*rhost=(?<SourceIP>\S+).*?user=(?<Username>\S+)$'
          "event_name" => "登录失败"
          "event_type" => "1000061"
          "event_subtype" => "1000000140"
          "extract_fields" => ["lgType", "SourceIP", "Username"]
        },
        {
          "regex" => 'GenTime="(?<time>.*?)"\sEventName=(?<EventName>\S+)\s+SrcIP=(?<SrcIP>\S+)\s+DstIP=(?<DstIP>\S+)\sSrcPort=(?<SrcPort>\S+)\sDstPort=(?<DstPort>\S+).*Content=\"(?<Content>\S+.*)[^"]'
          "event_name" => "APT"
          "event_type" => "1000014"
          "event_subtype" => "1000000030"
          "extract_fields" => ["EventName", "SrcIP", "DstIP", "SrcPort", "DstPort", "Content"]
        },
        {
          "regex" => '^<\d+?>ifevent_log:\sdevid=\d+\sdate="(?<time>.*)".*?logtype=8\s.*mod=ifstatus_up.*?dsp_msg="(?<interface>\S+?)\s接口链路连接.*?".*$'
          "event_name" => "网口接入"
          "event_type" => "1000065"
          "event_subtype" => "1000000214"
          "extract_fields" => ["interface"]
        }
      ]
    }
  }

  # 添加通用字段
  mutate {
    replace => {
      "device_type" => "1000035"
      "product_name" => "启明星辰-WAF V3075"
    }
  }

  # 保存提取字段
  mutate {
    rename => {
      "SourceIP" => "source_address"
      "SrcPort" => "source_port"
      "DstIP" => "dest_address"
      "interface" => "action_object_name"
      "SrcIP" => "source_address"
      "Username" => "related_object_name"
    }
  }
}
