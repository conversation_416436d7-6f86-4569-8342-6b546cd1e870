filter {
    grok {
        match => {
            "message" => [
                "FID %{INT:temp_event_type} %{INT:temp_event_subtype} %{GREEDYDATA:event_details}"
            ]
        }
    }
    if [hostname] {
        mutate {
            replace => {
                device_name => "%{hostname}"
            }
        }
    }
    mutate {
        replace => {
            "device_type" => "1000076"
        }
    }

    if [temp_event_type] == "0" {
        mutate {
            replace => { "event_type" => "1000071" }
        }
        if [temp_event_subtype] == "3" {
            mutate {
                replace => { "event_subtype" => "1000000235" }
            }
            grok {
                match => {
                    "event_details" => [
                        "%{NUMBER:cpu_usage}%%"
                    ]
                }
            }
        } else if [temp_event_subtype] == "4" {
            mutate {
                replace => { "event_subtype" => "1000000236" }
            }
            grok {
                match => {
                    "event_details" => [
                        "%{NUMBER:memory_usage}%%"
                    ]
                }
            }
        } else if [temp_event_subtype] == "5" {
            mutate {
                replace => { "event_subtype" => "1000000243" }
            }
            grok {
                match => {
                    "event_details" => [
                        "%{DATA:related_object_name} %{GREEDYDATA:login_description}"
                    ]
                }
            }
        } else if [temp_event_subtype] == "6" {
            mutate {
                replace => { "event_subtype" => "1000000244" }
            }
        }
    } else if [temp_event_type] == "2" {
        mutate {
            replace => { "event_type" => "1000065" }
        }
        if [temp_event_subtype] == "1" {
            mutate {
                replace => { "event_subtype" => "1000000241" }
            }
            grok {
                match => {
                    "event_details" => [
                        "%{WORD:protocol} %{IP:source_address} %{NUMBER:source_port} %{IP:dest_address} %{NUMBER:dest_port}"
                    ]
                }
            }
        }
    }
}