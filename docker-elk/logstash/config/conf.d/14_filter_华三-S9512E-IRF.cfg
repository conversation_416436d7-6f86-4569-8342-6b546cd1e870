filter {
  ruby {
    path => "/usr/share/logstash/config/conf.d/ruby/event_processor.rb"
    script_params => {
      "patterns" => [
        {
          "regex" => 'LOGIN.*?Trap 1\.3\.6\.1\.4\.1\.25506\.2\.2\.1\.1\.3\.0\.1<hh3cLogIn>:(?<Username>\S+?) login from.*$'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => ["Username"]
        },
        {
          "regex" => '^(.*?LOGIN.*?:\s+(?<Username>\S+?)\slog.*from (?<DeviceIp>\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}).*)$'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => ["Username", "DeviceIp"]
        },
        {
          "regex" => 'LOGINFAIL.*?: (?<LoginType>\S+?) user (?<Username>\S+?) failed to login from (?<DeviceIp>\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}) on.*$'
          "event_name" => "登录失败"
          "event_type" => "1000061"
          "event_subtype" => "1000000140"
          "extract_fields" => ["LoginType", "Username", "DeviceIp"]
        },
        {
          "regex" => 'LOGOUT.*?: (?<LoginType>\S+?) user (?<Username>\S+?) \(IP:(?<DeviceIp>\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})\).*$'
          "event_name" => "退出登录"
          "event_type" => "1000068"
          "event_subtype" => "1000000201"
          "extract_fields" => ["LoginType", "Username", "DeviceIp"]
        },
        {
          "regex" => 'LOGOUT.*?:\s+(?<Username>\S+?)\slog.*out\sfrom\s(?<DeviceIp>\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}).*$'
          "event_name" => "退出登录"
          "event_type" => "1000068"
          "event_subtype" => "1000000201"
          "extract_fields" => ["Username", "DeviceIp"]
        },
        {
          "regex" => 'LINK\s+UPDOWN\(l\):\s+(?<interface>\S+?):\s+link\s+status\s+is\s+DOWN.*$'
          "event_name" => "网口拔出"
          "event_type" => "1000065"
          "event_subtype" => "1000000215"
          "extract_fields" => ["interface"]
        },
        {
          "regex" => '%%\d+IFNET/\d+/LINK\s+UPDOWN.*?:\s+(?<interface>\S+?):\s+link\s+status\s+is\s+UP.*$'
          "event_name" => "网口接入"
          "event_type" => "1000065"
          "event_subtype" => "1000000214"
          "extract_fields" => ["interface"]
        }
      ]
    }
  }

  # 添加通用字段
  mutate {
    replace => {
      "device_type" => "1000079"
      "product_name" => "华三-S9512E-IRF"
    }
  }

  # 保存提取字段
  mutate {
    rename => {
      "DeviceIp" => "device_name"
      "interface" => "action_object_name"
      "Username" => "related_object_name"
    }
  }
}
