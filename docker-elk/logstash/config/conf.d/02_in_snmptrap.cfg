input {
	snmptrap {
		port => 162
		id => "logstash-snmp"
		community => []
		mib_paths => "/usr/share/logstash/mibs"
		oid_mapping_format => "ruby_snmp"
		oid_map_field_values => true
		use_provided_mibs => true
		target => "snmptrap"
	}
}

filter { 
	mutate { 
		add_field => {
			source_address => "%{[host]}"
			device_address => "%{[host]}"
			device_name => "%{[host]}"
		}
	} 

    ruby {
        code => '
            snmptrap = event.get("snmptrap")
            if snmptrap.is_a?(Hash)
                details = snmptrap.map { |k, v| "#{k}: #{v}" }.join(" ||| ")
                event.set("action_details", details)
            end
        '
    }
}

