filter {
  ruby {
    path => "/usr/share/logstash/config/conf.d/ruby/event_processor.rb"
    script_params => {
      "patterns" => [
        {
          "regex" => ':(?<logType>\S+)\suser\s'(?<user>\S+)'\slogged in.*IP:\s+(?<srcIp>\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3}).*$'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => ["logType", "user", "srcIp"]
        },
        {
          "regex" => '(?<srcIp>\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3}).*:(?<logType>\S+)\s+user\s+'(?<user>\S+)'\s+logged\s+in\s+with\s+admin\s+level.*$'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => ["srcIp", "logType", "user"]
        },
        {
          "regex" => ':Failed\s(?<logType>\S+)\suser\s'(?<user>\S+)'\slogin\sattempt.*IP:\s+(?<srcIp>\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3}).*$'
          "event_name" => "登录失败"
          "event_type" => "1000061"
          "event_subtype" => "1000000140"
          "extract_fields" => ["logType", "user", "srcIp"]
        },
        {
          "regex" => '(?<srcip>\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3}).*Failed\s+Console\s+user\s+'(?<Username>\S+)'\s+login\s+attempt.*$'
          "event_name" => "登录失败"
          "event_type" => "1000061"
          "event_subtype" => "1000000140"
          "extract_fields" => ["srcip", "Username"]
        },
        {
          "regex" => ':(?<user>\S+)\slogged\sout.*$'
          "event_name" => "退出登录"
          "event_type" => "1000068"
          "event_subtype" => "1000000201"
          "extract_fields" => ["user"]
        },
        {
          "regex" => ':(?<user>\S+)\slogged\sout.*$'
          "event_name" => "退出登录"
          "event_type" => "1000068"
          "event_subtype" => "1000000201"
          "extract_fields" => ["user"]
        },
        {
          "regex" => 'RSG2300:(?<port>Port\s+\S+).*down.*$'
          "event_name" => "网口拔出"
          "event_type" => "1000065"
          "event_subtype" => "1000000215"
          "extract_fields" => ["port"]
        },
        {
          "regex" => 'RSG2300:(?<port>Port\s+\S+).*LEARNING.*$'
          "event_name" => "网口接入"
          "event_type" => "1000065"
          "event_subtype" => "1000000214"
          "extract_fields" => ["port"]
        }
      ]
    }
  }

  # 添加通用字段
  mutate {
    replace => {
      "device_type" => "1000079"
      "product_name" => "罗杰康-RSG2300NC"
    }
  }

  # 保存提取字段
  mutate {
    rename => {
      "srcip" => "source_address"
      "port" => "source_port"
      "srcIp" => "source_address"
      "user" => "related_object_name"
      "Username" => "related_object_name"
    }
  }
}
