filter {
  ruby {
    path => "/usr/share/logstash/config/conf.d/ruby/event_processor.rb"
    script_params => {
      "patterns" => [
        {
          "regex" => '%%.*LOGOUT.*succeeded\sin\slogging\sout.*UserName=(?<user>\S+),\sIp=(?<srcIp>\S+),.*$'
          "event_name" => "退出登录"
          "event_type" => "1000068"
          "event_subtype" => "1000000201"
          "extract_fields" => ["user", "srcIp"]
        },
        {
          "regex" => '%%.*DISPLAY_CMDRECORD.*Ip=(?<DeviceIp>\S+),\s.*User=(?<Username>\S+),.*\sCommand="(?<Command>.*)"\).*$'
          "event_name" => "配置变更"
          "event_type" => "1000026"
          "event_subtype" => "1000000094"
          "extract_fields" => ["DeviceIp", "Username", "Command"]
        }
      ]
    }
  }

  # 添加通用字段
  mutate {
    replace => {
      "device_type" => "1000079"
      "product_name" => "华三-S5720-56C-EI-AC"
    }
  }

  # 保存提取字段
  mutate {
    rename => {
      "Command" => "action_details"
      "srcIp" => "source_address"
      "DeviceIp" => "device_name"
      "user" => "related_object_name"
      "Username" => "related_object_name"
    }
  }
}
