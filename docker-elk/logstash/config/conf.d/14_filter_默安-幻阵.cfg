filter {
  ruby {
    path => "/usr/share/logstash/config/conf.d/ruby/event_processor.rb"
    script_params => {
      "patterns" => [
        {
          "regex" => 'asset_name":"(?<AssetName>.*)","attack_desc":"(?<AttackDesc>.*)","attack_ip":"(?<srcIp>\S+)",.*code":"(?<code>\S+)","city":"(?<city>.*)","country":"(?<country>.*)","latitude":"(?<latitude>.*)","longitude":"(?<longitude>.*)"\},.*attack_opt_type":(?<AttackOptType>\d+),"attack_time":"(?<time>\S+\s+\S+)","attack_type":"(?<AttackType>.*?)",".*vm_ip":"(?<destIp>\S+)"\}.*$'
          "event_name" => "蜜罐攻击"
          "event_type" => "1000065"
          "event_subtype" => "1000000242"
          "extract_fields" => ["AssetName", "AttackDesc", "srcIp", "code", "city", "country", "latitude", "longitude", "AttackOptType", "AttackType", "destIp"]
        }
      ]
    }
  }

  # 添加通用字段
  mutate {
    replace => {
      "device_type" => "1000032"
      "product_name" => "默安-幻阵"
    }
  }

  # 保存提取字段
  mutate {
    rename => {
      "destIp" => "dest_address"
      "srcIp" => "source_address"
      "code" => "standard_event_code"
    }
  }
}
