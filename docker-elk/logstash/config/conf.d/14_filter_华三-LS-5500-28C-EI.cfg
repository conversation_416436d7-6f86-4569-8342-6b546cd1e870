filter {
  ruby {
    path => "/usr/share/logstash/config/conf.d/ruby/event_processor.rb"
    script_params => {
      "patterns" => [
        {
          "regex" => '%%\d+SHELL/\d+/LOGIN.*?:-DevIP=(?<DeviceIp>\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3});   Trap 1\.3\.6\.1\.4\.1\.25506\.2\.2\.1\.1\.3\.0\.1<hh3cLogIn>:(?<Username>\S+?) login from .*$'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => ["DeviceId", "DeviceIp", "Username"]
        },
        {
          "regex" => 'LOGIN.*?:-DevIP=(?<srcIp>\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}); (?<LoginType>\S+?) user (?<Username>\S+?) \(IP:(?<DeviceIp>\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})\) login succeeded.*?'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => ["srcIp", "LoginType", "Username", "DeviceIp"]
        },
        {
          "regex" => 'LOGINFAIL.*?:-DevIP=(?<srcIp>\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}); (?<LoginType>\S+?) (?<Username>\S+?) failed to login from (?<DeviceIp>\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}) .*$'
          "event_name" => "登录失败"
          "event_type" => "1000061"
          "event_subtype" => "1000000140"
          "extract_fields" => ["srcIp", "LoginType", "Username", "DeviceIp"]
        },
        {
          "regex" => 'LOGOUT.*?:-DevIP=(?<srcIp>\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3});  (?<Username>\S+?) logout from (?<DeviceIp>\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}).*?$'
          "event_name" => "退出登录"
          "event_type" => "1000068"
          "event_subtype" => "1000000201"
          "extract_fields" => ["srcIp", "Username", "DeviceIp"]
        },
        {
          "regex" => 'LOGOUT.*?-DevIP=(?<srcIp>\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3});.*?hh3cLogOut\>:(?<user>\S+?) logout from.*$'
          "event_name" => "退出登录"
          "event_type" => "1000068"
          "event_subtype" => "1000000201"
          "extract_fields" => ["srcIp", "user"]
        }
      ]
    }
  }

  # 添加通用字段
  mutate {
    replace => {
      "device_type" => "1000079"
      "product_name" => "华三-LS-5500-28C-EI"
    }
  }

  # 保存提取字段
  mutate {
    rename => {
      "srcIp" => "source_address"
      "DeviceIp" => "device_name"
      "user" => "related_object_name"
      "Username" => "related_object_name"
    }
  }
}
