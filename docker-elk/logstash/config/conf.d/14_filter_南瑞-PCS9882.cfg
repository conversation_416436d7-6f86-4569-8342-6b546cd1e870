filter {
  ruby {
    path => "/usr/share/logstash/config/conf.d/ruby/event_processor.rb"
    script_params => {
      "patterns" => [
        {
          "regex" => 'USER\((?<Username>\S+)\)\slogin\sfrom\sweb\s+(?<srcIP>.*),success.*$'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => ["Username", "srcIP"]
        },
        {
          "regex" => 'USER\((?<user>\S+)\S+(?<srcIp>\S+)\)\sexit\sfrom\s(?<logType>\S+).*?$'
          "event_name" => "退出登录"
          "event_type" => "1000068"
          "event_subtype" => "1000000201"
          "extract_fields" => ["user", "srcIp", "logType"]
        }
      ]
    }
  }

  # 添加通用字段
  mutate {
    replace => {
      "device_type" => "1000079"
      "product_name" => "南瑞-PCS9882"
    }
  }

  # 保存提取字段
  mutate {
    rename => {
      "srcIp" => "source_address"
      "user" => "related_object_name"
      "Username" => "related_object_name"
    }
  }
}
