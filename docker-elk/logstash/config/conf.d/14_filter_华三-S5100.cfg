filter {
  ruby {
    path => "/usr/share/logstash/config/conf.d/ruby/event_processor.rb"
    script_params => {
      "patterns" => [
        {
          "regex" => 'LOGIN.*?:-DevIP=(?<DeviceIp>\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})- \d+ - (?<Username>\S+?)\(.*?in.*?login.*$'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => ["DeviceIp", "Username"]
        },
        {
          "regex" => 'LOGOUT.*?:-DevIP=(?<DeviceIp>\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})- \d+ - (?<Username>\S+?)\(.*?in.*?logout.*$'
          "event_name" => "退出登录"
          "event_type" => "1000068"
          "event_subtype" => "1000000201"
          "extract_fields" => ["DeviceIp", "Username"]
        },
        {
          "regex" => 'PORT LINK STATUS CHANGE.*?:-DevIP=(?<DeviceIp>\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})-.*?Trap 1\.3\.6\.1\.6\.3\.1\.1\.5\.3\(linkDown\): portIndex is (?<PortIndex>\S+?),.*$'
          "event_name" => "网口拔出"
          "event_type" => "1000065"
          "event_subtype" => "1000000215"
          "extract_fields" => ["DeviceIp", "PortIndex"]
        },
        {
          "regex" => 'PORT LINK STATUS CHANGE.*?-DevIP=(?<DeviceIp>\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})- \d+ -  (?<interface>\S+?) is DOWN.*$'
          "event_name" => "网口拔出"
          "event_type" => "1000065"
          "event_subtype" => "1000000215"
          "extract_fields" => ["DeviceIp", "interface"]
        },
        {
          "regex" => 'PORT LINK STATUS CHANGE.*?-DevIP=(?<DeviceIp>\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})- \d+ -  (?<interface>\S+?) is UP.*$'
          "event_name" => "网口接入"
          "event_type" => "1000065"
          "event_subtype" => "1000000214"
          "extract_fields" => ["DeviceIp", "interface"]
        }
      ]
    }
  }

  # 添加通用字段
  mutate {
    replace => {
      "device_type" => "1000079"
      "product_name" => "华三-S5100"
    }
  }

  # 保存提取字段
  mutate {
    rename => {
      "DeviceIp" => "device_name"
      "interface" => "action_object_name"
      "Username" => "related_object_name"
    }
  }
}
