filter {
  ruby {
    path => "/usr/share/logstash/config/conf.d/ruby/event_processor.rb"
    script_params => {
      "patterns" => [
        {
          "regex" => '(?<Username>\S+)\slogged\sin\sfrom\s+(?<ip>\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})\son\svty\s+\d.*$'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => ["Username", "ip"]
        },
        {
          "regex" => '\s+(?<Username>\S+)\s+\S+\s+\S+\(\S+\s+(?<ip>\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})\).*?$'
          "event_name" => "登录失败"
          "event_type" => "1000061"
          "event_subtype" => "1000000140"
          "extract_fields" => ["Username", "ip"]
        },
        {
          "regex" => '^(?<Action>User)\s+(?<Username>\S+)\slogouted\sfrom\s+(?<ip>\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})\son\svty\s+\d.*$'
          "event_name" => "退出登录"
          "event_type" => "1000068"
          "event_subtype" => "1000000201"
          "extract_fields" => ["Action", "Username", "ip"]
        },
        {
          "regex" => 'Line.*Interface\s+(?<interface>\S+),\s+changed\s+state\s+to\s+down.*$'
          "event_name" => "网口拔出"
          "event_type" => "1000065"
          "event_subtype" => "1000000215"
          "extract_fields" => ["interface"]
        },
        {
          "regex" => 'Line.*Interface\s+(?<interface>\S+),\s+changed\s+state\s+to\s+up.*$'
          "event_name" => "网口接入"
          "event_type" => "1000065"
          "event_subtype" => "1000000214"
          "extract_fields" => ["interface"]
        }
      ]
    }
  }

  # 添加通用字段
  mutate {
    replace => {
      "device_type" => "1000079"
      "product_name" => "上海博达-BDCOM-IES3328M"
    }
  }

  # 保存提取字段
  mutate {
    rename => {
      "Action" => "action"
      "interface" => "action_object_name"
      "Username" => "related_object_name"
    }
  }
}
