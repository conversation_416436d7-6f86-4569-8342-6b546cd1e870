filter {
    mutate {
      replace => { 
        collector_id => "${collector_id}"
        collector_name => "${collector_name}"
        credibility => "50"
        event_name => "通用事件"
        original_level => "0"
        device_type => "1000046"
      }
    }
    if [event][severity] {
      translate {
        source => "[event][severity]"
        target => "[severity_level]"
        dictionary => {
          "0" => 4
          "1" => 4
          "2" => 3
          "3" => 2
          "4" => 1
          "5" => 0
          "6" => 0
          "7" => 0
        }
        fallback => 0
      }
    } else {
      mutate { 
        replace => {
          severity_level => 0
        }
      } 
    }

    ruby {
        code => "now = Time.now.utc; event.set('day_time', now.hour * 60 + now.min); event.set('received_time', LogStash::Timestamp.new)"
    }
}