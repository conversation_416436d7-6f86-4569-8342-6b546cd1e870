filter {
  ruby {
    path => "/usr/share/logstash/config/conf.d/ruby/event_processor.rb"
    script_params => {
      "patterns" => [
        {
          "regex" => 'LOGIN.*succeeded\s+in\s+logging\s+in.*UserType=\[STRING\],\s*UserName=(?<user>\S*),\s*Ip=(?<srcIp>\S*).*?$'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => ["user", "srcIp"]
        },
        {
          "regex" => 'LOGIN.*?succeeded in logging in to \S+?\.\s\(UserType=(?<LoginType>.*?)\,\sUserName=(?<username>.*?),(\sAuthenticationMethod=\".*?\"\,|)\sIp=(?<srcIp>\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})\,\sVpnName=.*\).*$'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => ["LoginType", "username", "srcIp"]
        },
        {
          "regex" => 'LOGINFAILED.*?Failed\s+to\s+login\.\s\(Ip=(?<srcIp>\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})\,\sUserName=(?<username>.*)\,\sTimes=\d+?\,\sAccessType=(?<LoginType>.+?)\).*$'
          "event_name" => "登录失败"
          "event_type" => "1000061"
          "event_subtype" => "1000000140"
          "extract_fields" => ["srcIp", "username", "LoginType"]
        },
        {
          "regex" => '%%\d+SSH.*Failed\sto\slogin\s+through\s+(?<loginType>\S+).*\(IP=(?<srcIp>\S+),\sUserName=(?<user>\S+),.*$'
          "event_name" => "登录失败"
          "event_type" => "1000061"
          "event_subtype" => "1000000140"
          "extract_fields" => ["loginType", "srcIp", "user"]
        },
        {
          "regex" => 'SSH_FAIL.*Failed\s+to\s+login\s+through.*IP=(?<srcIp>\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3}),.*UserName=(?<user>\S+),.*$'
          "event_name" => "登录失败"
          "event_type" => "1000061"
          "event_subtype" => "1000000140"
          "extract_fields" => ["srcIp", "user"]
        },
        {
          "regex" => 'LOGINFAILED.*:Failed\s+to\s+login.*Ip=(?<srcIp>\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3}).*UserName=(?<user>\S+),.*AccessType=(?<logType>\S+),.*$'
          "event_name" => "登录失败"
          "event_type" => "1000061"
          "event_subtype" => "1000000140"
          "extract_fields" => ["srcIp", "user", "logType"]
        },
        {
          "regex" => 'SSH_CONNECT_CLOSED.*:SSH\s+connect\s+was\s+closed.*IP=(?<srcIp>\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3}).*UserName=(?<user>\S+).*$'
          "event_name" => "退出登录"
          "event_type" => "1000068"
          "event_subtype" => "1000000201"
          "extract_fields" => ["srcIp", "user"]
        },
        {
          "regex" => 'LOGOUT.*succeeded\s+in\s+logging\s+out.*UserType=\[STRING\],\s*UserName=(?<user>\S*),\s*Ip=(?<srcIp>\S*).*?$'
          "event_name" => "退出登录"
          "event_type" => "1000068"
          "event_subtype" => "1000000201"
          "extract_fields" => ["user", "srcIp"]
        },
        {
          "regex" => 'LOGOUT.*succeeded\s+in\s+logging\s+out.*UserType=(?<logType>\S+),.*UserName=(?<user>\S+),.*Ip=(?<srcIp>\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3}).*$'
          "event_name" => "退出登录"
          "event_type" => "1000068"
          "event_subtype" => "1000000201"
          "extract_fields" => ["logType", "user", "srcIp"]
        },
        {
          "regex" => 'CMDRECORD.*:Record.*command\s+information.*IP=(?<srcIp>\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3}).*User=(?<user>\S+),.*Command="(?<Command>.*)".*$'
          "event_name" => "配置变更"
          "event_type" => "1000026"
          "event_subtype" => "1000000094"
          "extract_fields" => ["srcIp", "user", "Command"]
        },
        {
          "regex" => 'LINK_STATE.*:.*interface\s+(?<interface>\S+).*has\s+entered\s+the\s+DOWN\s+state.*$'
          "event_name" => "网口拔出"
          "event_type" => "1000065"
          "event_subtype" => "1000000215"
          "extract_fields" => ["interface"]
        },
        {
          "regex" => 'IF_STATE.*?Interface\s(?<interface>\S+?)\shas\sturned\sinto\sDOWN\sstate.*$'
          "event_name" => "网口拔出"
          "event_type" => "1000065"
          "event_subtype" => "1000000215"
          "extract_fields" => ["interface"]
        },
        {
          "regex" => 'LINK_STATE.*:.*interface\s+(?<interface>\S+).*has\s+entered\s+the\s+UP\s+state.*$'
          "event_name" => "网口接入"
          "event_type" => "1000065"
          "event_subtype" => "1000000214"
          "extract_fields" => ["interface"]
        },
        {
          "regex" => 'IF_STATE.*?Interface\s(?<interface>\S+?)\shas\sturned\sinto\sUP\sstate.*$'
          "event_name" => "网口接入"
          "event_type" => "1000065"
          "event_subtype" => "1000000214"
          "extract_fields" => ["interface"]
        }
      ]
    }
  }

  # 添加通用字段
  mutate {
    replace => {
      "device_type" => "1000079"
      "product_name" => "华为-S5700"
    }
  }

  # 保存提取字段
  mutate {
    rename => {
      "username" => "related_object_name"
      "Command" => "action_details"
      "srcIp" => "source_address"
      "interface" => "action_object_name"
      "user" => "related_object_name"
    }
  }
}
