filter {
  ruby {
    path => "/usr/share/logstash/config/conf.d/ruby/event_processor.rb"
    script_params => {
      "patterns" => [
        {
          "regex" => '%ASA-2-106001.*?from (?<source_address>\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3})/(?<SrcPort>\d{1,5}).*?to (?<dest_address>\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3})/(?<DestPort>\d{1,5}).*?$'
          "event_name" => "不符合安全策略访问"
          "event_type" => "1000065"
          "event_subtype" => "1000000241"
          "extract_fields" => ["source_address", "SrcPort", "dest_address", "DestPort"]
        },
        {
          "regex" => '%ASA-4-313004.*?from laddr (?<source_address>\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3}).*?to (?<dest_address>\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3}).*?$'
          "event_name" => "不符合安全策略访问"
          "event_type" => "1000065"
          "event_subtype" => "1000000241"
          "extract_fields" => ["source_address", "dest_address"]
        }
      ]
    }
  }

  # 添加通用字段
  mutate {
    replace => {
      "device_type" => "1000035"
      "product_name" => "思科-ASA5550"
    }
  }

  # 保存提取字段
  mutate {
    rename => {
      "SrcPort" => "source_port"
      "DestPort" => "dest_port"
    }
  }
}
