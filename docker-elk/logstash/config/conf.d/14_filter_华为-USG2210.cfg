filter {
  ruby {
    path => "/usr/share/logstash/config/conf.d/ruby/event_processor.rb"
    script_params => {
      "patterns" => [
        {
          "regex" => '%%\d+(?<lgType>\w+)/\d+/LOGIN.*v.*user:(?<user>\S+)\s+login\s+from.*$'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => ["lgType", "user"]
        },
        {
          "regex" => 'User\s+(?<Username>\S+)\(IP:(?<DeviceIp>\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3}) ID:\d+\)\s+login\s+succeeded.*$'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => ["Username", "DeviceIp"]
        },
        {
          "regex" => 'user:(?<Username>\S+)\s+login\s+from\s+con\d+.*$'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => ["Username"]
        },
        {
          "regex" => 'user:(?<Username>\S+)\s+login\s+from\s+(?<srcIp>\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})$'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => ["Username", "srcIp"]
        },
        {
          "regex" => 'login\s+failed\s+from.*$'
          "event_name" => "登录失败"
          "event_type" => "1000061"
          "event_subtype" => "1000000140"
          "extract_fields" => []
        },
        {
          "regex" => 'User\s+(?<Username>\S+)\(IP:(?<srcIp>\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})\s+ID:\d+\)\s+login\s+failed.*$'
          "event_name" => "登录失败"
          "event_type" => "1000061"
          "event_subtype" => "1000000140"
          "extract_fields" => ["Username", "srcIp"]
        },
        {
          "regex" => 'user:(?<Username>\S+)\s+login\s+from\s+(?<srcIp>\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3})\s+failed,\s+the\s+reason\s+:(?<msg>.*)$'
          "event_name" => "登录失败"
          "event_type" => "1000061"
          "event_subtype" => "1000000140"
          "extract_fields" => ["Username", "srcIp", "msg"]
        },
        {
          "regex" => 'user:(?<Username>\S+)\s+login\s+from\s+con\d+\s+failed,\s+the\s+reason\s+:(?<msg>.*)$'
          "event_name" => "登录失败"
          "event_type" => "1000061"
          "event_subtype" => "1000000140"
          "extract_fields" => ["Username", "msg"]
        },
        {
          "regex" => 'User\s+(?<Username>\S+)\(IP:(?<DeviceIp>\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})\s+ID:\d+\)\s+logout.*$'
          "event_name" => "退出登录"
          "event_type" => "1000068"
          "event_subtype" => "1000000201"
          "extract_fields" => ["Username", "DeviceIp"]
        },
        {
          "regex" => 'user:(?<Username>\S+)\s+logout\s+from\s+con\d+.*$'
          "event_name" => "退出登录"
          "event_type" => "1000068"
          "event_subtype" => "1000000201"
          "extract_fields" => ["Username"]
        },
        {
          "regex" => 'user:(?<Username>\S+)\s+logout\s+from\s+(?<srcIp>\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3}).*$'
          "event_name" => "退出登录"
          "event_type" => "1000068"
          "event_subtype" => "1000000201"
          "extract_fields" => ["Username", "srcIp"]
        },
        {
          "regex" => 'user:(?<user>\S+)\s+logout\s+from.*$'
          "event_name" => "退出登录"
          "event_type" => "1000068"
          "event_subtype" => "1000000201"
          "extract_fields" => ["user"]
        },
        {
          "regex" => '-DevIP=(?<DeviceIp>\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3}).*user:(?<Username>\S+).*command:(?<Command>.*)'
          "event_name" => "配置变更"
          "event_type" => "1000026"
          "event_subtype" => "1000000094"
          "extract_fields" => ["DeviceIp", "Username", "Command"]
        },
        {
          "regex" => 'deny\sicmp\s(?<srcIP>\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})->(?<dstIP>\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}).*$'
          "event_name" => "不符合安全策略访问"
          "event_type" => "1000065"
          "event_subtype" => "1000000241"
          "extract_fields" => ["srcIP", "dstIP"]
        },
        {
          "regex" => '(?<interface>\S+)\s+changed\s+status\s+to\s+down.*$'
          "event_name" => "网口拔出"
          "event_type" => "1000065"
          "event_subtype" => "1000000215"
          "extract_fields" => ["interface"]
        },
        {
          "regex" => 'Port\s+(?<interface>\S+)\s+was\s+changed\s+to\s+DOWN.*$'
          "event_name" => "网口拔出"
          "event_type" => "1000065"
          "event_subtype" => "1000000215"
          "extract_fields" => ["interface"]
        },
        {
          "regex" => 'Interface\s+(?<interface>\S+).*turned\s+into\s+DOWN.*$'
          "event_name" => "网口拔出"
          "event_type" => "1000065"
          "event_subtype" => "1000000215"
          "extract_fields" => ["interface"]
        },
        {
          "regex" => '^(?<interface>\S+)\s+changed\s+status\s+to\s+up.*$'
          "event_name" => "网口接入"
          "event_type" => "1000065"
          "event_subtype" => "1000000214"
          "extract_fields" => ["interface"]
        },
        {
          "regex" => 'Interface\s+(?<interface>\S+).*turned\s+into\s+UP.*$'
          "event_name" => "网口接入"
          "event_type" => "1000065"
          "event_subtype" => "1000000214"
          "extract_fields" => ["interface"]
        }
      ]
    }
  }

  # 添加通用字段
  mutate {
    replace => {
      "device_type" => "1000035"
      "product_name" => "华为-USG2210"
    }
  }

  # 保存提取字段
  mutate {
    rename => {
      "dstIP" => "dest_address"
      "Command" => "action_details"
      "srcIp" => "source_address"
      "DeviceIp" => "device_name"
      "interface" => "action_object_name"
      "user" => "related_object_name"
      "Username" => "related_object_name"
    }
  }
}
