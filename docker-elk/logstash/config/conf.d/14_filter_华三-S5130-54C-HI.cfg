filter {
  ruby {
    path => "/usr/share/logstash/config/conf.d/ruby/event_processor.rb"
    script_params => {
      "patterns" => [
        {
          "regex" => '%%.*SSHS_CONNECT:\s+-DevIP=\S+;\sSSH\suser\s(?<user>\S+)\s\(IP:\s(?<srcIp>\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3})\).*\ssuccessfully.*$'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => ["user", "srcIp"]
        },
        {
          "regex" => '%%.*SSHS_DISCONNECT:\s+-DevIP=\S+;\sSSH\suser\s(?<user>\S+)\s\(IP:\s(?<srcIp>\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3})\)\s+disconnected.*$'
          "event_name" => "退出登录"
          "event_type" => "1000068"
          "event_subtype" => "1000000201"
          "extract_fields" => ["user", "srcIp"]
        },
        {
          "regex" => '%%.*PHY_UPDOWN.*-DevIP=(?<devIp>\S+);\s+(?<interface>\S+).*down..*$'
          "event_name" => "网口拔出"
          "event_type" => "1000065"
          "event_subtype" => "1000000215"
          "extract_fields" => ["devIp", "interface"]
        },
        {
          "regex" => '%%.*PHY_UPDOWN.*-DevIP=(?<devIp>\S+);\s+(?<interface>\S+).*up.*$'
          "event_name" => "网口接入"
          "event_type" => "1000065"
          "event_subtype" => "1000000214"
          "extract_fields" => ["devIp", "interface"]
        }
      ]
    }
  }

  # 添加通用字段
  mutate {
    replace => {
      "device_type" => "1000079"
      "product_name" => "华三-S5130-54C-HI"
    }
  }

  # 保存提取字段
  mutate {
    rename => {
      "srcIp" => "source_address"
      "interface" => "action_object_name"
      "user" => "related_object_name"
    }
  }
}
