filter {
  ruby {
    path => "/usr/share/logstash/config/conf.d/ruby/event_processor.rb"
    script_params => {
      "patterns" => [
        {
          "regex" => 'LOGIN.*?:\s+-\s{0,1}DevIP=(?<srcIp>\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3});\s+(?<Username>\S+?)\s+logged\s+in\s+from\s+(?<DeviceIp>\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}).*$'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => ["srcIp", "Username", "DeviceIp"]
        },
        {
          "regex" => 'LOGINFAIL.*?: -DevIP=(?<srcIp>\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}); (?<LoginType>\S+?) user (?<Username>\S+?) failed to log in from (?<DeviceIp>\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}) on.*$'
          "event_name" => "登录失败"
          "event_type" => "1000061"
          "event_subtype" => "1000000140"
          "extract_fields" => ["srcIp", "LoginType", "Username", "DeviceIp"]
        },
        {
          "regex" => 'SHELL_LOGOUT.*?: -DevIP=(?<srcIp>\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}); (?<Username>\S+?) logged out from (?<DeviceIp>\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}).*$'
          "event_name" => "退出登录"
          "event_type" => "1000068"
          "event_subtype" => "1000000201"
          "extract_fields" => ["srcIp", "Username", "DeviceIp"]
        },
        {
          "regex" => '%%.*LOGOUT.*logout.*$'
          "event_name" => "退出登录"
          "event_type" => "1000068"
          "event_subtype" => "1000000201"
          "extract_fields" => []
        },
        {
          "regex" => 'interface\s+(?<interface>\S+)\s+is\s+down..*$'
          "event_name" => "网口拔出"
          "event_type" => "1000065"
          "event_subtype" => "1000000215"
          "extract_fields" => ["interface"]
        },
        {
          "regex" => 'interface\s+(?<interface>\S+)\s+is\s+up..*$'
          "event_name" => "网口接入"
          "event_type" => "1000065"
          "event_subtype" => "1000000214"
          "extract_fields" => ["interface"]
        }
      ]
    }
  }

  # 添加通用字段
  mutate {
    replace => {
      "device_type" => "1000079"
      "product_name" => "华三-S7503E-S"
    }
  }

  # 保存提取字段
  mutate {
    rename => {
      "srcIp" => "source_address"
      "DeviceIp" => "device_name"
      "interface" => "action_object_name"
      "Username" => "related_object_name"
    }
  }
}
