filter {
    grok {
        match => {
            "message" => [
                "AV %{INT:temp_event_type} %{GREEDYDATA:event_details}"
            ]
        }
    }
    if [hostname] {
        mutate {
            replace => {
                device_name => "%{hostname}"
            }
        }
    }
    mutate {
        replace => {
            "device_type" => "1000041"
        }
    }

    # 设置事件类型和子类型
    if [temp_event_type] == "0" {
        mutate {
            replace => { "event_type" => "1000072" }
            replace => { "event_subtype" => "1000000248" }
        }
    }

}