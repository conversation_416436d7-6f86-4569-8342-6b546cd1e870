filter {
  ruby {
    path => "/usr/share/logstash/config/conf.d/ruby/event_processor.rb"
    script_params => {
      "patterns" => [
        {
          "regex" => 'LOGIN.*?:\s+(?<Username>\S+?)\slogged\s+in\s+from.*$'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => ["Username"]
        },
        {
          "regex" => '^Syslog message:\s\S+?\s(?<time>\S+?\s\d{1,2}\s\d{2}:\d{2}:\d{2}\s\d{4}).*?SHELL_LOGIN: -DevIP=(?<DeviceIp>\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}); (?<Username>\S+?) logged in from.*$'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => ["DeviceIp", "Username"]
        },
        {
          "regex" => '^Syslog message:\s\S+?\s(?<time>\S+?\s\d{1,2}\s\d{2}:\d{2}:\d{2}\s\d{4}).*?LOGIN_FAILED: -DevIP=(?<DeviceIp>\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}); (?<Username>\S+?) failed to log in from.*$'
          "event_name" => "登录失败"
          "event_type" => "1000061"
          "event_subtype" => "1000000140"
          "extract_fields" => ["DeviceIp", "Username"]
        },
        {
          "regex" => 'LOGOUT.*?:\s+(?<username>.*)\slogged\s+out\s+from.*$'
          "event_name" => "退出登录"
          "event_type" => "1000068"
          "event_subtype" => "1000000201"
          "extract_fields" => ["username"]
        },
        {
          "regex" => '^Syslog message:\s\S+?\s(?<time>\S+?\s\d{1,2}\s\d{2}:\d{2}:\d{2}\s\d{4}).*?SHELL_LOGOUT: -DevIP=(?<DeviceIp>\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}); (?<Username>\S+?) logged out from.*$'
          "event_name" => "退出登录"
          "event_type" => "1000068"
          "event_subtype" => "1000000201"
          "extract_fields" => ["DeviceIp", "Username"]
        },
        {
          "regex" => '^Syslog message:\s\S+?\s(?<time>\S+?\s\d{1,2}\s\d{2}:\d{2}:\d{2}\s\d{4}).*?PHY_UPDOWN: -DevIP=(?<DeviceIp>\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}); Physical state on the interface (?<interface>\S+?) changed to down.*$'
          "event_name" => "网口拔出"
          "event_type" => "1000065"
          "event_subtype" => "1000000215"
          "extract_fields" => ["DeviceIp", "interface"]
        },
        {
          "regex" => '^Syslog message:\s\S+?\s(?<time>\S+?\s\d{1,2}\s\d{2}:\d{2}:\d{2}\s\d{4}).*?PHY_UPDOWN: -DevIP=(?<DeviceIp>\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}); Physical state on the interface (?<interface>\S+?) changed to up.*$'
          "event_name" => "网口接入"
          "event_type" => "1000065"
          "event_subtype" => "1000000214"
          "extract_fields" => ["DeviceIp", "interface"]
        }
      ]
    }
  }

  # 添加通用字段
  mutate {
    replace => {
      "device_type" => "1000079"
      "product_name" => "华三-LS-5130S-28S-HI"
    }
  }

  # 保存提取字段
  mutate {
    rename => {
      "username" => "related_object_name"
      "DeviceIp" => "device_name"
      "interface" => "action_object_name"
      "Username" => "related_object_name"
    }
  }
}
