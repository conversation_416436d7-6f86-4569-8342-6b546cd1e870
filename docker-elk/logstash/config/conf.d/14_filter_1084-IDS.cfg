filter {
    grok {
        match => {
            "message" => [
                "IDS %{INT:temp_event_type} %{GREEDYDATA:event_details}"
            ]
        }
    }

    if [hostname] {
        mutate {
            replace => {
                device_name => "%{hostname}"
            }
        }
    }
    mutate {
        replace => {
            "device_type" => "1000037"
        }
    }

    # 设置事件类型和子类型
    if [temp_event_type] == "0" {
        mutate {
            replace => { 
                "event_type" => "1000073" 
                "event_subtype" => "1000000249"
            }
        }
        # 解析事件详情
        grok {
            match => {
                "event_details" => [
                    "%{GREEDYDATA:action_details} %{IP:source_address} %{NUMBER:source_port} %{IP:dest_address} %{NUMBER:dest_port}"
                ]
            }
        }
    }else if [temp_event_type] == "1" {
        mutate {
            replace => { "event_type" => "1000071" }
        }
        if [temp_event_subtype] == "1" {
            mutate {
                replace => { "event_subtype" => "1000000235" }
            }
            # CPU 利用率
            grok {
                match => {
                    "event_details" => "%{NUMBER:cpu_usage}%%"
                }
            }
        } else if [temp_event_subtype] == "2" {
            mutate {
                replace => { "event_subtype" => "1000000236" }
            }
            # 内存使用率
            grok {
                match => {
                    "event_details" => "%{NUMBER:memory_usage}%%"
                }
            }
        }
    }
}