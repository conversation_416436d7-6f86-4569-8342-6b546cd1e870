filter {
  ruby {
    path => "/usr/share/logstash/config/conf.d/ruby/event_processor.rb"
    script_params => {
      "patterns" => [
        {
          "regex" => '^%.*:\s+Login\s+Success\s+\[user:\s+(?<user>\S+)\]\s+\[Source:\s+(?<srcIp>\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3})\].*$'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => ["user", "srcIp"]
        },
        {
          "regex" => '^%.*:\s+Still\s+timeleft\s+for\s+watching\s+failures.*\[user:\s+(?<user>\S+)\]\s+\[Source:\s{0,1}(?<srcIp>\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})\].*$'
          "event_name" => "登录失败"
          "event_type" => "1000061"
          "event_subtype" => "1000000140"
          "extract_fields" => ["user", "srcIp"]
        },
        {
          "regex" => '^%.*:\s+Login\s+failed\s+\[user:\s+(?<user>\S+)\]\s+\[Source:\s+(?<srcIp>\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})\].*$'
          "event_name" => "登录失败"
          "event_type" => "1000061"
          "event_subtype" => "1000000140"
          "extract_fields" => ["user", "srcIp"]
        },
        {
          "regex" => 'Configured\s+from.*by\s+(?<user>\S+).*\((?<srcIp>\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}).*$'
          "event_name" => "配置变更"
          "event_type" => "1000026"
          "event_subtype" => "1000000094"
          "extract_fields" => ["user", "srcIp"]
        },
        {
          "regex" => '^%LINEPROTO-5-UPDOWN:\s+Line\s+protocol\s+on\s+Interface\s+(?<interface>\S+),.*to\s+down.*$'
          "event_name" => "网口拔出"
          "event_type" => "1000065"
          "event_subtype" => "1000000215"
          "extract_fields" => ["interface"]
        },
        {
          "regex" => '^%.*CHANGED:.*\s+Interface\s+(?<interface>\S+),.*to.*down.*$'
          "event_name" => "网口拔出"
          "event_type" => "1000065"
          "event_subtype" => "1000000215"
          "extract_fields" => ["interface"]
        },
        {
          "regex" => '^%.*UPDOWN:.*\s+Interface\s+(?<interface>\S+),.*to\s+down.*$'
          "event_name" => "网口拔出"
          "event_type" => "1000065"
          "event_subtype" => "1000000215"
          "extract_fields" => ["interface"]
        },
        {
          "regex" => '^%.*UPDOWN:\s+Line\s+protocol\s+on\s+Interface\s+(?<interface>\S+),.*to\s+up.*$'
          "event_name" => "网口接入"
          "event_type" => "1000065"
          "event_subtype" => "1000000214"
          "extract_fields" => ["interface"]
        },
        {
          "regex" => '%.*UPDOWN:\s+Interface\s+(?<interface>\S+),.*to\s+up.*$'
          "event_name" => "网口接入"
          "event_type" => "1000065"
          "event_subtype" => "1000000214"
          "extract_fields" => ["interface"]
        }
      ]
    }
  }

  # 添加通用字段
  mutate {
    replace => {
      "device_type" => "1000079"
      "product_name" => "思科-C3550"
    }
  }

  # 保存提取字段
  mutate {
    rename => {
      "interface" => "action_object_name"
      "user" => "related_object_name"
      "srcIp" => "source_address"
    }
  }
}
