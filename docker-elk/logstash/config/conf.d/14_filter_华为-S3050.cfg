filter {
  ruby {
    path => "/usr/share/logstash/config/conf.d/ruby/event_processor.rb"
    script_params => {
      "patterns" => [
        {
          "regex" => 'SHELL.*LOGIN.*Console\s+login\s+from\s+(?<srcIp>.*).*$'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => ["srcIp"]
        },
        {
          "regex" => 'SHELL.*LOGOUT.*Console\s+logout\s+from\s+(?<srcIp>.*).*$'
          "event_name" => "退出登录"
          "event_type" => "1000068"
          "event_subtype" => "1000000201"
          "extract_fields" => ["srcIp"]
        },
        {
          "regex" => 'PORT\s+LINK\s+STATUS\s+CHANGE.*:\s+(?<interface>.*):\s+turns\s+into\s+DOWN\s+state.*$'
          "event_name" => "网口拔出"
          "event_type" => "1000065"
          "event_subtype" => "1000000215"
          "extract_fields" => ["interface"]
        },
        {
          "regex" => 'PORT\s+LINK\s+STATUS\s+CHANGE.*:\s+(?<interface>.*):\s+turns\s+into\s+UP\s+state.*$'
          "event_name" => "网口接入"
          "event_type" => "1000065"
          "event_subtype" => "1000000214"
          "extract_fields" => ["interface"]
        }
      ]
    }
  }

  # 添加通用字段
  mutate {
    replace => {
      "device_type" => "1000079"
      "product_name" => "华为-S3050"
    }
  }

  # 保存提取字段
  mutate {
    rename => {
      "interface" => "action_object_name"
      "srcIp" => "source_address"
    }
  }
}
