filter {
  ruby {
    path => "/usr/share/logstash/config/conf.d/ruby/event_processor.rb"
    script_params => {
      "patterns" => [
        {
          "regex" => '^SerialNum=(?<SerialNum>\S+?)\sGenTime="(?<time>.*?)"\sContent="interface\s(?<interface>\S+)\slinkdown.*$'
          "event_name" => "网口拔出"
          "event_type" => "1000065"
          "event_subtype" => "1000000215"
          "extract_fields" => ["SerialNum", "interface"]
        },
        {
          "regex" => '^SerialNum=(?<SerialNum>\S+?)\sGenTime="(?<time>.*?)"\sContent="interface\s(?<interface>\S+?)\slinkup.*$'
          "event_name" => "网口接入"
          "event_type" => "1000065"
          "event_subtype" => "1000000214"
          "extract_fields" => ["SerialNum", "interface"]
        },
        {
          "regex" => 'GenTime="(?<time>.*?)"\sCpuUsage=(?<cpuUsage>\S+?)\sMemoryUsage=(?<memUsage>\S+?)\s.*?\sContent="System running info\.".*$'
          "event_name" => "性能监控"
          "event_type" => "1000025"
          "event_subtype" => "1000000089"
          "extract_fields" => ["cpuUsage", "memUsage"]
        }
      ]
    }
  }

  # 添加通用字段
  mutate {
    replace => {
      "device_type" => "1000035"
      "product_name" => "启明星辰-USG-FW-8020E"
    }
  }

  # 保存提取字段
  mutate {
    rename => {
      "interface" => "action_object_name"
    }
  }
}
