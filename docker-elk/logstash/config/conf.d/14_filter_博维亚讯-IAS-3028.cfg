filter {
  ruby {
    path => "/usr/share/logstash/config/conf.d/ruby/event_processor.rb"
    script_params => {
      "patterns" => [
        {
          "regex" => '^<\d+>(?<time>.*)\s+WitNet.*CONFIG.*-Console:user:\s+(?<user>\S+)\s+Login.*$'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => ["user"]
        },
        {
          "regex" => '^<\d+>(?<time>.*)\s+WitNet.*CONFIG.*-(?<logType>\S+):user:\s+(?<user>\S+)\s+Login\s+from\s+IP:(?<srcIp>\S+)..*$'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => ["logType", "user", "srcIp"]
        },
        {
          "regex" => '^<\d+>(?<time>.*)\s+WitNet.*CONFIG.*:\s+(?<user>\S+?)\s+.*welcome\.html.*$'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => ["user"]
        },
        {
          "regex" => '^<\d+>(?<time>.*)\s+WitNet.*CONFIG.*-(?<logType>\S+):.*failed\s+from\s+IP:(?<srcIp>\S+)..*$'
          "event_name" => "登录失败"
          "event_type" => "1000061"
          "event_subtype" => "1000000140"
          "extract_fields" => ["logType", "srcIp"]
        },
        {
          "regex" => '^<\d+>(?<time>.*)\s+WitNet.*CONFIG.*-Console:user:\s+(?<user>\S+)\s+Logout.*$'
          "event_name" => "退出登录"
          "event_type" => "1000068"
          "event_subtype" => "1000000201"
          "extract_fields" => ["user"]
        },
        {
          "regex" => '^<\d+>(?<time>.*)\s+WitNet.*CONFIG.*-(?<logType>\S+):user:\s+(?<user>\S+)\s+Logout\s+from\s+IP:(?<srcIp>\S+)..*$'
          "event_name" => "退出登录"
          "event_type" => "1000068"
          "event_subtype" => "1000000201"
          "extract_fields" => ["logType", "user", "srcIp"]
        },
        {
          "regex" => '^<\d+>(?<time>.*)\s+WitNet.*CONFIG.*:\s+(?<user>\S+?)\s+.*Logout\.html.*$'
          "event_name" => "退出登录"
          "event_type" => "1000068"
          "event_subtype" => "1000000201"
          "extract_fields" => ["user"]
        },
        {
          "regex" => '^<\d+>(?<time>.*)\s+WitNet.*LINK_D:(?<interface>port\s+\S+)\s.*$'
          "event_name" => "网口拔出"
          "event_type" => "1000065"
          "event_subtype" => "1000000215"
          "extract_fields" => ["interface"]
        },
        {
          "regex" => '^<\d+>(?<time>.*)\s+WitNet.*LINK_U:(?<interface>port\s+\S+)\s.*$'
          "event_name" => "网口接入"
          "event_type" => "1000065"
          "event_subtype" => "1000000214"
          "extract_fields" => ["interface"]
        }
      ]
    }
  }

  # 添加通用字段
  mutate {
    replace => {
      "device_type" => "1000079"
      "product_name" => "博维亚讯-IAS-3028"
    }
  }

  # 保存提取字段
  mutate {
    rename => {
      "srcIp" => "source_address"
      "interface" => "action_object_name"
      "user" => "related_object_name"
    }
  }
}
