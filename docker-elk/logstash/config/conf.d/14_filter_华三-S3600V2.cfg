filter {
  ruby {
    path => "/usr/share/logstash/config/conf.d/ruby/event_processor.rb"
    script_params => {
      "patterns" => [
        {
          "regex" => '%%\S+/\d+\/.*_(?<logType>.*)_.*UserName=(?<user>.*);\sAAA\sis\ssuccessful.*$'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => ["logType", "user"]
        },
        {
          "regex" => '%%\S+/\d+\/\S+_(?<logType>.*)_.*UserName=(?<user>.*);\sAAA\slaunched.*$'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => ["logType", "user"]
        },
        {
          "regex" => '%%.*LOGIN\(t\):\s-DevIP.*:(?<user>\S+)\slogin\sfrom\s(?<srcIp>\S+).*$'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => ["user", "srcIp"]
        },
        {
          "regex" => '%%10SHELL/4/LOGIN\(t\):\s+Trap\s+1\.3\.6\.1\.4\.1\.25506\.2\.2\.1\.1\.3\.0\.1'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => []
        },
        {
          "regex" => '%%\d+(?<logType>.*)/\d+\/.*:.*\s+(?<user>.*)\s+logged\s+in\s+from\s+(?<srcIp>.*)\..*$'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => ["logType", "user", "srcIp"]
        },
        {
          "regex" => 'LOGIN\(l\).*-\s+(?<user>\S+)\(.*login.*$'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => ["user"]
        },
        {
          "regex" => '%%\d+(?<logType>.*)/\d+\/.*user\s(?<user>.*)\s\(IP:\s(?<srcIp>.*)\)\slogged\sin\ssuccessfully.*$'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => ["logType", "user", "srcIp"]
        },
        {
          "regex" => '%%.*LOGINFAIL.*\s+user\s+failed\s+to\s+log\s+in.*$'
          "event_name" => "登录失败"
          "event_type" => "1000061"
          "event_subtype" => "1000000140"
          "extract_fields" => []
        },
        {
          "regex" => '%%.*LOGINFAIL.*\s+user\s+failed\s+to\s+log\s+in.*$'
          "event_name" => "登录失败"
          "event_type" => "1000061"
          "event_subtype" => "1000000140"
          "extract_fields" => []
        },
        {
          "regex" => '%%.*LOGOUT\(t\):\s-DevIP.*:(?<user>\S+)\slogout\sfrom\s(?<srcIp>\S+).*$'
          "event_name" => "退出登录"
          "event_type" => "1000068"
          "event_subtype" => "1000000201"
          "extract_fields" => ["user", "srcIp"]
        },
        {
          "regex" => 'LOGOUT\(l\).*-\s+(?<user>\S+)\(.*logout.*$'
          "event_name" => "退出登录"
          "event_type" => "1000068"
          "event_subtype" => "1000000201"
          "extract_fields" => ["user"]
        },
        {
          "regex" => '%%\d+(?<logType>.*)/\d+\/.*:.*\s+(?<user>.*)\s+logged\s+out\s+from\s+(?<srcIp>.*)\.$'
          "event_name" => "退出登录"
          "event_type" => "1000068"
          "event_subtype" => "1000000201"
          "extract_fields" => ["logType", "user", "srcIp"]
        },
        {
          "regex" => '1\.3\.6\.1\.4\.1\.25506\.2\.2\.1\.1\.3\.0\.2'
          "event_name" => "退出登录"
          "event_type" => "1000068"
          "event_subtype" => "1000000201"
          "extract_fields" => []
        },
        {
          "regex" => '%%\d+(?<logType>.*)/\d+\/.*user\s(?<user>.*)\s\(IP:\s(?<srcIp>.*)\)\slogged\sout..*$'
          "event_name" => "退出登录"
          "event_type" => "1000068"
          "event_subtype" => "1000000201"
          "extract_fields" => ["logType", "user", "srcIp"]
        },
        {
          "regex" => '%%\d+.*/\d+\/CFGMAN_CFGCHANGED.*;\s(?<Command>.*)..*$'
          "event_name" => "配置变更"
          "event_type" => "1000026"
          "event_subtype" => "1000000094"
          "extract_fields" => ["Command"]
        },
        {
          "regex" => '%%\S+/\d+\/(?<logType>.*)_.*:.*IPAddr=(?<srcIp>.*)-User=(?<user>.*);\sCommand\sis\s(?<Command>.*)$'
          "event_name" => "配置变更"
          "event_type" => "1000026"
          "event_subtype" => "1000000094"
          "extract_fields" => ["logType", "srcIp", "user", "Command"]
        },
        {
          "regex" => '%%10IFNET/4/INTERFACE UPDOWN\(t\):\s+Trap\s+1\.3\.6\.1\.6\.3\.1\.1\.5\.3<linkDown>:\s+Interface\s+(?<InterfaceID>\S+)\s+is\s+Down,\s+ifAdminStatus\s+is\s+(?<ifAdminStatus>\d+),\s+ifOperStatus\s+is\s+(?<ifOperStatus>\d+)'
          "event_name" => "网口拔出"
          "event_type" => "1000065"
          "event_subtype" => "1000000215"
          "extract_fields" => ["InterfaceID", "ifAdminStatus", "ifOperStatus"]
        },
        {
          "regex" => '%%\S+/\d+\/LINK_UPDOWN.*;{0,1}\s(?<interface>.*)\slink.*is\sDOWN.*$'
          "event_name" => "网口拔出"
          "event_type" => "1000065"
          "event_subtype" => "1000000215"
          "extract_fields" => ["interface"]
        },
        {
          "regex" => '1\.3\.6\.1\.6\.3\.1\.1\.5\.4.*$'
          "event_name" => "网口接入"
          "event_type" => "1000065"
          "event_subtype" => "1000000214"
          "extract_fields" => []
        },
        {
          "regex" => '%%\S+/\d+\/LINK_UPDOWN.*;{0,1}\s(?<interface>.*)\slink.*is\sUP..*$'
          "event_name" => "网口接入"
          "event_type" => "1000065"
          "event_subtype" => "1000000214"
          "extract_fields" => ["interface"]
        }
      ]
    }
  }

  # 添加通用字段
  mutate {
    replace => {
      "device_type" => "1000079"
      "product_name" => "华三-S3600V2"
    }
  }

  # 保存提取字段
  mutate {
    rename => {
      "InterfaceID" => "action_object_name"
      "Command" => "action_details"
      "device" => "device_name"
      "srcIp" => "source_address"
      "interface" => "action_object_name"
      "user" => "related_object_name"
    }
  }
}
