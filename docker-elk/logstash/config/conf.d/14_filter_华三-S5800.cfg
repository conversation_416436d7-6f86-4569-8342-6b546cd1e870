filter {
  ruby {
    path => "/usr/share/logstash/config/conf.d/ruby/event_processor.rb"
    script_params => {
      "patterns" => [
        {
          "regex" => 'LOGIN\(t\).*<\S+>:(?<Username>.*)\s+login\s+from\s+Console.*$'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => ["Username"]
        },
        {
          "regex" => 'WEB/5/LOGIN.*:\s+(?<Username>\S+)\s+从\s+(?<srcIp>\S+)\s+登录成功.*$'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => ["Username", "srcIp"]
        },
        {
          "regex" => 'WEBOPT_LOGIN_SUC.*\s+(?<Username>\S+)\s+logged\s+in\s+from\s+(?<srcIp>\S+?)(\.|)\s*$'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => ["Username", "srcIp"]
        },
        {
          "regex" => 'LOGIN.*\s+.*\((?<srcIp>\S+)\)\s+in\s+unit\d+\s+login.*?$'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => ["srcIp"]
        },
        {
          "regex" => 'LOGIN.*\s+Console\s+login\s+from\s+(?<srcIp>\S+?)(\.|)\s*$'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => ["srcIp"]
        },
        {
          "regex" => 'SSHS_CONNECT.*\s+user\s+(?<Username>\S+)\s+\(IP:\s*(?<srcIp>\S+)\)\s+connected\s+to\s+the\s+server\s+successfully.*?$'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => ["Username", "srcIp"]
        },
        {
          "regex" => 'SSHS_AUTH_SUCCESS.*\s+user\s+(?<Username>\S+)\s+from\s+(?<srcIp>\S+)\s+port.*?$'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => ["Username", "srcIp"]
        },
        {
          "regex" => 'LOGIN\(t\).*<\S+>:(?<Username>.*)\s+login\s+from\s+(?<srcIp>\S+?)(\.|)\s*$'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => ["Username", "srcIp"]
        },
        {
          "regex" => 'SSH/4/LOGIN.*\s+user\s+(?<Username>.*?)\s*\(IP:\s*(?<srcIp>\S+)\)\s+login\s+succeeded!.*$'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => ["Username", "srcIp"]
        },
        {
          "regex" => 'SC_AAA_SUCCESS.*UserName=(?<Username>\S+);\s+.*successful.*$'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => ["Username"]
        },
        {
          "regex" => 'SSHS_LOG.*Accepted\s+password\s+for\s+(?<Username>\S+)\s+from\s+(?<srcIp>\S+)\s+port.*?$'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => ["Username", "srcIp"]
        },
        {
          "regex" => 'SSH_LOGIN.*\s+user\s+(?<Username>\S+)\s+\(IP:\s*(?<srcIp>\S+)\)\s+logged\s+in\s+successfully.*$'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => ["Username", "srcIp"]
        },
        {
          "regex" => 'LOGIN.*\s+(?<Username>\S+)\s+logged\s+in\s+from\s+(?<srcIp>\S+?)(\.|)\s*$'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => ["Username", "srcIp"]
        },
        {
          "regex" => 'LOGIN.*\s+(?<Username>(?!.*:.*)\S+)\s+login\s+from\s+(?<srcIp>\S+?)(\.|)\s*$'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => ["Username", "srcIp"]
        },
        {
          "regex" => 'AUTHSUCC.*\s+user\s+(?<Username>.*)\s*\(IP:\s*(?<srcIp>\S+)\)\s+success\s+to\s+process\s+PASSWORD\s+Authentication!.*?$'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => ["Username", "srcIp"]
        },
        {
          "regex" => 'WEB/5/LOGIN_FAILED.*:\s+(?<Username>\S+)\s+从\s+(?<srcIp>\S+)\s+登录失败.*$'
          "event_name" => "登录失败"
          "event_type" => "1000061"
          "event_subtype" => "1000000140"
          "extract_fields" => ["Username", "srcIp"]
        },
        {
          "regex" => 'WEBOPT_LOGIN_FAIL.*\s+(?<Username>\S+)\s+failed\s+to\s+log\s+in\s+from\s+(?<srcIp>\S+?)(\.|)\s*$'
          "event_name" => "登录失败"
          "event_type" => "1000061"
          "event_subtype" => "1000000140"
          "extract_fields" => ["Username", "srcIp"]
        },
        {
          "regex" => 'SSHS_ALGORITHM_MISMATCH.*SSH\s+client\s+(?<srcIp>\S+)\s+failed\s+to\s+log\s+in.*$'
          "event_name" => "登录失败"
          "event_type" => "1000061"
          "event_subtype" => "1000000140"
          "extract_fields" => ["srcIp"]
        },
        {
          "regex" => 'SSHS_VERSION_MISMATCH.*SSH\s+client\s+(?<srcIp>\S+)\s+failed\s+to\s+log\s+in.*$'
          "event_name" => "登录失败"
          "event_type" => "1000061"
          "event_subtype" => "1000000140"
          "extract_fields" => ["srcIp"]
        },
        {
          "regex" => 'LOGIN_FAILED.*TTY\s+(?<Username>.*)\s*failed\s+to\s+log\s+in\s+from\s+(?<srcIp>\S+?)\.$'
          "event_name" => "登录失败"
          "event_type" => "1000061"
          "event_subtype" => "1000000140"
          "extract_fields" => ["Username", "srcIp"]
        },
        {
          "regex" => 'LOGINFAIL.*AUX\s+user\s+(?<Username>(?!.*:.*).*?)\s*failed\s+to\s+login\s+on.*$'
          "event_name" => "登录失败"
          "event_type" => "1000061"
          "event_subtype" => "1000000140"
          "extract_fields" => ["Username"]
        },
        {
          "regex" => 'SSH/4/TrapAuthFailed\(t\).*SSH\s+authentication\s+fail\s+trap\s+information.*$'
          "event_name" => "登录失败"
          "event_type" => "1000061"
          "event_subtype" => "1000000140"
          "extract_fields" => []
        },
        {
          "regex" => 'LOGINFAIL.*SSH\s+user\s+(?<Username>(?!.*:.*).*?)\s*failed\s+to\s+log\s+in\s+from\s+(?<srcIp>\S+)\s+on.*$'
          "event_name" => "登录失败"
          "event_type" => "1000061"
          "event_subtype" => "1000000140"
          "extract_fields" => ["Username", "srcIp"]
        },
        {
          "regex" => 'SSHS_LOG.*Authentication\s+failed\s+for\s+(?<Username>\S+)\s+from\s+(?<srcIp>\S+)\s+port.*?$'
          "event_name" => "登录失败"
          "event_type" => "1000061"
          "event_subtype" => "1000000140"
          "extract_fields" => ["Username", "srcIp"]
        },
        {
          "regex" => 'VTY_LOG.*AUX\s+user\s+(?<Username>(?!.*:.*).*?)\s*failed\s+to\s+login\s+on.*$'
          "event_name" => "登录失败"
          "event_type" => "1000061"
          "event_subtype" => "1000000140"
          "extract_fields" => ["Username"]
        },
        {
          "regex" => 'LOGINFAIL.*AUX\s+user\s+(?<Username>(?!.*:.*).*?)\s*failed\s+to\s+log\s+in\s+on.*$'
          "event_name" => "登录失败"
          "event_type" => "1000061"
          "event_subtype" => "1000000140"
          "extract_fields" => ["Username"]
        },
        {
          "regex" => 'LOGINFAIL.*SSH\s+user\s+(?<Username>(?!.*:.*)\S+)\s+failed\s+to\s+login\s+from\s+(?<srcIp>\S+)\s+on.*$'
          "event_name" => "登录失败"
          "event_type" => "1000061"
          "event_subtype" => "1000000140"
          "extract_fields" => ["Username", "srcIp"]
        },
        {
          "regex" => 'AUTHFAIL.*\s+user\s+(?<Username>.*)\s*\(IP:\s*(?<srcIp>\S+)\)\s+login\s+failed.*?$'
          "event_name" => "登录失败"
          "event_type" => "1000061"
          "event_subtype" => "1000000140"
          "extract_fields" => ["Username", "srcIp"]
        },
        {
          "regex" => 'LOGINAUTHFAIL\(t\).*<\S+>:(?<Username>.*)\s+failed\s+to\s+login\s+from\s+(?<srcIp>\S+),.*$'
          "event_name" => "登录失败"
          "event_type" => "1000061"
          "event_subtype" => "1000000140"
          "extract_fields" => ["Username", "srcIp"]
        },
        {
          "regex" => 'LOGIN_FAILED.*\s+(?<Username>.*)\s+failed\s+to\s+log\s+in\s+from\s+(?<srcIp>\S+?)(\.|)\s*$'
          "event_name" => "登录失败"
          "event_type" => "1000061"
          "event_subtype" => "1000000140"
          "extract_fields" => ["Username", "srcIp"]
        },
        {
          "regex" => 'LS_AUTHEN_FAILURE.*UserName=(?<Username>\S+);\s+Authentication\s+is\s+failed.*$'
          "event_name" => "登录失败"
          "event_type" => "1000061"
          "event_subtype" => "1000000140"
          "extract_fields" => ["Username"]
        },
        {
          "regex" => 'SSH_LOGOUT.*\s+user\s+(?<Username>\S+)\s+\(IP:\s*(?<srcIp>\S+)\)\s+logged\s+out.*$'
          "event_name" => "退出登录"
          "event_type" => "1000068"
          "event_subtype" => "1000000201"
          "extract_fields" => ["Username", "srcIp"]
        },
        {
          "regex" => 'SSHS_LOG.*Connection\s+closed\s+by\s+(?<srcIp>\S+?)(\.|)\s*$'
          "event_name" => "退出登录"
          "event_type" => "1000068"
          "event_subtype" => "1000000201"
          "extract_fields" => ["srcIp"]
        },
        {
          "regex" => 'WEBOPT_LOGOUT.*\s+(?<Username>\S+)\s+logged\s+out\s+from\s+(?<srcIp>\S+?)(\.|)\s*$'
          "event_name" => "退出登录"
          "event_type" => "1000068"
          "event_subtype" => "1000000201"
          "extract_fields" => ["Username", "srcIp"]
        },
        {
          "regex" => 'LOGOUT.*\s+(?<Username>\S+)\((?<srcIp>\S+)\)\s+in\s+unit\d+\s+logout.*?$'
          "event_name" => "退出登录"
          "event_type" => "1000068"
          "event_subtype" => "1000000201"
          "extract_fields" => ["Username", "srcIp"]
        },
        {
          "regex" => 'WEB/5/LOGOUT.*:\s+(?<Username>\S+)\s+从\s+(?<srcIp>\S+)\s+退出登录.*$'
          "event_name" => "退出登录"
          "event_type" => "1000068"
          "event_subtype" => "1000000201"
          "extract_fields" => ["Username", "srcIp"]
        },
        {
          "regex" => 'SSH_CONNECTION_CLOSE.*\s+user\s+(?<Username>.*?)\s*\(IP:\s*(?<srcIp>\S+)\)\s+logged\s+out.*$'
          "event_name" => "退出登录"
          "event_type" => "1000068"
          "event_subtype" => "1000000201"
          "extract_fields" => ["Username", "srcIp"]
        },
        {
          "regex" => 'SSHS_LOG.*User\s+(?<Username>\S+)\s+logged\s+out\s+from\s+(?<srcIp>\S+)\s+port.*?$'
          "event_name" => "退出登录"
          "event_type" => "1000068"
          "event_subtype" => "1000000201"
          "extract_fields" => ["Username", "srcIp"]
        },
        {
          "regex" => 'SSHS_DISCONNECT.*\s+user\s+(?<Username>\S+)\s+\(IP:\s*(?<srcIp>\S+)\)\s+disconnected\s+from\s+the\s+server.*?$'
          "event_name" => "退出登录"
          "event_type" => "1000068"
          "event_subtype" => "1000000201"
          "extract_fields" => ["Username", "srcIp"]
        },
        {
          "regex" => 'LOGOUT\(t\).*<\S+>:(?<Username>.*)\s+logout\s+from\s+(?<srcIp>\S+?)(\.|)\s*$'
          "event_name" => "退出登录"
          "event_type" => "1000068"
          "event_subtype" => "1000000201"
          "extract_fields" => ["Username", "srcIp"]
        },
        {
          "regex" => 'SSH/4/LOGOUT.*\s+user\s+(?<Username>.*?)\s*\(IP:\s*(?<srcIp>\S+)\)\s+logout.*$'
          "event_name" => "退出登录"
          "event_type" => "1000068"
          "event_subtype" => "1000000201"
          "extract_fields" => ["Username", "srcIp"]
        },
        {
          "regex" => 'SSH/4/TrapLogoff.*\s+user\s+logoff\s+trap\s+information.*$'
          "event_name" => "退出登录"
          "event_type" => "1000068"
          "event_subtype" => "1000000201"
          "extract_fields" => []
        },
        {
          "regex" => 'LOGOUT.*\s+.*logged\s+out\s+from\s+(?<srcIp>\S+?)(\.|)\s*$'
          "event_name" => "退出登录"
          "event_type" => "1000068"
          "event_subtype" => "1000000201"
          "extract_fields" => ["srcIp"]
        },
        {
          "regex" => 'LOGOUT.*\s+(?<Username>(?!.*:.*)\S+)\s+logout\s+from\s+(?<srcIp>\S+?)(\.|)\s*$'
          "event_name" => "退出登录"
          "event_type" => "1000068"
          "event_subtype" => "1000000201"
          "extract_fields" => ["Username", "srcIp"]
        },
        {
          "regex" => 'LINK-3-UPDOWN.*Interface\s+(?<Interface>.*),\s+changed\s+state\s+to\s+(down|DOWN|Down).*$'
          "event_name" => "网口拔出"
          "event_type" => "1000065"
          "event_subtype" => "1000000215"
          "extract_fields" => ["Interface"]
        },
        {
          "regex" => 'PORT\s+LINK\s+STATUS\s+CHANGE\(l\):.*\s+(?<Interface>\S+)\s+is\s+(down|DOWN|Down).*$'
          "event_name" => "网口拔出"
          "event_type" => "1000065"
          "event_subtype" => "1000000215"
          "extract_fields" => ["Interface"]
        },
        {
          "regex" => 'PHY_UPDOWN.*Physical\s+state\s+on\s+the\s+interface\s+(?<Interface>\S+?)\s+changed\s+to\s+(down|DOWN|Down).*?$'
          "event_name" => "网口拔出"
          "event_type" => "1000065"
          "event_subtype" => "1000000215"
          "extract_fields" => ["Interface"]
        },
        {
          "regex" => 'PHY_UPDOWN.*\s+(?<Interface>\S+?)\s+link\s+status\s+is\s+(down|DOWN|Down).*$'
          "event_name" => "网口拔出"
          "event_type" => "1000065"
          "event_subtype" => "1000000215"
          "extract_fields" => ["Interface"]
        },
        {
          "regex" => 'LINK_UPDOWN.*\s+(?<Interface>\S+?)\s+link\s+status\s+is\s+(down|DOWN|Down).*$'
          "event_name" => "网口拔出"
          "event_type" => "1000065"
          "event_subtype" => "1000000215"
          "extract_fields" => ["Interface"]
        },
        {
          "regex" => 'LINK UPDOWN.*\s+(?<Interface>\S+?)(:|)\s+link\s+status\s+is\s+(down|DOWN|Down).*$'
          "event_name" => "网口拔出"
          "event_type" => "1000065"
          "event_subtype" => "1000000215"
          "extract_fields" => ["Interface"]
        },
        {
          "regex" => 'PHY_UPDOWN.*Physical\s+state\s+on\s+the\s+(?<Interface>\S+)\s+changed\s+to\s+(up|UP|Up).*$'
          "event_name" => "网口接入"
          "event_type" => "1000065"
          "event_subtype" => "1000000214"
          "extract_fields" => ["Interface"]
        },
        {
          "regex" => 'PHY_UPDOWN.*Physical\s+state\s+on\s+the\s+interface\s+(?<Interface>\S+?)\s+changed\s+to\s+(up|UP|Up).*$'
          "event_name" => "网口接入"
          "event_type" => "1000065"
          "event_subtype" => "1000000214"
          "extract_fields" => ["Interface"]
        },
        {
          "regex" => 'LINK-3-UPDOWN.*Interface\s+(?<Interface>.*),\s+changed\s+state\s+to\s+(up|UP|Up).*$'
          "event_name" => "网口接入"
          "event_type" => "1000065"
          "event_subtype" => "1000000214"
          "extract_fields" => ["Interface"]
        },
        {
          "regex" => 'PORT\s+LINK\s+STATUS\s+CHANGE\(l\):.*\s+(?<Interface>\S+)\s+is\s+(up|UP|Up).*$'
          "event_name" => "网口接入"
          "event_type" => "1000065"
          "event_subtype" => "1000000214"
          "extract_fields" => ["Interface"]
        },
        {
          "regex" => 'LINK_UPDOWN.*\s+(?<Interface>\S+?)\s+link\s+status\s+is\s+(up|UP|Up).*$'
          "event_name" => "网口接入"
          "event_type" => "1000065"
          "event_subtype" => "1000000214"
          "extract_fields" => ["Interface"]
        },
        {
          "regex" => 'PHY_UPDOWN.*\s+(?<Interface>\S+?)\s+link\s+status\s+is\s+(up|UP|Up).*$'
          "event_name" => "网口接入"
          "event_type" => "1000065"
          "event_subtype" => "1000000214"
          "extract_fields" => ["Interface"]
        },
        {
          "regex" => 'LINK UPDOWN.*\s+(?<Interface>\S+?)(:|)\s+link\s+status\s+is\s+(up|UP|Up).*$'
          "event_name" => "网口接入"
          "event_type" => "1000065"
          "event_subtype" => "1000000214"
          "extract_fields" => ["Interface"]
        }
      ]
    }
  }

  # 添加通用字段
  mutate {
    replace => {
      "device_type" => "1000079"
      "product_name" => "华三-S5800"
    }
  }

  # 保存提取字段
  mutate {
    rename => {
      "Interface" => "action_object_name"
      "srcIp" => "source_address"
      "Username" => "related_object_name"
    }
  }
}
