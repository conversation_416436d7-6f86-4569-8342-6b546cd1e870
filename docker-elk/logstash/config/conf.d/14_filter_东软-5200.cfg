filter {
  ruby {
    path => "/usr/share/logstash/config/conf.d/ruby/event_processor.rb"
    script_params => {
      "patterns" => [
        {
          "regex" => '^(?:\d{2}-\d{2}-\d{3}-\d{4}\s+Notice\s+System\s+admin\s+rep=\d+\s+\|\s+管理)?用户(?<Username>\S+)通过\S+登录成功.*?IP地址为(?<SourceIP>\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}).*$'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => ["Username", "SourceIP"]
        },
        {
          "regex" => 'user\s+(?<user>\S+)\s+logged\s+in\s+via\s+(?<logintype>\S+)\s+from\s+(?<SourceIP>\S+)..*$'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => ["user", "logintype", "SourceIP"]
        },
        {
          "regex" => '^(?:.*?)用户\s(?<Username>\S+?)从\S+?登录成功\,\sIP为(?<SourceIP>\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}).*$'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => ["Username", "SourceIP"]
        },
        {
          "regex" => 'user\s+(?<user>\S+)\s+logged\s+in\s+failed\s+via\s+(?<logintype>\S+)\s+from\s+(?<SourceIP>\S+)..*$'
          "event_name" => "登录失败"
          "event_type" => "1000061"
          "event_subtype" => "1000000140"
          "extract_fields" => ["user", "logintype", "SourceIP"]
        },
        {
          "regex" => '^(?<code>\S+)\s+Warning\s+System\s+N/A\s+rep=\d+\s+\|\s+管理用户(?<Username>\S+)通过\S+登录失败.*?IP地址为(?<SourceIP>\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}).*$'
          "event_name" => "登录失败"
          "event_type" => "1000061"
          "event_subtype" => "1000000140"
          "extract_fields" => ["code", "Username", "SourceIP"]
        },
        {
          "regex" => 'FW\s+0+\s+2\s+(?<user>\S+)\s+(?<srcIp>\S+).*$'
          "event_name" => "登录失败"
          "event_type" => "1000061"
          "event_subtype" => "1000000140"
          "extract_fields" => ["user", "srcIp"]
        },
        {
          "regex" => '用户\s(?<Username>\S+?)从\S+?登录失败\,\sIP为(?<SourceIP>\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}).*$'
          "event_name" => "登录失败"
          "event_type" => "1000061"
          "event_subtype" => "1000000140"
          "extract_fields" => ["Username", "SourceIP"]
        },
        {
          "regex" => '用户\s*(?<Username>\S+?)从\S+?注销.*IP为(?<SourceIP>\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}).*$'
          "event_name" => "退出登录"
          "event_type" => "1000068"
          "event_subtype" => "1000000201"
          "extract_fields" => ["Username", "SourceIP"]
        },
        {
          "regex" => 'user\s+(?<user>\S+)\s+logged\s+out.*?from\s+(?<srcIp>\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}).*$'
          "event_name" => "退出登录"
          "event_type" => "1000068"
          "event_subtype" => "1000000201"
          "extract_fields" => ["user", "srcIp"]
        },
        {
          "regex" => 'packet.(?<srcIp>\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})\s*?\((?<srcPort>\d+)\)-(?<dst>\S+)\((?<dstPort>\d+)\).*action.drop.*$'
          "event_name" => "不符合安全策略访问"
          "event_type" => "1000065"
          "event_subtype" => "1000000241"
          "extract_fields" => ["srcIp", "srcPort", "dst", "dstPort"]
        },
        {
          "regex" => '访问策略\,\s名称:\S+?,\s原始地址:(?<srcIp>.*)\((?<srcPort>\d+)\)-(?<dstIp>\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})\((?<dstPort>\d+)\)\,\s协议:(?<protocol>\S*?)\,\s地址转换:\S*?\,\s安全域:\S*?\,\s动作:drop.*$'
          "event_name" => "不符合安全策略访问"
          "event_type" => "1000065"
          "event_subtype" => "1000000241"
          "extract_fields" => ["srcIp", "srcPort", "dstIp", "dstPort", "protocol"]
        },
        {
          "regex" => '访问策略,\s名称:\S+?,\s原始地址:(?<srcIp>.*)\((?<srcPort>\d+)\)-(?<dstIp>\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})\((?<dstPort>\d+)\)\,\s协议:(?<protocol>\S*?)\,\s地址转换:\S*?\,\s安全域:\S*?\,\s动作:accept.*$'
          "event_name" => "符合安全策略访问"
          "event_type" => "1000065"
          "event_subtype" => "1000000241"
          "extract_fields" => ["srcIp", "srcPort", "dstIp", "dstPort", "protocol"]
        },
        {
          "regex" => '接口(?<interface>\S+)物理连接断开.*$'
          "event_name" => "网口拔出"
          "event_type" => "1000065"
          "event_subtype" => "1000000215"
          "extract_fields" => ["interface"]
        },
        {
          "regex" => 'Interface\s+(?<interface>\S+).*disconnected.*$'
          "event_name" => "网口拔出"
          "event_type" => "1000065"
          "event_subtype" => "1000000215"
          "extract_fields" => ["interface"]
        },
        {
          "regex" => '^(?<logSource>.*?)(?:\s+\S+)?\s+Interface\s+(?<interface>\S+)\s+was\s+disconnected.*$'
          "event_name" => "网口拔出"
          "event_type" => "1000065"
          "event_subtype" => "1000000215"
          "extract_fields" => ["logSource", "interface"]
        },
        {
          "regex" => 'Warning FW.*?接口 (?<interface>\S+) 断开.*'
          "event_name" => "网口拔出"
          "event_type" => "1000065"
          "event_subtype" => "1000000215"
          "extract_fields" => ["interface"]
        },
        {
          "regex" => '接口(?<interface>\S+)连接.*$'
          "event_name" => "网口接入"
          "event_type" => "1000065"
          "event_subtype" => "1000000214"
          "extract_fields" => ["interface"]
        },
        {
          "regex" => 'Interface\s+(?<interface>\S+).*\s+connected.*$'
          "event_name" => "网口接入"
          "event_type" => "1000065"
          "event_subtype" => "1000000214"
          "extract_fields" => ["interface"]
        },
        {
          "regex" => 'Interface\s+(?<interface>\S+)\s+was\s+connected.*$'
          "event_name" => "网口接入"
          "event_type" => "1000065"
          "event_subtype" => "1000000214"
          "extract_fields" => ["interface"]
        },
        {
          "regex" => 'Warning FW.*?接口 (?<interface>\S+) 连接.*'
          "event_name" => "网口接入"
          "event_type" => "1000065"
          "event_subtype" => "1000000214"
          "extract_fields" => ["interface"]
        }
      ]
    }
  }

  # 添加通用字段
  mutate {
    replace => {
      "device_type" => "1000035"
      "product_name" => "东软-5200"
    }
  }

  # 保存提取字段
  mutate {
    rename => {
      "dstIp" => "dest_address"
      "SourceIP" => "source_address"
      "protocol" => "protocol"
      "dstPort" => "dest_port"
      "srcIp" => "source_address"
      "code" => "standard_event_code"
      "user" => "related_object_name"
      "srcPort" => "source_port"
      "interface" => "action_object_name"
      "Username" => "related_object_name"
    }
  }
}
