filter {
  mutate {
    replace => { "device_type" => "1000035" }
  }

  if [process][program] == "IF_INFO" {
    mutate {
      add_field => { "event_type" => "1000026" }
      add_field => { "event_subtype" => "1000000095" }
    }
  } else if [process][program] == "FILTER" {
    mutate {
      add_field => { "event_type" => "1000027" }
      add_field => { "event_subtype" => "1000000111" }
    }
  } else if [process][program] == "CONFIG" {
    mutate {
      add_field => { "event_type" => "1000026" }
      add_field => { "event_subtype" => "1000000094" }
    }
  } else {
    mutate {
      add_field => { "event_type" => "1000031" }
    }
  }

}