filter {
  ruby {
    path => "/usr/share/logstash/config/conf.d/ruby/event_processor.rb"
    script_params => {
      "patterns" => [
        {
          "regex" => '4624.*AUDIT_SUCCESS.*登录类型:\s+[27]\s+.*帐户名(称|):\s+(?<user>\S+)\s+帐户域.*源网络地址:\s+(?<srcIp>\S+).*?$'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "**********"
          "extract_fields" => ["user", "srcIp"]
        },
        {
          "regex" => '\b5\s+15\s+(?<srcIp>\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})\s+.*\s+(?<Time>\d{2}:\d{2}:\d{2})\s+(?<Username>\S+).*?$'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "**********"
          "extract_fields" => ["srcIp", "Time", "Username"]
        },
        {
          "regex" => '4624.*AUDIT_SUCCESS.*Logon\s+Type:\s+(3|10)\s+.*Account\s+Name:\s+(?<user>\S+)\s+Account\s+Domain.*Network\s+Address:\s+(?<srcIp>\S+).*?$'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "**********"
          "extract_fields" => ["user", "srcIp"]
        },
        {
          "regex" => '^4624:\s+AUDIT_SUCCESS\s+An\s+account\s+was\s+successfully\s+logged\s+on\..*Account\s+Name:\s+(?<user>\S+)\s+Account\s+Domain.*Network\s+Address:\s+(?<srcIp>\S+).*?$'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "**********"
          "extract_fields" => ["user", "srcIp"]
        },
        {
          "regex" => '^4624.*AUDIT_SUCCESS.*登录类型:\s+(3|10)\s+.*帐户名(称|):\s+(?<user>\S+)\s+帐户域.*源网络地址:\s+(?<srcIp>\S+).*?$'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "**********"
          "extract_fields" => ["user", "srcIp"]
        },
        {
          "regex" => '4624.*AUDIT_SUCCESS.*登录类型:\s+[27]\s+.*帐户名:\s+(?<user>\S+)\s+帐户域.*源网络地址:\s+(?<srcIp>\S+).*$'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "**********"
          "extract_fields" => ["user", "srcIp"]
        },
        {
          "regex" => '4624.*AUDIT_SUCCESS.*登录类型:\s+(3|10)\s+.*帐户名:\s+(?<user>\S+)\s+帐户域.*源网络地址:\s+(?<srcIp>\S+).*$'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "**********"
          "extract_fields" => ["user", "srcIp"]
        },
        {
          "regex" => '^4624.*AUDIT_SUCCESS.*Logon\s+Type:\s+(3|10)\s+.*Account\s+Name:\s+(?<user>\S+)\s+Account\s+Domain.*Network\s+Address:\s+(?<srcIp>\S+).*$'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "**********"
          "extract_fields" => ["user", "srcIp"]
        },
        {
          "regex" => '^4624.*AUDIT_SUCCESS\s+An\s+account\s+was\s+successfully\s+logged\s+on\.\s+Subject:\s+Security\s+ID:\s+S-1-0-0\s+Account\s+Name:\s+-\s+Account\s+Domain:\s+-\s+Logon\s+ID:\s+0x0\s+Logon\s+Type:\s+[3]\s+New\s+Logon:\s+Security\s+ID:\s+S-1-5-21-**********-*********-**********-1000\s+Account\s+Name:\s+(?<user>\S+)\s+Account\s+Domain:\s+(?<domain>\S+)\s+Logon\s+ID:\s+0x1d9912\s+Logon\s+GUID:\s+{********-0000-0000-0000-********0000}\s+Process\s+Information:\s+Process\s+ID:\s+0x0\s+Process\s+Name:\s+-\s+Network\s+Information:\s+Workstation\s+Name:\s+PC-20181113JAEO\s+Source\s+Network\s+Address:\s+-\s+Source\s+Port:\s+-\s+Detailed\s+Authentication\s+Information:\s+Logon\s+Process:\s+NtLmSsp\s+Authentication\s+Package:\s+NTLM\s+Transited\s+Services:\s+-\s+Package\s+Name\s+\(NTLM\s+only\):\s+NTLM\s+V2\s+Key\s+Length:\s+128.*$'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "**********"
          "extract_fields" => ["eventCode", "user", "domain"]
        },
        {
          "regex" => '4625.*AUDIT_FAILURE.*登录类型:\s+[27]\s+.*帐户名(称|):\s+(?<user>\S+)\s+帐户域.*源网络地址:\s+(?<srcIp>\S+).*?$'
          "event_name" => "登录失败"
          "event_type" => "1000061"
          "event_subtype" => "**********"
          "extract_fields" => ["user", "srcIp"]
        },
        {
          "regex" => '4625.*AUDIT_FAILURE.*登录类型:\s+(3|10)\s+.*帐户名(称|):\s+(?<user>\S+)\s+帐户域.*源网络地址:\s+(?<srcIp>\S+).*?$'
          "event_name" => "登录失败"
          "event_type" => "1000061"
          "event_subtype" => "**********"
          "extract_fields" => ["user", "srcIp"]
        },
        {
          "regex" => '\s+5\s+14\s+(?<srcIp>\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})\s+.*\s+(?<Time>\d{2}:\d{2}:\d{2})\s+(?<Username>\S+).*?$'
          "event_name" => "登录失败"
          "event_type" => "1000061"
          "event_subtype" => "**********"
          "extract_fields" => ["srcIp", "Time", "Username"]
        },
        {
          "regex" => '4625.*AUDIT_FAILURE.*登录类型:\s+[27]\s+.*帐户名:\s+(?<user>\S+)\s+帐户域.*源网络地址:\s+(?<srcIp>\S+).*$'
          "event_name" => "登录失败"
          "event_type" => "1000061"
          "event_subtype" => "**********"
          "extract_fields" => ["user", "srcIp"]
        },
        {
          "regex" => '4625.*AUDIT_FAILURE.*登录类型:\s+(3|10)\s+.*帐户名:\s+(?<user>\S+)\s+帐户域.*源网络地址:\s+(?<srcIp>\S+).*'
          "event_name" => "登录失败"
          "event_type" => "1000061"
          "event_subtype" => "**********"
          "extract_fields" => ["user", "srcIp"]
        },
        {
          "regex" => '\s+5\s+16\s+(?<srcIp>\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})\s+.*\s+(?<Time>\d{2}:\d{2}:\d{2})\s+(?<Username>\S+).*?$'
          "event_name" => "退出登录"
          "event_type" => "1000068"
          "event_subtype" => "**********"
          "extract_fields" => ["srcIp", "Time", "Username"]
        },
        {
          "regex" => '^4647:\s+AUDIT_SUCCESS.*帐户名(称|):\s+(?<user>\S+)\s+帐户域.*$'
          "event_name" => "退出登录"
          "event_type" => "1000068"
          "event_subtype" => "**********"
          "extract_fields" => ["user"]
        },
        {
          "regex" => '^4634:\s+AUDIT_SUCCESS.*帐户名(称|):\s+(?<user>\S+)\s+帐户域.*登录类型:\s+(3|10)\s+.*$'
          "event_name" => "退出登录"
          "event_type" => "1000068"
          "event_subtype" => "**********"
          "extract_fields" => ["user"]
        },
        {
          "regex" => 'Security-Auditing:\s+4634:\s+AUDIT_SUCCESS.*帐户名(称|):\s+(?<user>\S+)\s+帐户域.*登录类型:\s+[27]\s+.*$'
          "event_name" => "退出登录"
          "event_type" => "1000068"
          "event_subtype" => "**********"
          "extract_fields" => ["user"]
        },
        {
          "regex" => 'Security-Auditing:\s+4634:\s+AUDIT_SUCCESS.*Account\s+Name:\s+(?<user>\S+)\s+Account\s+Domain.*Logon\s+Type:\s+[27]\s+.*$'
          "event_name" => "退出登录"
          "event_type" => "1000068"
          "event_subtype" => "**********"
          "extract_fields" => ["user"]
        },
        {
          "regex" => '4634:\s+AUDIT_SUCCESS.*Account\s+Name:\s+(?<user>\S+)\s+Account\s+Domain.*Logon\s+Type:\s+(3|10)\s+.*$'
          "event_name" => "退出登录"
          "event_type" => "1000068"
          "event_subtype" => "**********"
          "extract_fields" => ["user"]
        },
        {
          "regex" => '^4647:\s+AUDIT_SUCCESS.*帐户名称:\s+(?<user>\S+)\s+帐户域.*$'
          "event_name" => "退出登录"
          "event_type" => "1000068"
          "event_subtype" => "**********"
          "extract_fields" => ["user"]
        },
        {
          "regex" => 'Security-Auditing:\s+4634:\s+AUDIT_SUCCESS.*帐户名:\s+(?<user>\S+)\s+帐户域.*登录类型:\s+[27]\s+.*$'
          "event_name" => "退出登录"
          "event_type" => "1000068"
          "event_subtype" => "**********"
          "extract_fields" => ["user"]
        },
        {
          "regex" => '^4634:\s+AUDIT_SUCCESS.*Account\s+Name:\s+(?<user>\S+)\s+Account\s+Domain.*Logon\s+Type:\s+(3|10)\s+.*$'
          "event_name" => "退出登录"
          "event_type" => "1000068"
          "event_subtype" => "**********"
          "extract_fields" => ["user"]
        },
        {
          "regex" => '4634:\s+AUDIT_SUCCESS.*帐户名:\s+(?<user>\S+)\s+帐户域.*登录类型:\s+(3|10)\s+.*$'
          "event_name" => "退出登录"
          "event_type" => "1000068"
          "event_subtype" => "**********"
          "extract_fields" => ["user"]
        },
        {
          "regex" => '^Security-Auditing:\s+4634:\s+AUDIT_SUCCESS.*Account\sName:\s+(?<user>\S+)\s+Account\s+Domain.*Logon\s+Type:\s+[27]\s+.*$'
          "event_name" => "退出登录"
          "event_type" => "1000068"
          "event_subtype" => "**********"
          "extract_fields" => ["user"]
        },
        {
          "regex" => '\s+5\s+18\s+2\s+\{(?<UsbNumber>.+)\}\s+\{(?<deviceName>.+)\}\s+\{(?<FactoryName>.+)\}\s+\{(?<Dev_Num>.+)\}\s+\{(?<Factory_Num>.+)\}\s+\{(?<Protocol>.+)\}.*?$'
          "event_name" => "USB设备拔出"
          "event_type" => "1000061"
          "event_subtype" => "**********"
          "extract_fields" => ["UsbNumber", "deviceName", "FactoryName", "Dev_Num", "Factory_Num", "Protocol"]
        },
        {
          "regex" => '^\w+\s+5\s+17\s+1\s+\{(?<UsbNumber>.+)\}\s+\{(?<deviceName>.+)\}\s+\{(?<FactoryName>.+)\}\s+\{(?<Dev_Num>.+)\}\s+\{(?<Factory_Num>.+)\}\s+\{(?<Protocol>.+)\}.*?$'
          "event_name" => "USB设备接入"
          "event_type" => "1000065"
          "event_subtype" => "**********"
          "extract_fields" => ["UsbNumber", "deviceName", "FactoryName", "Dev_Num", "Factory_Num", "Protocol"]
        },
        {
          "regex" => '^<\d+>.*NETWORK.*disconnected.*Desc:(?<interface>.*)\s+MAC:(?<mac>.*)\s+Address:(?<devIp>\S+)\s+Mask.*$'
          "event_name" => "网口拔出"
          "event_type" => "1000065"
          "event_subtype" => "1000000215"
          "extract_fields" => ["interface", "mac", "devIp"]
        },
        {
          "regex" => '^<\d+>20002\s+NETWORK.*Desc:(?<interface>.*)\s+MAC:(?<mac>.*)\s+Address:(?<devIp>\S+)\s+Mask.*$'
          "event_name" => "网口拔出"
          "event_type" => "1000065"
          "event_subtype" => "1000000215"
          "extract_fields" => ["interface", "mac", "devIp"]
        },
        {
          "regex" => '^<\d+>.*NETWORK.*\s+connected.*Desc:(?<interface>.*)\s+MAC:(?<mac>.*)\s+Address:(?<devIp>\S+)\s+Mask.*$'
          "event_name" => "网口接入"
          "event_type" => "1000065"
          "event_subtype" => "1000000214"
          "extract_fields" => ["interface", "mac", "devIp"]
        },
        {
          "regex" => '^<\d+>4004\s+NETWORK.*Desc:(?<interface>.*)\s+MAC:(?<mac>.*)\s+Address:(?<devIp>\S+)\s+Mask.*$'
          "event_name" => "网口接入"
          "event_type" => "1000065"
          "event_subtype" => "1000000214"
          "extract_fields" => ["interface", "mac", "devIp"]
        }
      ]
    }
  }

  # 添加通用字段
  mutate {
    replace => {
      "device_type" => "1000078"
      "product_name" => "微软-Windows"
    }
  }

  # 保存提取字段
  mutate {
    rename => {
      "Protocol" => "protocol"
      "mac" => "source_mac"
      "deviceName" => "device_name"
      "srcIp" => "source_address"
      "eventCode" => "standard_event_code"
      "Dev_Num" => "device_name"
      "interface" => "action_object_name"
      "user" => "related_object_name"
      "domain" => "domain"
      "Time" => "action_details"
      "Username" => "related_object_name"
    }
  }
}
