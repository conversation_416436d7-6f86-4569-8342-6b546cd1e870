filter {
  ruby {
    path => "/usr/share/logstash/config/conf.d/ruby/event_processor.rb"
    script_params => {
      "patterns" => [
        {
          "regex" => ':(?<user>\S+)\s+(?<logType>\S+)\s+login\s+(?<srcIp>.*)\s+OK.*$'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => ["user", "logType", "srcIp"]
        },
        {
          "regex" => '^[^]]+\]:(?<user>\S+)\s+(?<logType>\S+)\s+log\s+on\s+(?<srcIp>.*)\s+OK.*$'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => ["user", "logType", "srcIp"]
        },
        {
          "regex" => ':(?<user>\S+)\s+(?<logType>\S+)\s+log\s+on\s+(?<srcIp>.*)\s+failed.*$'
          "event_name" => "登录失败"
          "event_type" => "1000061"
          "event_subtype" => "1000000140"
          "extract_fields" => ["user", "logType", "srcIp"]
        },
        {
          "regex" => ':(?<user>\S+)\s+(?<logType>\S+)\s+log\s+out\s+(?<srcIp>.*).*$'
          "event_name" => "退出登录"
          "event_type" => "1000068"
          "event_subtype" => "1000000201"
          "extract_fields" => ["user", "logType", "srcIp"]
        },
        {
          "regex" => 'Port\s+Alarm.*port:(?<interface>\S+\s+\S+)\s+state:Link\s+Down.*$'
          "event_name" => "网口拔出"
          "event_type" => "1000065"
          "event_subtype" => "1000000215"
          "extract_fields" => ["interface"]
        },
        {
          "regex" => ''
          "event_name" => "网口接入"
          "event_type" => "1000065"
          "event_subtype" => "1000000214"
          "extract_fields" => []
        },
        {
          "regex" => '^<\d+>\d+.*Port\s+Alarm.*port:(?<interface>\S+\s+\S+)\s+state:Link\s+Up.*$'
          "event_name" => "网口接入"
          "event_type" => "1000065"
          "event_subtype" => "1000000214"
          "extract_fields" => ["interface"]
        }
      ]
    }
  }

  # 添加通用字段
  mutate {
    replace => {
      "device_type" => "1000079"
      "product_name" => "东土-SICOM2024M"
    }
  }

  # 保存提取字段
  mutate {
    rename => {
      "srcIp" => "source_address"
      "interface" => "action_object_name"
      "user" => "related_object_name"
    }
  }
}
