filter {
  ruby {
    path => "/usr/share/logstash/config/conf.d/ruby/event_processor.rb"
    script_params => {
      "patterns" => [
        {
          "regex" => '^(?<loginIP>\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}) \S+ \S+ \[(?<time>\S.*)\] (?<logDetails>\S.*)'
          "event_name" => "攻击告警"
          "event_type" => "1000065"
          "event_subtype" => "1000000242"
          "extract_fields" => ["loginIP", "logDetails"]
        },
        {
          "regex" => '^\[(?<time>\S.*)\] (?<loginIP>\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}) (?<logDetails>\S.*)'
          "event_name" => "攻击告警"
          "event_type" => "1000065"
          "event_subtype" => "1000000242"
          "extract_fields" => ["loginIP", "logDetails"]
        },
        {
          "regex" => '.*?ahhoneypot\{"type": "(?<type>\w+)", "payload": \{"time": "(?<time>\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})", "src_ip": "(?<srcIp>\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}|-)", "protocol": "(?<protocol>.+)", "src_port": "(?<srcPort>\d+|-)", "dest_ip": "(?<destIp>\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}|-)", "dest_port": "(?<destPort>\d+|-)"\}\}.*'
          "event_name" => "攻击告警"
          "event_type" => "1000065"
          "event_subtype" => "1000000242"
          "extract_fields" => ["type", "srcIp", "protocol", "srcPort", "destIp", "destPort"]
        },
        {
          "regex" => '^(?<loginIP>\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}) \S+ \S+ \[(?<time>\S.*)\] (?<logDetails>\S.*)'
          "event_name" => "攻击告警"
          "event_type" => "1000065"
          "event_subtype" => "1000000242"
          "extract_fields" => ["loginIP", "logDetails"]
        },
        {
          "regex" => '^\[(?<time>\S.*)\] (?<loginIP>\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}) (?<logDetails>\S.*)'
          "event_name" => "攻击告警"
          "event_type" => "1000065"
          "event_subtype" => "1000000242"
          "extract_fields" => ["loginIP", "logDetails"]
        },
        {
          "regex" => '.*?ahhoneypot\{"type": "(?<type>\w+)", "payload": \{"time": "(?<time>\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})", "src_ip": "(?<srcIp>\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}|-)", "protocol": "(?<protocol>.+)", "src_port": "(?<srcPort>\d+|-)", "dest_ip": "(?<destIp>\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}|-)", "dest_port": "(?<destPort>\d+|-)"\}\}.*'
          "event_name" => "攻击告警"
          "event_type" => "1000065"
          "event_subtype" => "1000000242"
          "extract_fields" => ["type", "srcIp", "protocol", "srcPort", "destIp", "destPort"]
        }
      ]
    }
  }

  # 添加通用字段
  mutate {
    replace => {
      "device_type" => "1000081"
      "product_name" => "南瑞信通-其他"
    }
  }

  # 保存提取字段
  mutate {
    rename => {
      "protocol" => "protocol"
      "srcIp" => "source_address"
      "srcPort" => "source_port"
      "destIp" => "dest_address"
      "loginIP" => "source_address"
      "destPort" => "dest_port"
    }
  }
}
