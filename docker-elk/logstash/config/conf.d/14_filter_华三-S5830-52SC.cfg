filter {
  ruby {
    path => "/usr/share/logstash/config/conf.d/ruby/event_processor.rb"
    script_params => {
      "patterns" => [
        {
          "regex" => 'LOGIN.*?: -DevIP=(?<DeviceIp>\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3});   Trap 1\.3\.6\.1\.4\.1\.25506\.2\.2\.1\.1\.3\.0\.1<hh3cLogIn>: login from.*$'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => ["DeviceIp"]
        },
        {
          "regex" => 'LOGINAUTHFAIL.*?: -DevIP=(?<DeviceIp>\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3});   Trap 1\.3\.6\.1\.4\.1\.25506\.2\.2\.1\.1\.3\.0\.3<hh3cLogInAuthenFailure>: failed to login from.*$'
          "event_name" => "登录失败"
          "event_type" => "1000061"
          "event_subtype" => "1000000140"
          "extract_fields" => ["DeviceIp"]
        },
        {
          "regex" => 'LOGOUT.*?: -DevIP=(?<DeviceIp>\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3});   Trap 1\.3\.6\.1\.4\.1\.25506\.2\.2\.1\.1\.3\.0\.2<hh3cLogOut>: logout from.*$'
          "event_name" => "退出登录"
          "event_type" => "1000068"
          "event_subtype" => "1000000201"
          "extract_fields" => ["DeviceIp"]
        },
        {
          "regex" => '%%.*UPDOWN\(\w+\):\s-DevIP=(?<devIp>\S+);\s+(?<interface>\S+):{0,1}\slink\sstatus\sis\sDOWN.*$'
          "event_name" => "网口拔出"
          "event_type" => "1000065"
          "event_subtype" => "1000000215"
          "extract_fields" => ["devIp", "interface"]
        },
        {
          "regex" => '%%.*LINK_UPDOWN\(\w+\):\s-DevIP=(?<devIp>\S+);\s+(?<interface>\S+):{0,1}\slink\sstatus\sis\sUP.*$'
          "event_name" => "网口接入"
          "event_type" => "1000065"
          "event_subtype" => "1000000214"
          "extract_fields" => ["devIp", "interface"]
        }
      ]
    }
  }

  # 添加通用字段
  mutate {
    replace => {
      "device_type" => "1000079"
      "product_name" => "华三-S5830-52SC"
    }
  }

  # 保存提取字段
  mutate {
    rename => {
      "DeviceIp" => "device_name"
      "interface" => "action_object_name"
    }
  }
}
