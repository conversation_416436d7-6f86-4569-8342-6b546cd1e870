filter {
  ruby {
    path => "/usr/share/logstash/config/conf.d/ruby/event_processor.rb"
    script_params => {
      "patterns" => [
        {
          "regex" => '\s+(?<lgType>\w+)/\d+/PASS:User\s+(?<Username>\S+)\(IP:(?<srcIp>\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3})\s+ID\S+\)\s+login\s+succeeded$'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => ["lgType", "Username", "srcIp"]
        },
        {
          "regex" => 'LOGIN:\s(?<user>\S+)\slogin\sfrom.*$'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => ["user"]
        },
        {
          "regex" => '^(?:[^\s]+)\s+(?<lgType>\w+)/\d+/FAIL:User\s+(?<Username>\S+)\(IP:(?<srcIp>\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}).*?$'
          "event_name" => "登录失败"
          "event_type" => "1000061"
          "event_subtype" => "1000000140"
          "extract_fields" => ["lgType", "Username", "srcIp"]
        },
        {
          "regex" => 'LOGIN_FAIL:\s(?<user>\S+)\slogin\sfrom.*$'
          "event_name" => "登录失败"
          "event_type" => "1000061"
          "event_subtype" => "1000000140"
          "extract_fields" => ["user"]
        },
        {
          "regex" => '\s+(?<lgType>\w+)/\d+/OUT:User\s+(?<Username>\S+)\(IP:(?<srcIp>\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3})\s+ID\S+\)\s+logout$'
          "event_name" => "退出登录"
          "event_type" => "1000068"
          "event_subtype" => "1000000201"
          "extract_fields" => ["lgType", "Username", "srcIp"]
        },
        {
          "regex" => 'LOGOUT:\s(?<user>\S+)\slogout\sfrom.*$'
          "event_name" => "退出登录"
          "event_type" => "1000068"
          "event_subtype" => "1000000201"
          "extract_fields" => ["user"]
        },
        {
          "regex" => 'SHELL/\d/CMD:.*ip:(?<DeviceIp>\S+)\suser:(?<Username>\S+)\scommand:(?<Command>.*)$'
          "event_name" => "配置变更"
          "event_type" => "1000026"
          "event_subtype" => "1000000094"
          "extract_fields" => ["DeviceIp", "Username", "Command"]
        },
        {
          "regex" => '^\S+\sFILTER\/\d+\/ACLDENY.*source-ip=(?<srcIp>\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3});\ssource-port=(?<srcPort>\d+);\sdestination-ip=(?<dstIp>\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3});\sdestination-port=(?<dstPort>\d+);\s+.*acl.*$'
          "event_name" => "不符合安全策略访问"
          "event_type" => "1000065"
          "event_subtype" => "1000000241"
          "extract_fields" => ["srcIp", "srcPort", "dstIp", "dstPort"]
        },
        {
          "regex" => 'SEC/\d+/\S+:\S+\s(?<srcIp>\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3}):(?<srcPort>\S+);.*\>(?<dstIp>\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3}):(?<dstPort>\S+);\s.*status:1.*$'
          "event_name" => "不符合安全策略访问"
          "event_type" => "1000065"
          "event_subtype" => "1000000241"
          "extract_fields" => ["srcIp", "srcPort", "dstIp", "dstPort"]
        },
        {
          "regex" => '^\S+\sFILTER\/\d+\/ACLPERMIT.*source-ip=(?<srcIp>\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3});\ssource-port=(?<srcPort>\d+);\sdestination-ip=(?<dstIp>\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3});\sdestination-port=(?<dstPort>\d+);\s+.*acl.*$'
          "event_name" => "符合安全策略访问"
          "event_type" => "1000065"
          "event_subtype" => "1000000241"
          "extract_fields" => ["srcIp", "srcPort", "dstIp", "dstPort"]
        },
        {
          "regex" => 'SZS-1F-ED100E-1 IFNET/5/TRAP:(?<trapOid>1\.3\.6\.1\.6\.3\.1\.1\.5\.3)Interface\s+\d+\s+Turns\s+into\s+Down\s+state'
          "event_name" => "网口拔出"
          "event_type" => "1000065"
          "event_subtype" => "1000000215"
          "extract_fields" => ["trapOid"]
        },
        {
          "regex" => 'PHY:\s+(?<interface>\S+):\s+change\s+status\s+to\s+down.*$'
          "event_name" => "网口拔出"
          "event_type" => "1000065"
          "event_subtype" => "1000000215"
          "extract_fields" => ["interface"]
        },
        {
          "regex" => '\s\S+.*UPDOWN.*interface\s(?<interface>\S+)\sturns\sinto\sDOWN\sstate.*$'
          "event_name" => "网口拔出"
          "event_type" => "1000065"
          "event_subtype" => "1000000215"
          "extract_fields" => ["interface"]
        },
        {
          "regex" => 'PHY:\s+(?<interface>\S+):\s+change\s+status\s+to\s+up.*$'
          "event_name" => "网口接入"
          "event_type" => "1000065"
          "event_subtype" => "1000000214"
          "extract_fields" => ["interface"]
        },
        {
          "regex" => '\s\S+.*UPDOWN.*interface\s(?<interface>\S+)\sturns\sinto\sUP\sstate.*$'
          "event_name" => "网口接入"
          "event_type" => "1000065"
          "event_subtype" => "1000000214"
          "extract_fields" => ["interface"]
        }
      ]
    }
  }

  # 添加通用字段
  mutate {
    replace => {
      "device_type" => "1000035"
      "product_name" => "华为-Quidway E100E"
    }
  }

  # 保存提取字段
  mutate {
    rename => {
      "dstIp" => "dest_address"
      "Command" => "action_details"
      "dstPort" => "dest_port"
      "srcIp" => "source_address"
      "DeviceIp" => "device_name"
      "user" => "related_object_name"
      "srcPort" => "source_port"
      "interface" => "action_object_name"
      "Username" => "related_object_name"
    }
  }
}
