filter {
  ruby {
    path => "/usr/share/logstash/config/conf.d/ruby/event_processor.rb"
    script_params => {
      "patterns" => [
        {
          "regex" => '^<\d+>(?<loginType>\S+):.*date="(?<time>.*)".*\s+from=(?<DeviceIp>\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3}).*admin=(?<Username>\S+)\s+act=登陆.*result=成功.*$'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => ["loginType", "DeviceIp", "Username"]
        },
        {
          "regex" => '^<\d+>webui.*mod=webui\sfrom=(?<DeviceIp>\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3})\s+agent=.*admin=(?<Username>\S+)\s+act=登录\s+result=0.*$'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => ["DeviceIp", "Username"]
        },
        {
          "regex" => '^SerialNum=.+GenTime="(?<DateTime>\d{4}-\d{2}-\d{2}\s\d{2}:\d{2}:\d{2})"\sSrcIP=(?<DeviceIp>.+)\sUserName=(?<Username>.+)\sOperate="login"\sManageStyle=(?<loginType>\S+?)\sContent="success"\s{0,2}.+$'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => ["DateTime", "DeviceIp", "Username", "loginType"]
        },
        {
          "regex" => '^<\d+>webui.*mod=webui\sfrom=(?<DeviceIp>\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3})\s+agent=.*admin=("|)(?<Username>\S+)("|)\s+act=("|)登录("|)\s+result=\d.*$'
          "event_name" => "登录失败"
          "event_type" => "1000061"
          "event_subtype" => "1000000140"
          "extract_fields" => ["DeviceIp", "Username"]
        },
        {
          "regex" => '^SerialNum=.+GenTime="(?<DateTime>\d{4}-\d{2}-\d{2}\s\d{2}:\d{2}:\d{2})"\sSrcIP=(?<DeviceIp>.+)\sUserName=(?<Username>.+)\sOperate="login"\sManageStyle=(?<lgType>\S+?)\sContent="failed for user name or password error"\s{0,2}EvtCount=\d+.*$'
          "event_name" => "登录失败"
          "event_type" => "1000061"
          "event_subtype" => "1000000140"
          "extract_fields" => ["DateTime", "DeviceIp", "Username", "lgType"]
        },
        {
          "regex" => '^<\d+>webui.*mod=webui\sfrom=(?<DeviceIp>\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3})\s+agent=.*admin=("|)(?<Username>\S+)("|)\s+act=("|)退出("|)\s+result=0.*$'
          "event_name" => "退出登录"
          "event_type" => "1000068"
          "event_subtype" => "1000000201"
          "extract_fields" => ["DeviceIp", "Username"]
        },
        {
          "regex" => '^SerialNum=.+GenTime="(?<DateTime>\d{4}-\d{2}-\d{2}\s\d{2}:\d{2}:\d{2})"\sSrcIP=(?<DeviceIp>.+)\sUserName=(?<Username>.+)\sManageStyle=(?<lgType>\S+?)\sContent="Logout"\s{0,2}.+$'
          "event_name" => "退出登录"
          "event_type" => "1000068"
          "event_subtype" => "1000000201"
          "extract_fields" => ["DateTime", "DeviceIp", "Username", "lgType"]
        },
        {
          "regex" => '^<\d+>.*?date="(?<time>.*?)"\s+dname=\S+\s+logtype=\d+\s+pri=\d+\s+ver=\S+\s+rule_name=\S+\s+mod=pf\s+sa=(?<srcIp>\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3})\s+sport=(?<srcPort>\d+?)\s+type=\S+\s+da=(?<dstIp>\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3})\s+dport=(?<dstPort>\d+?)\s+code=\S+\s+proto=\S+\s+policy=POLICY_DENY.*dsp_msg="包过滤日志"$'
          "event_name" => "不符合安全策略访问"
          "event_type" => "1000065"
          "event_subtype" => "1000000241"
          "extract_fields" => ["srcIp", "srcPort", "dstIp", "dstPort"]
        },
        {
          "regex" => 'SrcIP=(?<srcIp>\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3}) DstIP=(?<dstIp>\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3}) Protocol=(?<protocol>\S+?)\sSrcPort=(?<srcPort>\d+?)\sDstPort=(?<dstPort>\d+?)\s.*?Action=DENY Content=.*$'
          "event_name" => "不符合安全策略访问"
          "event_type" => "1000065"
          "event_subtype" => "1000000241"
          "extract_fields" => ["srcIp", "dstIp", "protocol", "srcPort", "dstPort"]
        },
        {
          "regex" => '^<\d+>.*kernel:\s+\S+:\s+(?<interface>\S+):.*NIC\s+Link\s+is\s+Down.*$'
          "event_name" => "网口拔出"
          "event_type" => "1000065"
          "event_subtype" => "1000000215"
          "extract_fields" => ["interface"]
        },
        {
          "regex" => '^<\d+?>ifevent_log:\s+devid=\d+\s+date="(?<time>.*)".*?logtype=8\s.*mod=ifstatus_down.*?dsp_msg="(?<interface>\S+?)\s+接口链路断开.*?".*$'
          "event_name" => "网口拔出"
          "event_type" => "1000065"
          "event_subtype" => "1000000215"
          "extract_fields" => ["interface"]
        },
        {
          "regex" => 'SerialNum=.+GenTime="(?<DateTime>\d{4}-\d{2}-\d{2}\s\d{2}:\d{2}:\d{2})"\sContent="interface\s(?<Interface>.+)\slink down".*$'
          "event_name" => "网口拔出"
          "event_type" => "1000065"
          "event_subtype" => "1000000215"
          "extract_fields" => ["DateTime", "Interface"]
        },
        {
          "regex" => 'kernel:\s+\S+:\s+(?<interface>\S+):.*NIC\s+Link\s+is\s+Up .*?$'
          "event_name" => "网口接入"
          "event_type" => "1000065"
          "event_subtype" => "1000000214"
          "extract_fields" => ["interface"]
        },
        {
          "regex" => '^<\d+?>ifevent_log:\s+devid=\d+\s+date="(?<time>.*)".*?logtype=8\s.*mod=ifstatus_up.*?dsp_msg="(?<interface>\S+?)\s+接口链路连接.*?".*$'
          "event_name" => "网口接入"
          "event_type" => "1000065"
          "event_subtype" => "1000000214"
          "extract_fields" => ["interface"]
        },
        {
          "regex" => '^SerialNum=.+GenTime="(?<DateTime>\d{4}-\d{2}-\d{2}\s\d{2}:\d{2}:\d{2})"\sContent="interface\s(?<Interface>.+)\slink up".+$'
          "event_name" => "网口接入"
          "event_type" => "1000065"
          "event_subtype" => "1000000214"
          "extract_fields" => ["DateTime", "Interface"]
        }
      ]
    }
  }

  # 添加通用字段
  mutate {
    replace => {
      "device_type" => "1000035"
      "product_name" => "启明星辰-USG-310"
    }
  }

  # 保存提取字段
  mutate {
    rename => {
      "dstIp" => "dest_address"
      "DateTime" => "action_details"
      "protocol" => "protocol"
      "dstPort" => "dest_port"
      "srcIp" => "source_address"
      "DeviceIp" => "device_name"
      "interface" => "action_object_name"
      "srcPort" => "source_port"
      "Interface" => "action_object_name"
      "Username" => "related_object_name"
    }
  }
}
