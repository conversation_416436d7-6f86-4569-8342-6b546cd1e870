filter {
  ruby {
    path => "/usr/share/logstash/config/conf.d/ruby/event_processor.rb"
    script_params => {
      "patterns" => [
        {
          "regex" => 'user\s+login.+\S+.*\s+UserName=+(?<username>.+)\S+\s+UserIP=+(?<ipaddress>.+),.*$'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => ["username", "ipaddress"]
        },
        {
          "regex" => 'LOGIN.*?succeeded\s+in\s+logging\s+in\s+to\s+\S+?\.\s+\(UserType=(?<LoginType>\S+),\s+UserName=(?<username>\S+),.*Ip=(?<srcIp>\S+),.*$'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => ["LoginType", "username", "srcIp"]
        },
        {
          "regex" => 'Failed\s+to\s+login.\s+\S+Ip=(?<ipaddress>.*)\S+\s+UserName=(?<username>.+),\s+Time.*AccessType=(?<type>\S+),.*$'
          "event_name" => "登录失败"
          "event_type" => "1000061"
          "event_subtype" => "1000000140"
          "extract_fields" => ["ipaddress", "username", "type"]
        },
        {
          "regex" => 'user\s+logout.+\S+.*\s+UserName=+(?<username>.+)\S+\s+UserIP=+(?<ipaddress>.+),.*$'
          "event_name" => "退出登录"
          "event_type" => "1000068"
          "event_subtype" => "1000000201"
          "extract_fields" => ["username", "ipaddress"]
        },
        {
          "regex" => 'LOGOUT.*?succeeded\s+in\s+logging\s+out\s+of\s+\S+?\.\s\(UserType=(?<LoginType>.*?)\,\sUserName=(?<username>\S+).*Ip=(?<srcIp>\S+),.*$'
          "event_name" => "退出登录"
          "event_type" => "1000068"
          "event_subtype" => "1000000201"
          "extract_fields" => ["LoginType", "username", "srcIp"]
        },
        {
          "regex" => 'Interface\s+(?<interface>.+)\s+has.*into\s+DOWN\s+.*$'
          "event_name" => "网口拔出"
          "event_type" => "1000065"
          "event_subtype" => "1000000215"
          "extract_fields" => ["interface"]
        },
        {
          "regex" => 'Interface\s+(?<interface>.+)\s+has.*into\s+UP\s+.*$'
          "event_name" => "网口接入"
          "event_type" => "1000065"
          "event_subtype" => "1000000214"
          "extract_fields" => ["interface"]
        }
      ]
    }
  }

  # 添加通用字段
  mutate {
    replace => {
      "device_type" => "1000079"
      "product_name" => "华为-S5720-28X-SI"
    }
  }

  # 保存提取字段
  mutate {
    rename => {
      "username" => "related_object_name"
      "srcIp" => "source_address"
      "interface" => "action_object_name"
    }
  }
}
