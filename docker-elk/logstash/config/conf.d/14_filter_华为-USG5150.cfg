filter {
  ruby {
    path => "/usr/share/logstash/config/conf.d/ruby/event_processor.rb"
    script_params => {
      "patterns" => [
        {
          "regex" => '^protocol=(?<proto>\d+),\ssource-ip=(?<srcIp>\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3}),\ssource-port=(?<srcPort>\d+),\sdestination-ip=(?<dstIp>\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3}),\sdestination-port=(?<dstPort>\d+),.*?$'
          "event_name" => "不符合安全策略访问"
          "event_type" => "1000065"
          "event_subtype" => "1000000241"
          "extract_fields" => ["proto", "srcIp", "srcPort", "dstIp", "dstPort"]
        },
        {
          "regex" => '(?<hostname>.*)\s\w+/\d+/IF_PVCDOWN:.*\s接口(?<NetPort>\S+)\s变为Down.*$'
          "event_name" => "网口拔出"
          "event_type" => "1000065"
          "event_subtype" => "1000000215"
          "extract_fields" => ["NetPort"]
        },
        {
          "regex" => '%%\d+\w+/\d+/STATUSDOWN\(\w+\):\s(?<NetPort>\S+):端口状态变为DOWN.*$'
          "event_name" => "网口拔出"
          "event_type" => "1000065"
          "event_subtype" => "1000000215"
          "extract_fields" => ["NetPort"]
        },
        {
          "regex" => '%%\d+\w+/\d+/STATUSUP\(\w+\):\s(?<NetPort>\S+):端口状态变为UP.*$'
          "event_name" => "网口接入"
          "event_type" => "1000065"
          "event_subtype" => "1000000214"
          "extract_fields" => ["NetPort"]
        },
        {
          "regex" => 'IF_PVCUP:.*\s接口(?<NetPort>\S+)\s变为Up.*$'
          "event_name" => "网口接入"
          "event_type" => "1000065"
          "event_subtype" => "1000000214"
          "extract_fields" => ["NetPort"]
        }
      ]
    }
  }

  # 添加通用字段
  mutate {
    replace => {
      "device_type" => "1000035"
      "product_name" => "华为-USG5150"
    }
  }

  # 保存提取字段
  mutate {
    rename => {
      "dstIp" => "dest_address"
      "proto" => "protocol"
      "dstPort" => "dest_port"
      "srcIp" => "source_address"
      "srcPort" => "source_port"
      "NetPort" => "dest_port"
    }
  }
}
