filter {
  ruby {
    path => "/usr/share/logstash/config/conf.d/ruby/event_processor.rb"
    script_params => {
      "patterns" => [
        {
          "regex" => '^<\d+?>.*:.*%.*:\s+Login\s+Success\s+\[user:\s+(?<user>\S+)\]\s+\[Source:\s+(?<srcIp>\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3})\].*at\s+(?<time>.*).*$'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => ["user", "srcIp", "time"]
        },
        {
          "regex" => ':\s+.*:\s+Login\s+Success\s+\[user:\s+(?<user>\S+)\]\s+\[Source:\s+(?<srcIp>\S+)\].*$'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => ["user", "srcIp"]
        },
        {
          "regex" => '^<\d+?>.*:\s+%.*:\s+Login\s+Success\s+\[user:\s+(?<user>\S+)\]\s+\[Source:\s+UNKNOWN.*$'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => ["user"]
        },
        {
          "regex" => '^<\d+?>.*:.*%.*:\s+Login\s+failed\s+\[user:\s+(?<user>\S+)\]\s+\[Source:\s+(?<srcIp>\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3})\].*Authentication\s+Failed.*at\s+(?<time>.*)$'
          "event_name" => "登录失败"
          "event_type" => "1000061"
          "event_subtype" => "1000000140"
          "extract_fields" => ["user", "srcIp", "time"]
        },
        {
          "regex" => '%.*:\s+Still\s+timeleft\s+for\s+watching\s+failures.*\[user:\s+(?<user>\S+)\]\s+\[Source:\s{0,1}(?<srcIp>\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3})\].*$'
          "event_name" => "登录失败"
          "event_type" => "1000061"
          "event_subtype" => "1000000140"
          "extract_fields" => ["user", "srcIp"]
        },
        {
          "regex" => '^%.*\sLogin\sfailed\s\[user:(?<user>.*)\]\s\[Source:\s(?<srcIp>\S+)\]\s\[localport:\s(?<port>\d+)\].*$'
          "event_name" => "登录失败"
          "event_type" => "1000061"
          "event_subtype" => "1000000140"
          "extract_fields" => ["user", "srcIp", "port"]
        },
        {
          "regex" => '^%.*\sConfigured.*\sby\s(?<Username>\S+).*\s(?<DeviceIp>\S+)$'
          "event_name" => "配置变更"
          "event_type" => "1000026"
          "event_subtype" => "1000000094"
          "extract_fields" => ["Username", "DeviceIp"]
        },
        {
          "regex" => '^%.*Interface\s(?<interface>\S+),\s+changed\sstate\sto\sdown.*$'
          "event_name" => "网口拔出"
          "event_type" => "1000065"
          "event_subtype" => "1000000215"
          "extract_fields" => ["interface"]
        },
        {
          "regex" => 'CHANGED.*Interface\s(?<interface>\S+),\s+changed\sstate\sto.*down.*$'
          "event_name" => "网口拔出"
          "event_type" => "1000065"
          "event_subtype" => "1000000215"
          "extract_fields" => ["interface"]
        },
        {
          "regex" => '^%.*Interface\s(?<interface>\S+),\s+changed\sstate\sto\sup.*$'
          "event_name" => "网口接入"
          "event_type" => "1000065"
          "event_subtype" => "1000000214"
          "extract_fields" => ["interface"]
        }
      ]
    }
  }

  # 添加通用字段
  mutate {
    replace => {
      "device_type" => "1000079"
      "product_name" => "思科-3560"
    }
  }

  # 保存提取字段
  mutate {
    rename => {
      "port" => "source_port"
      "srcIp" => "source_address"
      "DeviceIp" => "device_name"
      "interface" => "action_object_name"
      "user" => "related_object_name"
      "time" => "action_details"
      "Username" => "related_object_name"
    }
  }
}
