filter {
  ruby {
    path => "/usr/share/logstash/config/conf.d/ruby/event_processor.rb"
    script_params => {
      "patterns" => [
        {
          "regex" => '%%\d+\w+/\d+/\S+:\s+access\s+type:(?<lgType>\w+)\s+vsys:(?<sysUser>\S+)\s+user:(?<username>\S+)\s+login\s+from\s+(?<srcIp>\S+)$'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => ["lgType", "sysUser", "username", "srcIp"]
        },
        {
          "regex" => '%%\d+(?<lgType>HTTPD)/\d+/\S+:\s+User\s+(?<username>\S+)\(IP:(?<srcIp>\S+)\s+ID:\d+\)\s+login\s+succeeded.*$'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => ["lgType", "username", "srcIp"]
        },
        {
          "regex" => '%%\d+(?<lgType>SSH)/\d+/\S+:\s+Failed\s+to\s+login\s+through\s+\w+.\s+\(Ip=(?<srcIp>\S+),\s+UserName=(?<username>\S+),\s+Times=\d+\).$'
          "event_name" => "登录失败"
          "event_type" => "1000061"
          "event_subtype" => "1000000140"
          "extract_fields" => ["lgType", "srcIp", "username"]
        },
        {
          "regex" => '%%\d+(?<lgType>HTTPD)/\d+/\S+:\s+User\s+(?<username>\S+)\(IP:(?<srcIp>\S+)\s+ID:\d+\)\s+logout.*$'
          "event_name" => "退出登录"
          "event_type" => "1000068"
          "event_subtype" => "1000000201"
          "extract_fields" => ["lgType", "username", "srcIp"]
        },
        {
          "regex" => '%%.*POLICYDENY\(l\).*source-ip=(?<srcIp>\S+),\s+source-port=(?<srcPort>\d+),\s+destination-ip=(?<dstIp>\S+),\s+destination-port=(?<dstPort>\d+).*$'
          "event_name" => "不符合安全策略访问"
          "event_type" => "1000065"
          "event_subtype" => "1000000241"
          "extract_fields" => ["srcIp", "srcPort", "dstIp", "dstPort"]
        }
      ]
    }
  }

  # 添加通用字段
  mutate {
    replace => {
      "device_type" => "1000035"
      "product_name" => "华为-USG6550"
    }
  }

  # 保存提取字段
  mutate {
    rename => {
      "username" => "related_object_name"
      "dstIp" => "dest_address"
      "dstPort" => "dest_port"
      "srcIp" => "source_address"
      "srcPort" => "source_port"
      "sysUser" => "related_object_name"
    }
  }
}
