filter {
  ruby {
    path => "/usr/share/logstash/config/conf.d/ruby/device_quota.rb"
    script_params => {
      "es_host" => "elasticsearch:9200"
      "es_user" => "logstash_internal"
      "es_password" => "${LOGSTASH_INTERNAL_PASSWORD}"
      "max_devices" => 999
      "cache_ttl" => 300 # seconds
    }
  }

  mutate {
    replace => { 
      "[@metadata][device_quota_update]" => "1732097612" 
      collector_address => "*************"
    }
  }
}