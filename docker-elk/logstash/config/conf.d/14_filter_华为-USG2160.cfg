filter {
  ruby {
    path => "/usr/share/logstash/config/conf.d/ruby/event_processor.rb"
    script_params => {
      "patterns" => [
        {
          "regex" => 'SHELL/4/LOGIN:\s+(?<user>\S+)\s+login\s+from.*$'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => ["user"]
        },
        {
          "regex" => 'HTTPD.*User\s+(?<user>\S+)\(IP:(?<srcIp>\S+).*login\s+succeeded.*$'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => ["user", "srcIp"]
        },
        {
          "regex" => 'user:(?<user>\S+).*login\s+from\s+.*$'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => ["user"]
        },
        {
          "regex" => 'SHELL/4/LOGIN_FAIL:\s+(?<user>\S+)\s+login\s+from.*failed.*$'
          "event_name" => "登录失败"
          "event_type" => "1000061"
          "event_subtype" => "1000000140"
          "extract_fields" => ["user"]
        },
        {
          "regex" => 'User login\s+failed\s+from.*$'
          "event_name" => "登录失败"
          "event_type" => "1000061"
          "event_subtype" => "1000000140"
          "extract_fields" => []
        },
        {
          "regex" => 'SHELL/4/LOGOUT:\s+(?<user>\S+)\s+logout\s+from.*$'
          "event_name" => "退出登录"
          "event_type" => "1000068"
          "event_subtype" => "1000000201"
          "extract_fields" => ["user"]
        },
        {
          "regex" => 'HWCM/4/EXIT:\s+exit.*$'
          "event_name" => "退出登录"
          "event_type" => "1000068"
          "event_subtype" => "1000000201"
          "extract_fields" => []
        },
        {
          "regex" => 'user:(?<user>\S+).*logout\s+from\s.*$'
          "event_name" => "退出登录"
          "event_type" => "1000068"
          "event_subtype" => "1000000201"
          "extract_fields" => ["user"]
        },
        {
          "regex" => 'task:.*ip:(?<srcIp>.*)\s+user:(?<user>\S+).*command:(?<Command>.*?)\..*$'
          "event_name" => "配置变更"
          "event_type" => "1000026"
          "event_subtype" => "1000000094"
          "extract_fields" => ["srcIp", "user", "Command"]
        },
        {
          "regex" => 'FILTER\/\d+\/ACLDENY.*?source-ip=(?<srcIp>\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3}).*\s+source-port=(?<srcPort>\d+).*\s+destination-ip=(?<dstIp>\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3}).*\s+destination-port=(?<dstPort>\d+).*?$'
          "event_name" => "不符合安全策略访问"
          "event_type" => "1000065"
          "event_subtype" => "1000000241"
          "extract_fields" => ["srcIp", "srcPort", "dstIp", "dstPort"]
        },
        {
          "regex" => 'FILTER\/\d+\/ACLDENY.*?source-ip=(?<srcIp>\d{1,3}(?:\.\d{1,3}){3}).*\s+source-port=(?<srcPort>\d+).*\s+destination-ip=(?<dstIp>\d{1,3}(?:\.\d{1,3}){3}).*\s+destination-port=(?<dstPort>\d+).*?$'
          "event_name" => "不符合安全策略访问"
          "event_type" => "1000065"
          "event_subtype" => "1000000241"
          "extract_fields" => ["srcIp", "srcPort", "dstIp", "dstPort"]
        },
        {
          "regex" => 'FILTER\/\d+\/ACLDENY.*?source-ip=(?<srcIp>\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3});\s+destination-ip=(?<dstIp>\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3}).*?$'
          "event_name" => "不符合安全策略访问"
          "event_type" => "1000065"
          "event_subtype" => "1000000241"
          "extract_fields" => ["srcIp", "dstIp"]
        },
        {
          "regex" => 'source-ip=(?<srcIp>\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}), source-port=(?<srcPort>\d+), destination-ip=(?<dstIp>\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}), destination-port=(?<dstPort>\d+), ?time=\S+\s+\S+, interzone-trust\(\S+\)-untrust\(\S+\) outbound, policy=\d+\.$'
          "event_name" => "不符合安全策略访问"
          "event_type" => "1000065"
          "event_subtype" => "1000000241"
          "extract_fields" => ["srcIp", "srcPort", "dstIp", "dstPort"]
        },
        {
          "regex" => '^(?=.*protocol=\d+).*(source-ip=(?<srcIp>\d{1,3}(?:\.\d{1,3}){3})).*source-port=(?<srcPort>\d+).*destination-ip=(?<dstIp>\d{1,3}(?:\.\d{1,3}){3}).*destination-port=(?<dstPort>\d+).*'
          "event_name" => "符合安全策略访问"
          "event_type" => "1000065"
          "event_subtype" => "1000000241"
          "extract_fields" => ["srcIp", "srcPort", "dstIp", "dstPort"]
        },
        {
          "regex" => 'IFNET\/4\/TRAP:1\.3\.6\.1\.6\.3\.1\.1\.5\.3.*$'
          "event_name" => "网口拔出"
          "event_type" => "1000065"
          "event_subtype" => "1000000215"
          "extract_fields" => []
        },
        {
          "regex" => 'IFNET.*interface\s+(?<interface>\S+)\s+turns\s+into\s+DOWN\s+state.*$'
          "event_name" => "网口拔出"
          "event_type" => "1000065"
          "event_subtype" => "1000000215"
          "extract_fields" => ["interface"]
        },
        {
          "regex" => 'PHY:\s+(?<interface>\S+):\s+change\s+status\s+to\s+down.*$'
          "event_name" => "网口拔出"
          "event_type" => "1000065"
          "event_subtype" => "1000000215"
          "extract_fields" => ["interface"]
        },
        {
          "regex" => 'Interface\s+(?<interface>\S+).*turned\s+into\s+DOWN.*$'
          "event_name" => "网口拔出"
          "event_type" => "1000065"
          "event_subtype" => "1000000215"
          "extract_fields" => ["interface"]
        },
        {
          "regex" => 'Port\s+(?<interface>\S+)\s+was\s+changed\s+to\s+DOWN.*$'
          "event_name" => "网口拔出"
          "event_type" => "1000065"
          "event_subtype" => "1000000215"
          "extract_fields" => ["interface"]
        },
        {
          "regex" => '^(?<interface>\S+)\s+changed\s+status\s+to\s+down.*$'
          "event_name" => "网口拔出"
          "event_type" => "1000065"
          "event_subtype" => "1000000215"
          "extract_fields" => ["interface"]
        },
        {
          "regex" => 'PHY:\s+(?<interface>\S+):\s+change\s+status\s+to\s+up.*$'
          "event_name" => "网口接入"
          "event_type" => "1000065"
          "event_subtype" => "1000000214"
          "extract_fields" => ["interface"]
        },
        {
          "regex" => 'IFNET.*interface\s+(?<interface>\S+)\s+turns\s+into\s+UP\s+state.*$'
          "event_name" => "网口接入"
          "event_type" => "1000065"
          "event_subtype" => "1000000214"
          "extract_fields" => ["interface"]
        },
        {
          "regex" => 'Port\s+(?<interface>\S+)\s+was\s+changed\s+to\s+UP.*$'
          "event_name" => "网口接入"
          "event_type" => "1000065"
          "event_subtype" => "1000000214"
          "extract_fields" => ["interface"]
        },
        {
          "regex" => 'Interface\s+(?<interface>\S+).*turned\s+into\s+UP.*$'
          "event_name" => "网口接入"
          "event_type" => "1000065"
          "event_subtype" => "1000000214"
          "extract_fields" => ["interface"]
        },
        {
          "regex" => '^(?<interface>\S+)\s+changed\s+status\s+to\s+up.*$'
          "event_name" => "网口接入"
          "event_type" => "1000065"
          "event_subtype" => "1000000214"
          "extract_fields" => ["interface"]
        }
      ]
    }
  }

  # 添加通用字段
  mutate {
    replace => {
      "device_type" => "1000035"
      "product_name" => "华为-USG2160"
    }
  }

  # 保存提取字段
  mutate {
    rename => {
      "dstIp" => "dest_address"
      "Command" => "action_details"
      "dstPort" => "dest_port"
      "srcIp" => "source_address"
      "interface" => "action_object_name"
      "user" => "related_object_name"
      "srcPort" => "source_port"
    }
  }
}
