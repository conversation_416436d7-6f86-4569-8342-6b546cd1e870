filter {
  ruby {
    path => "/usr/share/logstash/config/conf.d/ruby/event_processor.rb"
    script_params => {
      "patterns" => [
        {
          "regex" => '(?<srcIp>\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3}).*:(?<logType>\S+)\s+user\s+'(?<user>\S+)'\s+logged\s+in\s+with\s+admin\s+level.*$'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => ["srcIp", "logType", "user"]
        },
        {
          "regex" => 'RSG2300:(?<logType>\S+)\s+user\s'(?<user>\S+)'\slogged\sin.*$'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => ["logType", "user"]
        },
        {
          "regex" => 'RSG2300:(?<logType>\S+)\s+user\s'(?<user>\S+)'\sloged\sin.*IP:\s+(?<srcIp>\S+)\).*$'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => ["logType", "user", "srcIp"]
        },
        {
          "regex" => '(?<srcip>\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3}).*Failed\s+Console\s+user\s+(?<Username>\S+)\s+login\s+attempt.*$'
          "event_name" => "登录失败"
          "event_type" => "1000061"
          "event_subtype" => "1000000140"
          "extract_fields" => ["srcip", "Username"]
        },
        {
          "regex" => '(?<srcip>\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3}).*Failed\s+Console\s+user\s+\'(?<Username>\S+)\'\s+login\s+attempt.*$'
          "event_name" => "登录失败"
          "event_type" => "1000061"
          "event_subtype" => "1000000140"
          "extract_fields" => ["srcip", "Username"]
        },
        {
          "regex" => 'RSG2300:Failed.*user\s\'(?<user>\w+).*IP:\s(?<srcIp>\S+)\)$'
          "event_name" => "登录失败"
          "event_type" => "1000061"
          "event_subtype" => "1000000140"
          "extract_fields" => ["user", "srcIp"]
        },
        {
          "regex" => ':(?<user>\S+)\slogged\sout.*$'
          "event_name" => "退出登录"
          "event_type" => "1000068"
          "event_subtype" => "1000000201"
          "extract_fields" => ["user"]
        },
        {
          "regex" => ':(?<interface>Port\s+\S+)\s+is\s+down.*$'
          "event_name" => "网口拔出"
          "event_type" => "1000065"
          "event_subtype" => "1000000215"
          "extract_fields" => ["srcIp", "interface"]
        },
        {
          "regex" => 'RSG2300:(?<port>Port\s+\S+)\s+was\s+down.*$'
          "event_name" => "网口拔出"
          "event_type" => "1000065"
          "event_subtype" => "1000000215"
          "extract_fields" => ["port"]
        },
        {
          "regex" => ':(?<interface>Port\s+\S+)\s+is\s+up.*$'
          "event_name" => "网口接入"
          "event_type" => "1000065"
          "event_subtype" => "1000000214"
          "extract_fields" => ["srcIp", "interface"]
        },
        {
          "regex" => ':(?<interface>Port\s+\S+).*from\sLEARNING\s+to\sFORWARDING$'
          "event_name" => "网口接入"
          "event_type" => "1000065"
          "event_subtype" => "1000000214"
          "extract_fields" => ["interface"]
        }
      ]
    }
  }

  # 添加通用字段
  mutate {
    replace => {
      "device_type" => "1000079"
      "product_name" => "罗杰康-RSG2300"
    }
  }

  # 保存提取字段
  mutate {
    rename => {
      "srcip" => "source_address"
      "port" => "source_port"
      "srcIp" => "source_address"
      "interface" => "action_object_name"
      "user" => "related_object_name"
      "Username" => "related_object_name"
    }
  }
}
