filter {
  ruby {
    path => "/usr/share/logstash/config/conf.d/ruby/event_processor.rb"
    script_params => {
      "patterns" => [
        {
          "regex" => '^7\.<TSGZ>\d{8}:(?<content>.*)\s+from\s+(?<targetMAC>([0-9a-fA-F]{2})(([/\s:][0-9a-fA-F]{2}){5}))\s+to\s+(?<attackerMAC>([0-9a-fA-F]{2})(([/\s:][0-9a-fA-F]{2}){5}))\s+at\s+time\s+\d{8}\s+\d{1,2}:\d{1,2}:\d{1,2}\s+([0-9a-fA-F]{2})(([/\s:][0-9a-fA-F]{2}){5})\sis\schanged.*$'
          "event_name" => "攻击告警"
          "event_type" => "1000065"
          "event_subtype" => "1000000242"
          "extract_fields" => ["content", "targetMAC", "attackerMAC"]
        },
        {
          "regex" => 'ahhoneypot\{"type": "(?<type>\w+)", "payload": \{"time": "(?<time>\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})", "src_ip": "(?<srcIp>\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}|-)", "protocol": "(?<protocol>.+)", "src_port": "(?<srcPort>\d+|-)", "dest_ip": "(?<destIp>\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}|-)", "dest_port": "(?<destPort>\d+|-)"\}\}'
          "event_name" => "攻击告警"
          "event_type" => "1000065"
          "event_subtype" => "1000000242"
          "extract_fields" => ["type", "srcIp", "protocol", "srcPort", "destIp", "destPort"]
        },
        {
          "regex" => '^\[(?<time>\S.*)\] (?<loginIP>\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}) (?<logDetails>\S.*)'
          "event_name" => "攻击告警"
          "event_type" => "1000065"
          "event_subtype" => "1000000242"
          "extract_fields" => ["loginIP", "logDetails"]
        },
        {
          "regex" => '^(?<loginIP>\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}) \S+ \S+ \[(?<time>\S.*)\] (?<logDetails>\S.*)'
          "event_name" => "攻击告警"
          "event_type" => "1000065"
          "event_subtype" => "1000000242"
          "extract_fields" => ["loginIP", "logDetails"]
        },
        {
          "regex" => '^(?<loginIP>\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}) \S+ \S+ \[(?<time>\S.*)\] (?<logDetails>\S.*)'
          "event_name" => "攻击告警"
          "event_type" => "1000065"
          "event_subtype" => "1000000242"
          "extract_fields" => ["loginIP", "logDetails"]
        },
        {
          "regex" => '^\[(?<time>\S.*)\] (?<loginIP>\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}) (?<logDetails>\S.*)'
          "event_name" => "攻击告警"
          "event_type" => "1000065"
          "event_subtype" => "1000000242"
          "extract_fields" => ["loginIP", "logDetails"]
        },
        {
          "regex" => '^(?<loginIP>\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}) \S+ \S+ \[(?<time>\S.*)\] (?<logDetails>\S.*)'
          "event_name" => "攻击告警"
          "event_type" => "1000065"
          "event_subtype" => "1000000242"
          "extract_fields" => ["loginIP", "logDetails"]
        }
      ]
    }
  }

  # 添加通用字段
  mutate {
    replace => {
      "device_type" => "1000081"
      "product_name" => "华三-CSA1800A"
    }
  }

  # 保存提取字段
  mutate {
    rename => {
      "attackerMAC" => "source_mac"
      "protocol" => "protocol"
      "loginIP" => "source_address"
      "srcIp" => "source_address"
      "srcPort" => "source_port"
      "content" => "action_details"
      "destIp" => "dest_address"
      "destPort" => "dest_port"
    }
  }
}
