filter {
  ruby {
    path => "/usr/share/logstash/config/conf.d/ruby/event_processor.rb"
    script_params => {
      "patterns" => [
        {
          "regex" => 'VTYUSERLOGIN:OID.*?\s+A\s+user\s+login.\s+\(UserIndex=0,\s+UserName=(?<username>\S+),\s+UserIP=(?<srcIP>\S+),\s+UserChannel=.*$'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => ["username", "srcIP"]
        },
        {
          "regex" => 'LOGIN.*?succeeded\s+in\s+logging\s+in\s+to\s+\S+?\.\s+\(UserType=(?<LoginType>.+?)\,\s+UserName=(?<username>\S+?)\,\s+Ip=(?<srcIP>.*?)\,\s+VpnName=(?<vpnname>.*)\).*$'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => ["LoginType", "username", "srcIP", "vpnname"]
        },
        {
          "regex" => 'LOGIN.*\s+(?<Username>\S+)\s+login\s+from\s+(?<sourceIp>\S+).*?$'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => ["Username", "sourceIp"]
        },
        {
          "regex" => 'LOGIN.*?succeeded in logging in to\s\S+?\.\s\(UserType=(?<LoginType>.+?)\,\sUserName=(?<username>\S+?)\,\sIp=(?<srcIP>.*?)\,\sVpnName=(?<vpnname>.*)\).*$'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => ["LoginType", "username", "srcIP", "vpnname"]
        },
        {
          "regex" => 'LOGINFAILED.*?Failed\s+to\s+login.*\(Ip=(?<srcIp>.*),\s+UserName=(?<username>\S+),\s+Times=.*AccessType=(?<type>\S+)\).*$'
          "event_name" => "登录失败"
          "event_type" => "1000061"
          "event_subtype" => "1000000140"
          "extract_fields" => ["srcIp", "username", "type"]
        },
        {
          "regex" => 'LOGINFAILED.*?Failed\s+to\s+login\.\s+\(Ip=(?<ip>.*?),\s+UserName=(?<username>\S+?)\,\s+Times=.*?\,\sAccessType=(?<LoginType>.+?)\).*$'
          "event_name" => "登录失败"
          "event_type" => "1000061"
          "event_subtype" => "1000000140"
          "extract_fields" => ["ip", "username", "LoginType"]
        },
        {
          "regex" => 'SSH_FAIL.*Failed\s+to\s+login\s+through.*IP=(?<srcIp>\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3}),.*UserName=(?<user>\S+),.*$'
          "event_name" => "登录失败"
          "event_type" => "1000061"
          "event_subtype" => "1000000140"
          "extract_fields" => ["srcIp", "user"]
        },
        {
          "regex" => 'TELNETFAILED.*Login\s+through\s+telnet\s+failed\(\s+ip=\s+times=\d+\).*$'
          "event_name" => "登录失败"
          "event_type" => "1000061"
          "event_subtype" => "1000000140"
          "extract_fields" => []
        },
        {
          "regex" => 'LOGINFAILED.*?Failed to login\.\s\(Ip=(?<ip>.*?),\sUserName=(?<username>\S+?)\,\sTimes=.*?\,\sAccessType=(?<LoginType>.+?)\).*$'
          "event_name" => "登录失败"
          "event_type" => "1000061"
          "event_subtype" => "1000000140"
          "extract_fields" => ["ip", "username", "LoginType"]
        },
        {
          "regex" => 'VTYUSERLOGOUT:OID.*?\s+A\suser\s+logout.\s+\(UserIndex=0,\s+UserName=(?<username>\S+),\s+UserIP=(?<srcIP>\S+),\s+UserChannel=.*$'
          "event_name" => "退出登录"
          "event_type" => "1000068"
          "event_subtype" => "1000000201"
          "extract_fields" => ["username", "srcIP"]
        },
        {
          "regex" => 'LOGOUT.*?succeeded\s+in\s+logging\s+out\s+of\s\S+?\.\s\(UserType=(?<LoginType>\S+?)\,\sUserName=(?<username>\S+?)\,\sIp=(?<srcIP>.*?)\,\sVpnName=(?<vpnname>.*?)\).*$'
          "event_name" => "退出登录"
          "event_type" => "1000068"
          "event_subtype" => "1000000201"
          "extract_fields" => ["LoginType", "username", "srcIP", "vpnname"]
        },
        {
          "regex" => 'SSH_CONNECT_CLOSED.*:SSH\s+connect\s+was\s+closed.*IP=(?<srcIp>\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3}).*UserName=(?<user>\S+).*$'
          "event_name" => "退出登录"
          "event_type" => "1000068"
          "event_subtype" => "1000000201"
          "extract_fields" => ["srcIp", "user"]
        },
        {
          "regex" => 'LOGOUT.*\s+(?<Username>\S+)\s+logout\s+from\s+(?<sourceIp>\S+).*$'
          "event_name" => "退出登录"
          "event_type" => "1000068"
          "event_subtype" => "1000000201"
          "extract_fields" => ["Username", "sourceIp"]
        },
        {
          "regex" => 'LOGOUT.*?succeeded in logging out of\s\S+?\.\s\(UserType=(?<LoginType>\S+?)\,\sUserName=(?<username>\S+?)\,\sIp=(?<srcIP>.*?)\,\sVpnName=(?<vpnname>.*?)\).*$'
          "event_name" => "退出登录"
          "event_type" => "1000068"
          "event_subtype" => "1000000201"
          "extract_fields" => ["LoginType", "username", "srcIP", "vpnname"]
        },
        {
          "regex" => 'CMDRECORD.*:Record.*command\s+information.*IP=(?<srcIp>\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3}).*User=(?<user>\S+),.*Command="(?<Command>.*)".*$'
          "event_name" => "配置变更"
          "event_type" => "1000026"
          "event_subtype" => "1000000094"
          "extract_fields" => ["srcIp", "user", "Command"]
        },
        {
          "regex" => '%%01IFNET/\d+\/UPDOWN.*interface\s+(?<interface>.*)\s+was\s+changed\s+to\s+DOWN.*$'
          "event_name" => "网口拔出"
          "event_type" => "1000065"
          "event_subtype" => "1000000215"
          "extract_fields" => ["interface"]
        },
        {
          "regex" => 'IF_LINKDOWN.*:.*Interface.*into\s+DOWN\s+state.*InterfaceName=(?<interface>\S+)\).*'
          "event_name" => "网口拔出"
          "event_type" => "1000065"
          "event_subtype" => "1000000215"
          "extract_fields" => ["interface"]
        },
        {
          "regex" => 'IF_STATE.*:.*Interface\s+(?<interface>\S+).*into\s+DOWN\s+state.*$'
          "event_name" => "网口拔出"
          "event_type" => "1000065"
          "event_subtype" => "1000000215"
          "extract_fields" => ["interface"]
        },
        {
          "regex" => 'LINK_STATE.*:.*interface\s+(?<interface>\S+).*has\s+entered\s+the\s+DOWN\s+state.*$'
          "event_name" => "网口拔出"
          "event_type" => "1000065"
          "event_subtype" => "1000000215"
          "extract_fields" => ["interface"]
        },
        {
          "regex" => '^Syslog\smessage:\s\S+?\s(?<time>\S+?\s\s\d{1,2}\s\d{4}\s\d{2}:\d{2}:\d{2}).*?IF_STATE.*?Interface\s(?<interface>\S+?)\shas\sturned\sinto\sDOWN\sstate.*$'
          "event_name" => "网口拔出"
          "event_type" => "1000065"
          "event_subtype" => "1000000215"
          "extract_fields" => ["interface"]
        },
        {
          "regex" => '%%01IFNET/\d+\/UPDOWN.*interface\s+(?<interface>.*)\s+was\s+changed\s+to\s+UP.*$'
          "event_name" => "网口接入"
          "event_type" => "1000065"
          "event_subtype" => "1000000214"
          "extract_fields" => ["interface"]
        },
        {
          "regex" => 'IF_LINKDOWN.*:.*Interface.*into\s+UP\s+state.*InterfaceName=(?<interface>\S+)\).*'
          "event_name" => "网口接入"
          "event_type" => "1000065"
          "event_subtype" => "1000000214"
          "extract_fields" => ["interface"]
        },
        {
          "regex" => 'IF_STATE.*:.*Interface\s+(?<interface>\S+).*into\s+UP\s+state.*$'
          "event_name" => "网口接入"
          "event_type" => "1000065"
          "event_subtype" => "1000000214"
          "extract_fields" => ["interface"]
        },
        {
          "regex" => 'LINK_STATE.*:.*interface\s+(?<interface>\S+).*has\s+entered\s+the\s+UP\s+state.*$'
          "event_name" => "网口接入"
          "event_type" => "1000065"
          "event_subtype" => "1000000214"
          "extract_fields" => ["interface"]
        },
        {
          "regex" => '^Syslog\smessage:\s\S+?\s(?<time>\S+?\s\s\d{1,2}\s\d{4}\s\d{2}:\d{2}:\d{2}).*?IF_STATE.*?Interface\s(?<interface>\S+?)\shas\sturned\sinto\sUP\sstate.*$'
          "event_name" => "网口接入"
          "event_type" => "1000065"
          "event_subtype" => "1000000214"
          "extract_fields" => ["interface"]
        }
      ]
    }
  }

  # 添加通用字段
  mutate {
    replace => {
      "device_type" => "1000079"
      "product_name" => "华为-S5300"
    }
  }

  # 保存提取字段
  mutate {
    rename => {
      "username" => "related_object_name"
      "Command" => "action_details"
      "srcIp" => "source_address"
      "user" => "related_object_name"
      "time" => "action_details"
      "interface" => "action_object_name"
      "sourceIp" => "source_address"
      "Username" => "related_object_name"
    }
  }
}
