filter {
  ruby {
    path => "/usr/share/logstash/config/conf.d/ruby/event_processor.rb"
    script_params => {
      "patterns" => [
        {
          "regex" => 'USERLOGIN:.*login.*$'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => []
        },
        {
          "regex" => 'USEROFFLINE\(s\).*offline.\s\(UserType=(?<lgType>\w+),\sUserName=(?<Username>\S+),.*IP=(?<SourceIP>\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3}).*Reason=User request to offline\)$'
          "event_name" => "登录失败"
          "event_type" => "1000061"
          "event_subtype" => "1000000140"
          "extract_fields" => ["lgType", "Username", "SourceIP"]
        },
        {
          "regex" => 'USERLOGOUT:.*logout.*UserName=(?<Username>\S+),\sUserIP=(?<SourceIP>\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3}),\sUserChannel=(?<lgType>\w+)\)$'
          "event_name" => "退出登录"
          "event_type" => "1000068"
          "event_subtype" => "1000000201"
          "extract_fields" => ["Username", "SourceIP", "lgType"]
        }
      ]
    }
  }

  # 添加通用字段
  mutate {
    replace => {
      "device_type" => "1000032"
      "product_name" => "华为-AR2240E"
    }
  }

  # 保存提取字段
  mutate {
    rename => {
      "Username" => "related_object_name"
      "SourceIP" => "source_address"
    }
  }
}
