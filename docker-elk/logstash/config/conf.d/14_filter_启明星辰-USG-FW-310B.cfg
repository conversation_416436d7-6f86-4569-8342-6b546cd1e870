filter {
  ruby {
    path => "/usr/share/logstash/config/conf.d/ruby/event_processor.rb"
    script_params => {
      "patterns" => [
        {
          "regex" => '^SerialNum=.+GenTime="(?<DateTime>\d{4}-\d{2}-\d{2}\s\d{2}:\d{2}:\d{2})"\sSrcIP=(?<DeviceIp>.+)\sUserName=(?<Username>.+)\sOperate="login"\sManageStyle=(?<loginType>\S+)\sContent="success".*$'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => ["DateTime", "DeviceIp", "Username", "loginType"]
        },
        {
          "regex" => '^<\d+>.*"(?<time>.*)"\sdname=.*from=(?<srcIp>\S+).*admin="{0,1}(?<user>\S+)"{0,1}\sact="{0,1}登录"{0,1}\sresult=0.*登录".*$'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => ["srcIp", "user"]
        },
        {
          "regex" => '^webui.*mod=webui\s+from=(?<DeviceIp>\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3})\s+agent=.*admin=("|)(?<Username>\S+?)("|)\s+act=("|)登录("|)\s+result=1.*（帐|账）号登录错误.*$'
          "event_name" => "登录失败"
          "event_type" => "1000061"
          "event_subtype" => "1000000140"
          "extract_fields" => ["DeviceIp", "Username"]
        },
        {
          "regex" => '^<\d+>.*date="(?<time>.*)".*from=(?<srcIp>\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3})\s+.*admin=("|)(?<Username>\S+?)("|)\s+act=("|)登录("|)\s+.*(帐|账)号登录错误.*$'
          "event_name" => "登录失败"
          "event_type" => "1000061"
          "event_subtype" => "1000000140"
          "extract_fields" => ["srcIp", "Username"]
        },
        {
          "regex" => 'CONFIG: SerialNum=.+GenTime="(?<DateTime>\d{4}-\d{2}-\d{2}\s\d{2}:\d{2}:\d{2})"\sSrcIP=(?<DeviceIp>.+)\sUserName=(?<Username>.+)\sOperate="login"\sManageStyle=(?<loginType>\S+)\sContent="failed for user name or password error".*?$'
          "event_name" => "登录失败"
          "event_type" => "1000061"
          "event_subtype" => "1000000140"
          "extract_fields" => ["DateTime", "DeviceIp", "Username", "loginType"]
        },
        {
          "regex" => 'CONFIG: SerialNum=.+GenTime="(?<DateTime>\d{4}-\d{2}-\d{2}\s\d{2}:\d{2}:\d{2})"\sSrcIP=(?<DeviceIp>.+)\sUserName=(?<Username>.+)\sManageStyle=(?<loginType>\S+)\sContent="Logout".*$'
          "event_name" => "退出登录"
          "event_type" => "1000068"
          "event_subtype" => "1000000201"
          "extract_fields" => ["DateTime", "DeviceIp", "Username", "loginType"]
        },
        {
          "regex" => '^<\d+>.*date="(?<time>.*)"\sdname.*logtype=(?<logtype>\d+).*from=(?<srcIp>\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3}).*admin=(?<user>.*)\sact=退出.*$'
          "event_name" => "退出登录"
          "event_type" => "1000068"
          "event_subtype" => "1000000201"
          "extract_fields" => ["logtype", "srcIp", "user"]
        },
        {
          "regex" => '^<\d+>.*date="(?<time>.*)"\sdname.*act=设置\suser=(?<user>\S+)\sdsp_msg="(?<Command>.*)"\s.*$'
          "event_name" => "配置变更"
          "event_type" => "1000026"
          "event_subtype" => "1000000094"
          "extract_fields" => ["user", "Command"]
        },
        {
          "regex" => '^<\d+>.*sa=(?<srcIp>\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})\s*sport=(?<srcPort>\w+)\s+.*?da=(?<dstIp>\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})\s*dport=(?<dstPort>\w+)\s+.*?policy=.*([Dd]eny|DENY|拒绝).*$'
          "event_name" => "不符合安全策略访问"
          "event_type" => "1000065"
          "event_subtype" => "1000000241"
          "extract_fields" => ["srcIp", "srcPort", "dstIp", "dstPort"]
        },
        {
          "regex" => 'SrcIP=(?<srcIp>\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3})\s*DstIP=(?<dstIp>\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3}).*?SrcPort=(?<srcPort>\d+)\s*DstPort=(?<dstPort>\d+)\s*.*?Action=([Dd]eny|DENY|拒绝).*$'
          "event_name" => "不符合安全策略访问"
          "event_type" => "1000065"
          "event_subtype" => "1000000241"
          "extract_fields" => ["srcIp", "dstIp", "srcPort", "dstPort"]
        },
        {
          "regex" => '^<\d+>kernel:.*date="(?<time>.*)"\s+.*pri=5.*mod=pf\s+sa=(?<srcIp>\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3})\s+sport=(?<srcPort>\S+)\s+type=\S+\s+da=(?<dstIp>\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3})\s+dport=(?<dstPort>\S+).*policy=POLICY.*DENY.*$'
          "event_name" => "不符合安全策略访问"
          "event_type" => "1000065"
          "event_subtype" => "1000000241"
          "extract_fields" => ["srcIp", "srcPort", "dstIp", "dstPort"]
        },
        {
          "regex" => 'FILTER:\s+SerialNum=.*?\s+GenTime="(?<time>.*?)"\s+SrcIP=(?<srcIp>\d{1,3}(?:\.\d{1,3}){3})\s+DstIP=(?<dstIp>\d{1,3}(?:\.\d{1,3}){3})\s+Protocol=(?<protocol>\S+?)\s+SrcPort=(?<srcPort>\d+?)\s+DstPort=(?<dstPort>\d+?)\s+.*?Action=DENY\s+Content=.*$'
          "event_name" => "不符合安全策略访问"
          "event_type" => "1000065"
          "event_subtype" => "1000000241"
          "extract_fields" => ["srcIp", "dstIp", "protocol", "srcPort", "dstPort"]
        },
        {
          "regex" => '^<\d+>.*date="(?<time>.*)"\sdname.*sa=(?<srcIp>.*)\ssport=(?<srcPort>\d+)\stype.*da=(?<dstIp>.*)\sdport=(?<dstPort>\d+).*policy=POLICY_PERMIT.*$'
          "event_name" => "符合安全策略访问"
          "event_type" => "1000065"
          "event_subtype" => "1000000241"
          "extract_fields" => ["srcIp", "srcPort", "dstIp", "dstPort"]
        },
        {
          "regex" => '^<\d+>.*\"(?<time>.*)\".*?logtype=8\s+.*mod=ifstatus.*down.*?dsp.*msg=\"(?<interface>\S+?)\s+接口链路断开.*$'
          "event_name" => "网口拔出"
          "event_type" => "1000065"
          "event_subtype" => "1000000215"
          "extract_fields" => ["interface"]
        },
        {
          "regex" => '^<\d+>kernel.*:\s+(?<interface>\S+)\s+NIC\s+Link\s+is\s+Down.*$'
          "event_name" => "网口拔出"
          "event_type" => "1000065"
          "event_subtype" => "1000000215"
          "extract_fields" => ["interface"]
        },
        {
          "regex" => 'IF_INFO: SerialNum=.*GenTime="(?<DateTime>\d{4}-\d{2}-\d{2}\s+\d{2}:\d{2}:\d{2})"\s+Content="interface\s+(?<Interface>.+)\s+linkdown".*$'
          "event_name" => "网口拔出"
          "event_type" => "1000065"
          "event_subtype" => "1000000215"
          "extract_fields" => ["DateTime", "Interface"]
        },
        {
          "regex" => '^<\d+>.*\"(?<time>.*)\".*?logtype=8\s+.*mod=ifstatus.*up.*?dsp.*msg=\"(?<interface>\S+?)\s+接口链路连接.*$'
          "event_name" => "网口接入"
          "event_type" => "1000065"
          "event_subtype" => "1000000214"
          "extract_fields" => ["interface"]
        },
        {
          "regex" => '^<\d+>kernel.*:\s+(?<interface>\S+)\s+NIC\s+Link\s+is\s+Up.*$'
          "event_name" => "网口接入"
          "event_type" => "1000065"
          "event_subtype" => "1000000214"
          "extract_fields" => ["interface"]
        },
        {
          "regex" => '^\s*.*IF_INFO: SerialNum=.*GenTime="(?<DateTime>\d{4}-\d{2}-\d{2}\s+\d{2}:\d{2}:\d{2})"\s+Content="interface\s+(?<Interface>.+)\s+linkup".*$'
          "event_name" => "网口接入"
          "event_type" => "1000065"
          "event_subtype" => "1000000214"
          "extract_fields" => ["DateTime", "Interface"]
        }
      ]
    }
  }

  # 添加通用字段
  mutate {
    replace => {
      "device_type" => "1000035"
      "product_name" => "启明星辰-USG-FW-310B"
    }
  }

  # 保存提取字段
  mutate {
    rename => {
      "dstIp" => "dest_address"
      "DateTime" => "action_details"
      "protocol" => "protocol"
      "Command" => "action_details"
      "dstPort" => "dest_port"
      "srcIp" => "source_address"
      "DeviceIp" => "device_name"
      "interface" => "action_object_name"
      "user" => "related_object_name"
      "srcPort" => "source_port"
      "Interface" => "action_object_name"
      "Username" => "related_object_name"
    }
  }
}
