filter {
  ruby {
    path => "/usr/share/logstash/config/conf.d/ruby/event_processor.rb"
    script_params => {
      "patterns" => [
        {
          "regex" => '^日志类型:系统操作,\s*用户:(?<UserName>\S+)\(\S+\),\s*IP地址:(?<SourceIP>\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3}),\s*操作对象:用户登录,\s*操作类型:登录,\s*描述:.+登录成功.*?$'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => ["UserName", "SourceIP"]
        },
        {
          "regex" => '日志类型:SSL VPN用户行为日志,\s用户:(?<UserName>\S+),\sIP地址:(?<SourceIP>\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3}),\s操作对象:SSL\sVPN,\s*操作:登录,\s时间:(?<time1>\d{4}-\d{1,2}-\d{1,2}\s\d{1,2}:\d{1,2}:\d{1,2}),\s描述:登录成功.*?$'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => ["UserName", "SourceIP", "time1"]
        },
        {
          "regex" => '^日志类型:系统操作,\s*用户:(?<UserName>\S+)\(\S+\),\s*IP地址:(?<SourceIP>\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3}),\s*操作对象:用户登录,\s*操作类型:登录,\s*描述:.+登录失败.*$'
          "event_name" => "登录失败"
          "event_type" => "1000061"
          "event_subtype" => "1000000140"
          "extract_fields" => ["UserName", "SourceIP"]
        },
        {
          "regex" => '日志类型:系统操作,\s*用户:(?<UserName>\S+)\(\S+\),\s*IP地址:(?<SourceIP>\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3}),\s*操作对象:用户注销,\s*操作类型:注销,\s*描述:.+注销成功.*$'
          "event_name" => "退出登录"
          "event_type" => "1000068"
          "event_subtype" => "1000000201"
          "extract_fields" => ["UserName", "SourceIP"]
        }
      ]
    }
  }

  # 添加通用字段
  mutate {
    replace => {
      "device_type" => "1000035"
      "product_name" => "深信服-AF6062"
    }
  }

  # 保存提取字段
  mutate {
    rename => {
      "time1" => "action_details"
      "UserName" => "related_object_name"
      "SourceIP" => "source_address"
    }
  }
}
