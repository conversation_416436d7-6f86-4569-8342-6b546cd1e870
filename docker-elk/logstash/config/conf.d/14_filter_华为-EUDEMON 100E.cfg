filter {
  ruby {
    path => "/usr/share/logstash/config/conf.d/ruby/event_processor.rb"
    script_params => {
      "patterns" => [
        {
          "regex" => '\s+(?<lgType>\w+)/\d+/LOGIN:\s+(?<Username>\S+).*$'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => ["lgType", "Username"]
        },
        {
          "regex" => '\s+(?<lgType>\w+)/\d+/PASS:User\s+(?<Username>\S+)\(IP:(?<srcIp>\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3})\s+ID\S+\)\s+login\s+succeeded$'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => ["lgType", "Username", "srcIp"]
        },
        {
          "regex" => '^\s*.*?\s+(?<lgType>\w+)/\d+/FAIL:User\s+(?<Username>\S+)\(IP:(?<srcIp>\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}).*?$'
          "event_name" => "登录失败"
          "event_type" => "1000061"
          "event_subtype" => "1000000140"
          "extract_fields" => ["lgType", "Username", "srcIp"]
        },
        {
          "regex" => '\s+(?<lgType>\w+)/\d+/LOGIN_FAIL:\s+(?<Username>\S+).*?$'
          "event_name" => "登录失败"
          "event_type" => "1000061"
          "event_subtype" => "1000000140"
          "extract_fields" => ["lgType", "Username"]
        },
        {
          "regex" => '^(?:<\d+>)?.*?\s+(?<lgType>\w+)/\d+/LOGOUT:\s+(?<Username>\S+).*?$'
          "event_name" => "退出登录"
          "event_type" => "1000068"
          "event_subtype" => "1000000201"
          "extract_fields" => ["lgType", "Username"]
        },
        {
          "regex" => '\s+(?<lgType>\w+)/\d+/OUT:User\s+(?<Username>\S+)\(IP:(?<srcIp>\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3})\s+ID\S+\)\s+logout$'
          "event_name" => "退出登录"
          "event_type" => "1000068"
          "event_subtype" => "1000000201"
          "extract_fields" => ["lgType", "Username", "srcIp"]
        },
        {
          "regex" => 'SHELL/\d/CMD:.*ip:(?<DeviceIp>\S+)\suser:(?<Username>\S+)\scommand:(?<Command>.*)$'
          "event_name" => "配置变更"
          "event_type" => "1000026"
          "event_subtype" => "1000000094"
          "extract_fields" => ["DeviceIp", "Username", "Command"]
        },
        {
          "regex" => 'FILTER\/\d+\/ACLDENY.*?source-ip=(?<srcIp>\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3}).*\s+source-port=(?<srcPort>\d+).*\s+destination-ip=(?<dstIp>\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3}).*\s+destination-port=(?<dstPort>\d+).*?$'
          "event_name" => "不符合安全策略访问"
          "event_type" => "1000065"
          "event_subtype" => "1000000241"
          "extract_fields" => ["srcIp", "srcPort", "dstIp", "dstPort"]
        },
        {
          "regex" => '^(?!<)\S+\s+IFNET/5/UPDOWN:.*interface\s+(?<interface>\S+)\s+turns\s+into\s+DOWN\s+state.*$'
          "event_name" => "网口拔出"
          "event_type" => "1000065"
          "event_subtype" => "1000000215"
          "extract_fields" => ["interface"]
        },
        {
          "regex" => 'PHY\S\s+(?<interface>\S+)\S\s+change\s+status\s+to\s+down.*$'
          "event_name" => "网口拔出"
          "event_type" => "1000065"
          "event_subtype" => "1000000215"
          "extract_fields" => ["interface"]
        },
        {
          "regex" => 'UPDOWN.*interface\s+(?<interface>\S+)\s+turns\s+into\s+UP\s+state.*$'
          "event_name" => "网口接入"
          "event_type" => "1000065"
          "event_subtype" => "1000000214"
          "extract_fields" => ["interface"]
        },
        {
          "regex" => 'PHY\S\s+(?<interface>\S+)\S\s+change\s+status\s+to\s+up.*$'
          "event_name" => "网口接入"
          "event_type" => "1000065"
          "event_subtype" => "1000000214"
          "extract_fields" => ["interface"]
        }
      ]
    }
  }

  # 添加通用字段
  mutate {
    replace => {
      "device_type" => "1000035"
      "product_name" => "华为-EUDEMON 100E"
    }
  }

  # 保存提取字段
  mutate {
    rename => {
      "dstIp" => "dest_address"
      "Command" => "action_details"
      "dstPort" => "dest_port"
      "srcIp" => "source_address"
      "DeviceIp" => "device_name"
      "interface" => "action_object_name"
      "srcPort" => "source_port"
      "Username" => "related_object_name"
    }
  }
}
