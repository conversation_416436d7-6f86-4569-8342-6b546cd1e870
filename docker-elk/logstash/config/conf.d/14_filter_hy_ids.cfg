filter {
  mutate {
    replace => { "device_type" => "1000037" }
  }

  # Use KV filter to extract all key=value pairs
  kv {
    source => "message"
    field_split => " "
    value_split => "="
    target => "kv"
  }

  # Set event_type and event_subtype based on the EventType field
  if [process][program] == "INTRUSION_ATTACK" {
    if [kv][EventType] == "命令执行" {
      mutate {
        replace => { "event_type" => "1000014" }
        replace => { "event_subtype" => "1000000095" }
      }
    } else if [kv][EventType] == "蠕虫病毒" {
      mutate {
        replace => { "event_type" => "1000013" }
        replace => { "event_subtype" => "1000000001" }
      }
    } else if [kv][EventType] == "木马后门" {
      mutate {
        replace => { "event_type" => "1000013" }
        replace => { "event_subtype" => "1000000003" }
      }
    } else if [kv][EventType] in ["目录遍历", "缓冲溢出", "跨站攻击", "SQL注入"] {
      mutate {
        replace => { "event_type" => "1000014" }
        replace => { "event_subtype" => "1000000013" }
      }
    } else if [kv][EventType] == "DoS攻击" {
      mutate {
        replace => { "event_type" => "1000014" }
        replace => { "event_subtype" => "1000000018" }
      }
    } else if [kv][EventType] == "请求访问" {
      mutate {
        replace => { "event_type" => "1000028" }
        replace => { "event_subtype" => "1000000115" }
      }
    } else if [kv][EventType] in ["安全绕过", "用户自定义安全类型"] {
      mutate {
        replace => { "event_type" => "1000014" }
        replace => { "event_subtype" => "1000000031" }
      }
    } else if [kv][EventType] == "信息泄露" {
      mutate {
        replace => { "event_type" => "1000015" }
        replace => { "event_subtype" => "1000000034" }
      }
    } else if [kv][EventType] == "漏洞扫描" {
      mutate {
        replace => { "event_type" => "1000014" }
        replace => { "event_subtype" => "1000000011" }
      }
    } else {
      mutate {
        replace => { "event_type" => "1000031" }
      }
    }
  } else if [process][program] == "IF_INFO" {
    mutate {
      replace => { "event_type" => "1000026" }
      replace => { "event_subtype" => "1000000095" }
      replace => { "event_name" => "接口信息" }
    }
  } else if [process][program] == "FILTER" {
    mutate {
      replace => { "event_type" => "1000027" }
      replace => { "event_subtype" => "1000000111" }
      replace => { "event_name" => "防火墙信息" }
    }
  } else if [process][program] == "CONFIG" {
    mutate {
      replace => { "event_type" => "1000026" }
      replace => { "event_subtype" => "1000000094" }
      replace => { "event_name" => "配置信息" }
    }
  } else {
    mutate {
      replace => { "event_type" => "1000031" }
    }
  }
  # Map extracted fields to standardized names
  mutate {
    rename => {
      "[kv][SrcIP]" => "source_address"
      "[kv][DstIP]" => "dest_address"
      "[kv][Protocol]" => "protocol"
      "[kv][SrcPort]" => "source_port"
      "[kv][DstPort]" => "dest_port"
      "[kv][InInterface]" => "source_zone"
      "[kv][OutInterface]" => "dest_zone"
      "[kv][Action]" => "action"
      "[kv][UserID]" => "related_object_account"
      "[kv][UserName]" => "related_object_name"
      "[kv][SrcMac]" => "source_mac"
      "[kv][DstMac]" => "dest_mac"
      "[kv][EventType]" => "event_name"
      "[kv][EventName]" => "action_details"
      "[kv][AppName]" => "action_object_name"
      "[kv][IpsLogLevel]" => "original_level"
      "[kv][FileName]" => "related_filename"
      "[kv][SerialNum]" => "device_name"
      "[kv][Operate]" => "action_details"
    }
  }
}