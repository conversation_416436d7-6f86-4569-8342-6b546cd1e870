filter {
  ruby {
    path => "/usr/share/logstash/config/conf.d/ruby/event_processor.rb"
    script_params => {
      "patterns" => [
        {
          "regex" => '^<\d+>\d{2}\/\d{2}\/\d{2}\s\d{2}:\d{2}:\d{2}\s\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3}\s\S+:(?<portnumber>Port\s\d+)\sis\sdown'
          "event_name" => "网口拔出"
          "event_type" => "1000065"
          "event_subtype" => "1000000215"
          "extract_fields" => ["portnumber"]
        },
        {
          "regex" => '^<\d+>\d{2}\/\d{2}\/\d{2}\s\d{2}:\d{2}:\d{2}\s\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3}\s\S+:(?<portnumber>Port\s\d+)\s\S+\sfrom\sDisabled'
          "event_name" => "网口接入"
          "event_type" => "1000065"
          "event_subtype" => "1000000214"
          "extract_fields" => ["portnumber"]
        }
      ]
    }
  }

  # 添加通用字段
  mutate {
    replace => {
      "device_type" => "1000079"
      "product_name" => "罗杰康-其他"
    }
  }

  # 保存提取字段
  mutate {
    rename => {
      "portnumber" => "dest_port"
    }
  }
}
