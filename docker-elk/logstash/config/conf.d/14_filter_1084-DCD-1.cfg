filter {
    grok {
        match => {
            "message" => "%{IP:product_name}#%{TIMESTAMP_ISO8601:event_timestamp}#%{GREEDYDATA:action_details}"
        }
    }
    date {
        match => [ "event_timestamp", "yyyy-MM-dd HH:mm:ss" ]
        target => "@timestamp"
    }
    if [hostname] {
        mutate {
            replace => {
                device_name => "%{hostname}"
            }
        }
    }
    mutate {
        replace => {
            "device_type" => "1000081"
        }
    }
    # SW类型
    grok {
        match => {
            "message" => [
                "^SW %{INT:temp_event_type} %{INT:temp_event_subtype} %{GREEDYDATA:event_details}$"
            ]
        }
    }
    if [event_details] {
        # 根据不同的event_type设置具体的事件类型描述
        if [temp_event_type] == "0" {
            mutate {
                replace => { "event_type" => "1000061" }
            }
        } else if [temp_event_type] == "1" {
            mutate {
                replace => { "event_type" => "1000062" }
            }
        } else if [temp_event_type] == "3" {
            mutate {
                replace => { "event_type" => "1000063" }
            }
        } else if [temp_event_type] == "4" {
            mutate {
                replace => { "event_type" => "1000064" }
            }
        }

        # 根据不同的event_subtype设置具体的事件子类型描述
        if [event_type] == "1000061" {
            if [temp_event_subtype] == "2" {
                mutate {
                    replace => { "event_subtype" => "**********" }
                }
                # 行为监视 - 用户登录成功
                grok {
                    match => {
                        "event_details" => "^%{IP:related_object_address} %{TIMESTAMP_ISO8601:login_time} %{USER:related_object_account} %{WORD:related_object_category}$"
                    }
                }
            } else if [temp_event_subtype] == "3" {
                mutate {
                    replace => { "event_subtype" => "**********" }
                }
                # 行为监视 - 用户操作信息
                grok {
                    match => {
                        "event_details" => "^%{IP:related_object_address} %{TIMESTAMP_ISO8601:login_time} %{USER:related_object_account} %{GREEDYDATA:action_details}$"
                    }
                }
            } else if [temp_event_subtype] == "4" {
                mutate {
                    replace => { "event_subtype" => "**********" }
                }
                # 行为监视 - 用户登录失败
                grok {
                    match => {
                        "event_details" => "^%{IP:related_object_address} %{TIMESTAMP_ISO8601:login_time} %{USER:related_object_account} %{WORD:related_object_category}$"
                    }
                }
            } else if [temp_event_subtype] == "5" {
                mutate {
                    replace => { "event_subtype" => "**********" }
                }
                # 行为监视 - 用户退出登录
                grok {
                    match => {
                        "event_details" => "^%{IP:related_object_address} %{TIMESTAMP_ISO8601:login_time} %{USER:related_object_account} %{WORD:related_object_category}$"
                    }
                }
            }
        } else if [event_type] == "1000062" {
            if [temp_event_subtype] == "2" {
                mutate {
                    replace => { "event_subtype" => "**********" }
                }
                # 运行日志 - 运行时长
                grok {
                    match => {
                        "event_details" => "^%{NUMBER:action_duration}$"
                    }
                }
            } else if [temp_event_subtype] == "3" {
                mutate {
                    replace => { "event_subtype" => "**********" }
                }
                # 运行日志 - CPU利用率
                grok {
                    match => {
                        "event_details" => "^%{NUMBER:action_details}$"
                    }
                }
            } else if [temp_event_subtype] == "4" {
                mutate {
                    replace => { "event_subtype" => "1000000144" }
                }
                # 运行日志 - 内存利用率
                grok {
                    match => {
                        "event_details" => "^%{NUMBER:action_details}$"
                    }
                }
            } else if [temp_event_subtype] == "5" {
                mutate {
                    replace => { "event_subtype" => "1000000145" }
                }
                # 运行日志 - 网口流量均值
                grok {
                    match => {
                        "event_details" => "^(#%{INT:slot1} %{INT:port1} %{NUMBER:avg_traffic1}(?: #%{INT:slot2} %{INT:port2} %{NUMBER:avg_traffic2})?)$"
                    }
                }
            } else if [temp_event_subtype] == "18" {
                mutate {
                    replace => { "event_subtype" => "1000000146" }
                }
                # 运行日志 - 网口状态
                grok {
                    match => {
                        "event_details" => "^(#%{INT:port1} %{WORD:status1}(?: #%{INT:port2} %{WORD:status2})?)$"
                    }
                }
            } else if [temp_event_subtype] == "14" {
                mutate {
                    replace => { "event_subtype" => "1000000147" }
                }
                # 运行日志 - 接入设备 MAC 地址的合法性识别
                grok {
                    match => {
                        "event_details" => "^(#%{INT:slot1} %{INT:port1} %{MAC:mac1} %{WORD:validity1}(?: #%{INT:slot2} %{INT:port2} %{MAC:mac2} %{WORD:validity2})?)$"
                    }
                }
            }
        } else if [event_type] == "1000063" {
            if [temp_event_subtype] == "1" {
                mutate {
                    replace => { "event_subtype" => "1000000148" }
                }
                # 告警事件 - IP连接信息
                grok {
                    match => {
                        "event_details" => "^(#%{INT:action_object_name} %{INT:source_port} %{IP:source_address}(?: #%{INT:slot2} %{INT:port2} %{IP:ip2})?)$"
                    }
                }
            } else if [temp_event_subtype] == "1" {
                mutate {
                    replace => { "event_subtype" => "1000000149" }
                }
                # 告警事件 - 接入设备IP地址冲突
                grok {
                    match => {
                        "event_details" => "^%{GREEDYDATA:conflict_time} %{MAC:mac1} %{DATA:port1} %{INT:vlan1} %{MAC:mac2} %{DATA:port2} %{INT:vlan2} %{IP:conflict_ip}$"
                    }
                }
            } else if [temp_event_subtype] == "6" {
                mutate {
                    replace => { "event_subtype" => "1000000150" }
                }
                # 告警事件 - 端口UP
                grok {
                    match => {
                        "event_details" => "^%{INT:source_port} %{INT:action_result} %{INT:action_details}$"
                    }
                }
            } else if [temp_event_subtype] == "7" {
                mutate {
                    replace => { "event_subtype" => "1000000151" }
                }
                # 告警事件 - 端口DOWN
                grok {
                    match => {
                        "event_details" => "^%{INT:source_port} %{INT:action_result} %{INT:action_details}$"
                    }
                }
            }
        } else if [event_type] == "1000064" {
            if [temp_event_subtype] == "1" {
                mutate {
                    replace => { "event_subtype" => "1000000152" }
                }
                # 设备故障事件 - 风扇故障
                grok {
                    match => {
                        "event_details" => "^%{INT:action_object_name} %{WORD:action_result} %{DATA:action_details}$"
                    }
                }
            } else if [temp_event_subtype] == "2" {
                mutate {
                    replace => { "event_subtype" => "1000000153" }
                }
                # 设备故障事件 - 电源故障
                grok {
                    match => {
                        "event_details" => "^%{DATA:action_details}$"
                    }
                }
            } else if [temp_event_subtype] == "3" {
                mutate {
                    replace => { "event_subtype" => "1000000154" }
                }
                # 设备故障事件 - 温度异常
                grok {
                    match => {
                        "event_details" => "^%{INT:action_object_name} %{INT:action_details}$"
                                  }
                }
            } else if [temp_event_subtype] == "8" {
                mutate {
                    replace => { "event_subtype" => "1000000155" }
                }
                # 设备故障事件 - 端口故障
                grok {
                    match => {
                        "event_details" => "^(#%{INT:action_object_name} %{INT:port1}(?: #%{INT:slot2} %{INT:port2})?) port fault$"
                    }
                }
            } else if [temp_event_subtype] == "9" {
                mutate {
                    replace => { "event_subtype" => "1000000156" }
                }
                # 设备故障事件 - 电路板故障
                grok {
                    match => {
                        "event_details" => "^circuit board fault$"
                    }
                }
            }
        }
    }
}     