filter {
    grok {
        match => {
            "message" => [
                "SVR %{INT:temp_event_type} %{INT:temp_event_subtype} %{GREEDYDATA:event_details}$"
            ]
        }
    }
    if [hostname] {
        mutate {
            replace => {
                device_name => "%{hostname}"
            }
        }
    }
    mutate {
        replace => {
            "device_type" => "1000078"
        }
    }

    if [temp_event_subtype] == "15" {
        # 登录成功
        grok {
            match => { "event_details" => "%{IP:action_object_address} %{IP:related_object_address} %{TIMESTAMP_ISO8601:login_time} %{USERNAME:related_object_name}" }
        }
        mutate {
            replace => { "event_type" => "1000068" }
            replace => { "event_subtype" => "1000000198" }
        }
    } else if [temp_event_subtype] == "12" {
        # 操作输入信息
        grok {
            match => { "event_details" => "%{IP:action_object_address} %{IP:related_object_address} %{TIMESTAMP_ISO8601:login_time} %{TIMESTAMP_ISO8601:operation_time} %{USERNAME:related_object_name} %{NUMBER:terminal_number} %{NUMBER:operation_dir_length} %{DATA:operation_dir} %{NUMBER:operation_cmd_length} %{DATA:operation_cmd}" }
        }
        mutate {
            replace => { "event_type" => "1000068" }
            replace => { "event_subtype" => "1000000199" }
        }
    } else if [temp_event_subtype] == "13" {
        # 操作回显信息
        grok {
            match => { "event_details" => "%{IP:action_object_address} %{IP:related_object_address} %{TIMESTAMP_ISO8601:login_time} %{USERNAME:related_object_name} %{NUMBER:terminal_number} %{NUMBER:operation_echo_length} %{DATA:operation_echo}" }
        }
        mutate {
            replace => { "event_type" => "1000068" }
            replace => { "event_subtype" => "1000000200" }
        }
    } else if [temp_event_subtype] == "14" {
        # 本机登录失败
        grok {
            match => { "event_details" => "%{IP:action_object_address} %{IP:related_object_address} %{TIMESTAMP_ISO8601:attempt_time} %{USERNAME:related_object_name}" }
        }
        mutate {
            replace => { "event_type" => "1000068" }
            replace => { "event_subtype" => "1000000202" }
        }
    } else if [temp_event_subtype] == "16" {
        # 退出登录
        grok {
            match => { "event_details" => "%{IP:action_object_address} %{IP:related_object_address} %{TIMESTAMP_ISO8601:login_time} %{USERNAME:related_object_name} %{TIMESTAMP_ISO8601:logout_time}" }
        }
        mutate {
            replace => { "event_type" => "1000068" }
            replace => { "event_subtype" => "1000000201" }
        }
    } else if [temp_event_subtype] == "17" {
        # USB 接入
        grok {
            match => { "event_details" => "%{NUMBER:usb_insert_state} {%{DATA:usb_interface}}{%{DATA:device_name}}{%{DATA:vendor_name}}{%{NUMBER:device_id}}{%{NUMBER:vendor_id}}{<%{DATA:interface_id}@%{DATA:protocol_id}>}{%{NUMBER:usb_type}}" }
        }
        mutate {
            replace => { "event_type" => "1000065" }
            replace => { "event_subtype" => "1000000203" }
        }
    } else if [temp_event_subtype] == "18" {
        # USB 拔出
        grok {
            match => { "event_details" => "%{NUMBER:usb_remove_state} {%{DATA:usb_interface}}{%{DATA:device_name}}{%{DATA:vendor_name}}{%{NUMBER:device_id}}{%{NUMBER:vendor_id}}{<%{DATA:interface_id}@%{DATA:protocol_id}>}{%{NUMBER:usb_type}}" }
        }
        mutate {
            replace => { "event_type" => "1000065" }
            replace => { "event_subtype" => "1000000204" }
        }
    } else if [temp_event_subtype] == "19" {
        # 串口占用
        grok {
            match => { "event_details" => "%{DATA:serial_port_name}" }
        }
        mutate {
            replace => { "event_type" => "1000065" }
            replace => { "event_subtype" => "1000000205" }
        }
    } else if [temp_event_subtype] == "20" {
        # 串口释放
        grok {
            match => { "event_details" => "%{DATA:serial_port_name}" }
        }
        mutate {
            replace => { "event_type" => "1000065" }
            replace => { "event_subtype" => "1000000206" }
        }
    } else if [temp_event_subtype] == "21" {
        # 并口被占用
        grok {
            match => { "event_details" => "%{DATA:parallel_port_name}" }
        }
        mutate {
            replace => { "event_type" => "1000065" }
            replace => { "event_subtype" => "1000000207" }
        }
    } else if [temp_event_subtype] == "22" {
        # 并口释放
        grok {
            match => { "event_details" => "%{DATA:parallel_port_name}" }
        }
        mutate {
            replace => { "event_type" => "1000065" }
            replace => { "event_subtype" => "1000000208" }
        }
    } else if [temp_event_subtype] == "23" {
        # 光驱加载
        grok {
            match => { "event_details" => "%{DATA:cdrom_name} %{DATA:cdrom_disc}" }
        }
        mutate {
            replace => { "event_type" => "1000065" }
            replace => { "event_subtype" => "1000000209" }
        }
    } else if [temp_event_subtype] == "24" {
        # 光驱托盘弹出
        grok {
            match => { "event_details" => "%{DATA:cdrom_name}" }
        }
        mutate {
            replace => { "event_type" => "1000065" }
            replace => { "event_subtype" => "1000000210" }
        }
    } else if [temp_event_subtype] == "25" {
        # 非法外联
        grok {
            match => { "event_details" => "%{DATA:protocol} %{IP:source_address} %{NUMBER:source_port} %{IP:dest_address} %{NUMBER:dest_port}" }
        }
        mutate {
            replace => { "event_type" => "1000065" }
            replace => { "event_subtype" => "1000000211" }
        }
    } else if [temp_event_subtype] == "26" {
        # 存在光驱设备
        grok {
            match => { "event_details" => "%{DATA:cdrom_name}" }
        }
        mutate {
            replace => { "event_type" => "1000065" }
            replace => { "event_subtype" => "1000000212" }
        }
    } else if [temp_event_subtype] == "27" {
        # 开放非法端口
        grok {
            match => { "event_details" => "%{DATA:protocol} %{NUMBER:port}" }
        }
        mutate {
            replace => { "event_type" => "1000065" }
            replace => { "event_subtype" => "1000000213" }
        }
    } else if [temp_event_subtype] == "28" {
        # 网口 up
        grok {
            match => { "event_details" => "%{DATA:network_interface}" }
        }
        mutate {
            replace => { "event_type" => "1000065" }
            replace => { "event_subtype" => "1000000214" }
        }
    } else if [temp_event_subtype] == "29" {
        # 网口 down
        grok {
            match => { "event_details" => "%{DATA:network_interface}" }
        }
        mutate {
            replace => { "event_type" => "1000065" }
            replace => { "event_subtype" => "1000000215" }
        }
    } else if [temp_event_subtype] == "34" {
        # 关键文件/目录变更
        grok {
            match => { "event_details" => "%{IP:action_object_address} %{DATA:related_object_name} %{DATA:file_path} %{NUMBER:change_type} %{NUMBER:old_permission} %{NUMBER:new_permission}" }
        }
        mutate {
            replace => { "event_type" => "1000065" }
            replace => { "event_subtype" => "1000000216" }
        }
        translate {
            field => "change_type"
            destination => "action_details"
            dictionary => {
                "0" => "文件增加"
                "1" => "文件修改"
                "2" => "文件删除"
                "3" => "权限变更"
            }
        }
    } else if [temp_event_subtype] == "35" {
        # 用户权限变更
        grok {
            match => { "event_details" => "%{IP:action_object_address} %{DATA:operator} %{DATA:related_object_name} %{DATA:change_type}" }
        }
        mutate {
            replace => { "event_type" => "1000065" }
            replace => { "event_subtype" => "1000000217" }
        }
    }
}