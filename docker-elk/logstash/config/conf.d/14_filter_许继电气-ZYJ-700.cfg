filter {
  ruby {
    path => "/usr/share/logstash/config/conf.d/ruby/event_processor.rb"
    script_params => {
      "patterns" => [
        {
          "regex" => '\]\s+(?<LoginType>\S+)\s+user\s+(?<user>\S+)\s+login\s+success.*$'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => ["LoginType", "user"]
        },
        {
          "regex" => '\]\s+(?<LoginType>\S+)\s+user\s+(?<user>\S+)\s+from\s+(?<srcIp>\S+)\s+login\s+success.*$'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => ["LoginType", "user", "srcIp"]
        },
        {
          "regex" => '\]\s+(?<LoginType>\S+)\s+user\s+(?<user>\S+)\s+login\s+failed.*$'
          "event_name" => "登录失败"
          "event_type" => "1000061"
          "event_subtype" => "1000000140"
          "extract_fields" => ["LoginType", "user"]
        },
        {
          "regex" => '\]\s+(?<LoginType>\S+)\s+user\s+(?<user>\S+)\s+from\s+(?<srcIp>\S+)\s+login\s+failure.*$'
          "event_name" => "登录失败"
          "event_type" => "1000061"
          "event_subtype" => "1000000140"
          "extract_fields" => ["LoginType", "user", "srcIp"]
        },
        {
          "regex" => '\]\s+(?<LoginType>\S+)\s+user\s+from\s+(?<srcIp>\S+)\s+login\s+out.*$'
          "event_name" => "退出登录"
          "event_type" => "1000068"
          "event_subtype" => "1000000201"
          "extract_fields" => ["LoginType", "srcIp"]
        },
        {
          "regex" => '\]\s+(?<user>\S+).*log\s+out.*$'
          "event_name" => "退出登录"
          "event_type" => "1000068"
          "event_subtype" => "1000000201"
          "extract_fields" => ["user"]
        },
        {
          "regex" => '\]\s+Link\s+down\s+on\s+(?<interface>.*)$'
          "event_name" => "网口拔出"
          "event_type" => "1000065"
          "event_subtype" => "1000000215"
          "extract_fields" => ["interface"]
        },
        {
          "regex" => 'Interface\s+(?<interface>.*),\s+changed\s+state\s+to\s+up\.$'
          "event_name" => "网口接入"
          "event_type" => "1000065"
          "event_subtype" => "1000000214"
          "extract_fields" => ["interface"]
        }
      ]
    }
  }

  # 添加通用字段
  mutate {
    replace => {
      "device_type" => "1000079"
      "product_name" => "许继电气-ZYJ-700"
    }
  }

  # 保存提取字段
  mutate {
    rename => {
      "srcIp" => "source_address"
      "interface" => "action_object_name"
      "user" => "related_object_name"
    }
  }
}
