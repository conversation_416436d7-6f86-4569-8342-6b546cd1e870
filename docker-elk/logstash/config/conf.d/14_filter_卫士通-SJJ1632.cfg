filter {
  ruby {
    path => "/usr/share/logstash/config/conf.d/ruby/event_processor.rb"
    script_params => {
      "patterns" => [
        {
          "regex" => 'VEAD\s0\s1\s(?<Username>\S+).*?$'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => ["Username"]
        },
        {
          "regex" => '^0\s+1\s+(?<user>\S+).*?$'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => ["user"]
        },
        {
          "regex" => '(?<ip>\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}).+\suser\slogin\ssuccessed.*$'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => ["ip"]
        },
        {
          "regex" => 'VEAD\s0\s2\s(?<Username>\S+).*$'
          "event_name" => "登录失败"
          "event_type" => "1000061"
          "event_subtype" => "1000000140"
          "extract_fields" => ["Username"]
        },
        {
          "regex" => '^(<\d+>|)(?<ip>\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})\]\s+user\s+login\s+failed\.Password\s+is\s+incorrect.*$'
          "event_name" => "登录失败"
          "event_type" => "1000061"
          "event_subtype" => "1000000140"
          "extract_fields" => ["ip"]
        },
        {
          "regex" => 'VEAD\s0\s4\s(?<Username>\S+).*$'
          "event_name" => "退出登录"
          "event_type" => "1000068"
          "event_subtype" => "1000000201"
          "extract_fields" => ["Username"]
        },
        {
          "regex" => '^\d+\s+2\s+user\s+logout.*'
          "event_name" => "退出登录"
          "event_type" => "1000068"
          "event_subtype" => "1000000201"
          "extract_fields" => []
        },
        {
          "regex" => '^0\s+4\s+(?<user>\S+).*'
          "event_name" => "退出登录"
          "event_type" => "1000068"
          "event_subtype" => "1000000201"
          "extract_fields" => ["user"]
        },
        {
          "regex" => '(?<ip>\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}).+\slogout\ssuccessed.*$'
          "event_name" => "退出登录"
          "event_type" => "1000068"
          "event_subtype" => "1000000201"
          "extract_fields" => ["ip"]
        },
        {
          "regex" => '^VEAD\s0\s3\s1.*$'
          "event_name" => "配置变更"
          "event_type" => "1000026"
          "event_subtype" => "1000000094"
          "extract_fields" => []
        },
        {
          "regex" => '^(?<DateTime>\d{4}-\d{2}-\d{2}\s\d{2}:\d{2}:\d{2})?\s*VEAD\s0\s3\s2.*$'
          "event_name" => "配置变更"
          "event_type" => "1000026"
          "event_subtype" => "1000000094"
          "extract_fields" => ["DateTime"]
        },
        {
          "regex" => '^\s*VEAD\s0\s3\s3.*$'
          "event_name" => "配置变更"
          "event_type" => "1000026"
          "event_subtype" => "1000000094"
          "extract_fields" => []
        },
        {
          "regex" => '^\s*VEAD\s0\s3\s4.*$'
          "event_name" => "配置变更"
          "event_type" => "1000026"
          "event_subtype" => "1000000094"
          "extract_fields" => []
        },
        {
          "regex" => 'VEAD\s0\s3\s5.*$'
          "event_name" => "配置变更"
          "event_type" => "1000026"
          "event_subtype" => "1000000094"
          "extract_fields" => []
        },
        {
          "regex" => '^\s*VEAD\s0\s3\s6.*$'
          "event_name" => "配置变更"
          "event_type" => "1000026"
          "event_subtype" => "1000000094"
          "extract_fields" => []
        },
        {
          "regex" => 'VEAD\s0\s3\s7.*$'
          "event_name" => "配置变更"
          "event_type" => "1000026"
          "event_subtype" => "1000000094"
          "extract_fields" => []
        },
        {
          "regex" => '^VEAD\s2\s7\s8\s(?<src_ip>.+)\s+(?<src_port>.+)\s+(?<dest_ip>.+)\s+(?<dest_port>.+)$'
          "event_name" => "不符合安全策略访问"
          "event_type" => "1000065"
          "event_subtype" => "1000000241"
          "extract_fields" => ["src_ip", "src_port", "dest_ip", "dest_port"]
        },
        {
          "regex" => 'VEAD\s1\s6\s(?<Eth>.+)\sdown$'
          "event_name" => "网口拔出"
          "event_type" => "1000065"
          "event_subtype" => "1000000215"
          "extract_fields" => ["Eth"]
        },
        {
          "regex" => '^VEAD\s1\s7\s(?<Eth>.+)\sup$'
          "event_name" => "网口接入"
          "event_type" => "1000065"
          "event_subtype" => "1000000214"
          "extract_fields" => ["Eth"]
        },
        {
          "regex" => 'VEAD\s1\s8\s(?<msg>.*[Ll]ost)$'
          "event_name" => "连接中断"
          "event_type" => "1000027"
          "event_subtype" => "1000000107"
          "extract_fields" => ["msg"]
        },
        {
          "regex" => 'VEAD\s1\s8\s(?<msg>.*[Rr]ecovery)$'
          "event_name" => "连接中断"
          "event_type" => "1000027"
          "event_subtype" => "1000000107"
          "extract_fields" => ["msg"]
        },
        {
          "regex" => 'VEAD\s2\s1\s1\s(?<localTunnelAddress>\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})\s(?<distantTunnelAddress>\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})\s(?<Msg>.*?)$'
          "event_name" => "公钥加密错误"
          "event_type" => "1000065"
          "event_subtype" => "1000000263"
          "extract_fields" => ["localTunnelAddress", "distantTunnelAddress", "Msg"]
        },
        {
          "regex" => 'VEAD\s2\s1\s5\s(?<localTunnelAddress>\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})\s(?<distantTunnelAddress>\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})\s(?<Msg>.*?)$'
          "event_name" => "私钥解密错误"
          "event_type" => "1000065"
          "event_subtype" => "1000000263"
          "extract_fields" => ["localTunnelAddress", "distantTunnelAddress", "Msg"]
        },
        {
          "regex" => 'VEAD\s2\s1\s6\s(?<localTunnelAddress>\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})\s(?<distantTunnelAddress>\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})\s(?<Msg>.*?)$'
          "event_name" => "私钥签名错误"
          "event_type" => "1000065"
          "event_subtype" => "1000000263"
          "extract_fields" => ["localTunnelAddress", "distantTunnelAddress", "Msg"]
        },
        {
          "regex" => 'VEAD\s2\s1\s4\s(?<localTunnelAddress>\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3})\s(?<distantTunnelAddress>\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3})\s(?<Msg>.*?)$'
          "event_name" => "隧道没有配置"
          "event_type" => "1000065"
          "event_subtype" => "1000000247"
          "extract_fields" => ["localTunnelAddress", "distantTunnelAddress", "Msg"]
        },
        {
          "regex" => '^VEAD\s2\s1\s7\s(?<localTunnelAddress>\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3})\s(?<distantTunnelAddress>\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3})\s(?<Msg>.*?)$'
          "event_name" => "隧道由密通变明通"
          "event_type" => "1000065"
          "event_subtype" => "1000000247"
          "extract_fields" => ["localTunnelAddress", "distantTunnelAddress", "Msg"]
        },
        {
          "regex" => 'VEAD\s2\s1\s8\s(?<localTunnelAddress>\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})\s(?<distantTunnelAddress>\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})\s(?<Msg>.*?)$'
          "event_name" => "隧道由明通变密通"
          "event_type" => "1000065"
          "event_subtype" => "1000000247"
          "extract_fields" => ["localTunnelAddress", "distantTunnelAddress", "Msg"]
        },
        {
          "regex" => 'VEAD\s2\s1\s2\s(?<localTunnelAddress>\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})\s(?<distantTunnelAddress>\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})\s(?<Msg>.*?)$'
          "event_name" => "验证签名错误"
          "event_type" => "1000065"
          "event_subtype" => "1000000263"
          "extract_fields" => ["localTunnelAddress", "distantTunnelAddress", "Msg"]
        },
        {
          "regex" => 'VEAD\s2\s1\s3\s(?<localTunnelAddress>\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})\s(?<distantTunnelAddress>\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})\s(?<Msg>.*?)$'
          "event_name" => "证书不存在"
          "event_type" => "1000065"
          "event_subtype" => "1000000263"
          "extract_fields" => ["localTunnelAddress", "distantTunnelAddress", "Msg"]
        },
        {
          "regex" => 'VEAD\s1\s2\s(?<CPURate>.+%) cpu.+$'
          "event_name" => "性能监控"
          "event_type" => "1000025"
          "event_subtype" => "1000000089"
          "extract_fields" => ["CPURate"]
        },
        {
          "regex" => '^\d+\s\d+\s(?<CPURate>.+%) cpu.+$'
          "event_name" => "性能监控"
          "event_type" => "1000025"
          "event_subtype" => "1000000089"
          "extract_fields" => ["CPURate"]
        },
        {
          "regex" => 'VEAD\s1\s3\s(?<MemRate>.+%)$'
          "event_name" => "性能监控"
          "event_type" => "1000025"
          "event_subtype" => "1000000089"
          "extract_fields" => ["MemRate"]
        },
        {
          "regex" => '^1\s3\s(?<MemRate>.+%)\smemory.*$'
          "event_name" => "性能监控"
          "event_type" => "1000025"
          "event_subtype" => "1000000089"
          "extract_fields" => ["MemRate"]
        }
      ]
    }
  }

  # 添加通用字段
  mutate {
    replace => {
      "device_type" => "1000080"
      "product_name" => "卫士通-SJJ1632"
    }
  }

  # 保存提取字段
  mutate {
    rename => {
      "src_port" => "source_port"
      "DateTime" => "action_details"
      "dest_ip" => "dest_address"
      "dest_port" => "dest_port"
      "user" => "related_object_name"
      "src_ip" => "source_address"
      "Username" => "related_object_name"
    }
  }
}
