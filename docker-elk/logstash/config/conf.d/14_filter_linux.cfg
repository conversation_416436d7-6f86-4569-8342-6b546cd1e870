filter {
    mutate {
        replace => { "device_type" => "1000032" }
    }
    if [syslog][facility] {
        mutate {
            add_field => { "event_type" => "1000023" }  # Linux主机
            add_field => { "related_object_name" => "%{[process][program]}" }
        }
        
        if [syslog][facility] in [0, 1, 3, 4, 5] {
            mutate {
                add_field => { "event_subtype" => "1000000081" }  # 系统日志
            }
        } else if [syslog][facility] in [10, 13] {
            mutate {
                add_field => { "event_subtype" => "1000000082" }  # 安全日志
            }
        } else if [syslog][facility] in [2, 9, 11, 7] {
            mutate {
                add_field => { "event_subtype" => "1000000083" }  # 应用程序日志
            }
        } else {
            mutate {
                add_field => { "event_subtype" => "1000000084" }  # 其他
            }
        }
    }
}