filter {
    grok {
        match => {
            "message" => [
                "FW %{INT:temp_event_type} %{INT:temp_event_subtype} %{GREEDYDATA:event_details}"
            ]
        }
    }
    if [hostname] {
        mutate {
            replace => {
                device_name => "%{hostname}"
            }
        }
    }
    mutate {
        replace => {
            "device_type" => "1000035"
        }
    }

    # 根据不同的event_type和event_subtype设置具体的事件类型和子类型描述
    if [temp_event_type] == "0" {
        mutate {
            replace => { "event_type" => "1000068" }
        }
        if [temp_event_subtype] == "1" {
            mutate {
                replace => { "event_subtype" => "1000000198" }
            }
            # 用户登录成功
            grok {
                match => {
                    "event_details" => "%{DATA:related_object_name} %{IP:related_object_address}"
                }
            }
        } else if [temp_event_subtype] == "2" {
            mutate {
                replace => { "event_subtype" => "1000000201" }
            }
            # 用户退出
            grok {
                match => {
                    "event_details" => "%{DATA:related_object_name} %{IP:related_object_address}"
                }
            }
        } else if [temp_event_subtype] == "3" {
            mutate {
                replace => { "event_subtype" => "1000000202" }
            }
            # 用户登录失败
            grok {
                match => {
                    "event_details" => "%{DATA:related_object_name} %{IP:related_object_address}"
                }
            }
        } else if [temp_event_subtype] == "4" {
            mutate {
                replace => { "event_subtype" => "1000000234" }
            }
            # 修改策略
            grok {
                match => {
                    "event_details" => "%{DATA:related_object_name} %{IP:related_object_address} %{GREEDYDATA:policy_change}"
                }
            }
        }
    } else if [temp_event_type] == "1" {
        mutate {
            replace => { "event_type" => "1000071" }
        }
        if [temp_event_subtype] == "1" {
            mutate {
                replace => { "event_subtype" => "1000000235" }
            }
            # CPU 利用率
            grok {
                match => {
                    "event_details" => "%{NUMBER:cpu_usage}%%"
                }
            }
        } else if [temp_event_subtype] == "2" {
            mutate {
                replace => { "event_subtype" => "1000000236" }
            }
            # 内存使用率
            grok {
                match => {
                    "event_details" => "%{NUMBER:memory_usage}%%"
                }
            }
        } else if [temp_event_subtype] == "3" {
            mutate {
                replace => { "event_subtype" => "1000000237" }
            }
            # 防火墙电源故障
            grok {
                match => {
                    "event_details" => "%{GREEDYDATA:power_supply_error}"
                }
            }
        } else if [temp_event_subtype] == "4" {
            mutate {
                replace => { "event_subtype" => "1000000238" }
            }
            # 防火墙风扇故障
            grok {
                match => {
                    "event_details" => "%{GREEDYDATA:fan_error}"
                }
            }
        } else if [temp_event_subtype] == "5" {
            mutate {
                replace => { "event_subtype" => "1000000239" }
            }
            # 防火墙温度异常
            grok {
                match => {
                    "event_details" => "%{GREEDYDATA:temperature_error} %{NUMBER:temperature}° C"
                }
            }
        } else if [temp_event_subtype] == "7" {
            mutate {
                replace => { "event_subtype" => "1000000240" }
            }
            # 网口状态异常
            grok {
                match => {
                    "event_details" => "%{DATA:interface} %{GREEDYDATA:interface_status}"
                }
            }
        }
    } else if [temp_event_type] == "3" {
        mutate {
            replace => { "event_type" => "1000065" }
        }
        if [temp_event_subtype] == "1" {
            mutate {
                replace => { "event_subtype" => "1000000241" }
            }
            # 不符合安全策略访问
            grok {
                match => {
                    "event_details" => "%{WORD:protocol} %{IP:source_address} %{NUMBER:source_port} %{IP:dest_address} %{NUMBER:dest_port}"
                }
            }
        } else if [temp_event_subtype] == "2" {
            mutate {
                replace => { "event_subtype" => "1000000242" }
            }
            # 攻击告警
            grok {
                match => {
                    "event_details" => "%{WORD:protocol} %{WORD:attack_type} %{IP:source_address} %{NUMBER:source_port} %{IP:dest_address} %{NUMBER:dest_port}"
                }
            }
        }
    }
}