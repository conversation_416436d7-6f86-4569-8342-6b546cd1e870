filter {
  ruby {
    path => "/usr/share/logstash/config/conf.d/ruby/event_processor.rb"
    script_params => {
      "patterns" => [
        {
          "regex" => '%%.*SSH_LOGOUT.*user\s+(?<uesr>\S+)\s+\(IP:\s+(?<srcIp>\S+)\)\s+logged\s+out.*$'
          "event_name" => "退出登录"
          "event_type" => "1000068"
          "event_subtype" => "1000000201"
          "extract_fields" => ["uesr", "srcIp"]
        },
        {
          "regex" => '%%.*UPDOWN.*:\s+(?<interface>\S+).*link\s+status\s+is\s+DOWN..*$'
          "event_name" => "网口拔出"
          "event_type" => "1000065"
          "event_subtype" => "1000000215"
          "extract_fields" => ["interface"]
        },
        {
          "regex" => '%%.*UPDOWN.*:\s+(?<interface>\S+).*link\s+status\s+is\s+UP..*$'
          "event_name" => "网口接入"
          "event_type" => "1000065"
          "event_subtype" => "1000000214"
          "extract_fields" => ["interface"]
        }
      ]
    }
  }

  # 添加通用字段
  mutate {
    replace => {
      "device_type" => "1000079"
      "product_name" => "华三-S5500-52C-EI"
    }
  }

  # 保存提取字段
  mutate {
    rename => {
      "uesr" => "related_object_name"
      "interface" => "action_object_name"
      "srcIp" => "source_address"
    }
  }
}
