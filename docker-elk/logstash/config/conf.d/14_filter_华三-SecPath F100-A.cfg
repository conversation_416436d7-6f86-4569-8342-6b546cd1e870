filter {
  ruby {
    path => "/usr/share/logstash/config/conf.d/ruby/event_processor.rb"
    script_params => {
      "patterns" => [
        {
          "regex" => '%%\d+(?<logType>\S+)/\d+\/LOGIN.*\s(?<user>\S+)\slogin\sfrom.*$'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => ["logType", "user"]
        },
        {
          "regex" => '^(?:.*? )?(?<logType>\S+)\s+user\s+failed\s+to\s+login\s+\S+\s+(?<srcIp>\S+)\s+on.*$'
          "event_name" => "登录失败"
          "event_type" => "1000061"
          "event_subtype" => "1000000140"
          "extract_fields" => ["logType", "srcIp"]
        },
        {
          "regex" => '%%\d+VTY.*\sfailed\sto\slogin\son.*$'
          "event_name" => "登录失败"
          "event_type" => "1000061"
          "event_subtype" => "1000000140"
          "extract_fields" => []
        },
        {
          "regex" => '%%\d+VTY.*\suser\s(?<user>\S+)\sfailed\sto\slogin\son.*$'
          "event_name" => "登录失败"
          "event_type" => "1000061"
          "event_subtype" => "1000000140"
          "extract_fields" => ["user"]
        },
        {
          "regex" => '%%\d+(?<logType>\S+)/\d+\/LOGOUT.*\s(?<user>\S+)\slogout\sfrom.*$'
          "event_name" => "退出登录"
          "event_type" => "1000068"
          "event_subtype" => "1000000201"
          "extract_fields" => ["logType", "user"]
        },
        {
          "regex" => '%%\d+(?<logType>\S+)/\d+/CMD.*\sip:(?<srcIp>.*)\suser:(?<user>\S+)\scommand:quit.*$'
          "event_name" => "退出登录"
          "event_type" => "1000068"
          "event_subtype" => "1000000201"
          "extract_fields" => ["logType", "srcIp", "user"]
        },
        {
          "regex" => 'IPFILTER.*deny\s\S+\s\((?<srcIp>\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3})\s(?<srcPort>\d+)\).*\((?<dstIp>\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3})\s(?<dstPort>\d+)\).*$'
          "event_name" => "不符合安全策略访问"
          "event_type" => "1000065"
          "event_subtype" => "1000000241"
          "extract_fields" => ["srcIp", "srcPort", "dstIp", "dstPort"]
        },
        {
          "regex" => '%%\d+IFNET.*interface\s(?<interface>\S+)\sis\sDOWN.*$'
          "event_name" => "网口拔出"
          "event_type" => "1000065"
          "event_subtype" => "1000000215"
          "extract_fields" => ["interface"]
        },
        {
          "regex" => '%%\d+PHY.*\s+(?<interface>\S+):\schange\sstatus\sto\sdown.*$'
          "event_name" => "网口拔出"
          "event_type" => "1000065"
          "event_subtype" => "1000000215"
          "extract_fields" => ["interface"]
        },
        {
          "regex" => '%%\d+.*UPDOWN.*Line\sprotocol.*interface\s(?<interface>.*)\sis\sUP.*$'
          "event_name" => "网口接入"
          "event_type" => "1000065"
          "event_subtype" => "1000000214"
          "extract_fields" => ["interface"]
        },
        {
          "regex" => '%%\d+PHY/\d+\/PHY.*\s(?<interface>.*):\schange\sstatus\sto\sup.*$'
          "event_name" => "网口接入"
          "event_type" => "1000065"
          "event_subtype" => "1000000214"
          "extract_fields" => ["interface"]
        }
      ]
    }
  }

  # 添加通用字段
  mutate {
    replace => {
      "device_type" => "1000035"
      "product_name" => "华三-SecPath F100-A"
    }
  }

  # 保存提取字段
  mutate {
    rename => {
      "dstIp" => "dest_address"
      "dstPort" => "dest_port"
      "device" => "device_name"
      "srcIp" => "source_address"
      "interface" => "action_object_name"
      "user" => "related_object_name"
      "srcPort" => "source_port"
    }
  }
}
