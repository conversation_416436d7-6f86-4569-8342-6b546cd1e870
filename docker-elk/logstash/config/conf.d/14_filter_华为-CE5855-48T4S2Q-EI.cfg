filter {
  ruby {
    path => "/usr/share/logstash/config/conf.d/ruby/event_processor.rb"
    script_params => {
      "patterns" => [
        {
          "regex" => '%%.*linkDown.*ifName=(?<interface>\S+),\s+Admin.*OperStatus=DOWN.*link\sis\sdown.*$'
          "event_name" => "网口拔出"
          "event_type" => "1000065"
          "event_subtype" => "1000000215"
          "extract_fields" => ["interface"]
        },
        {
          "regex" => '%%.*linkDown.*ifName=(?<interface>\S+),\s+Admin.*OperStatus=UP.*link\sis\sup.*$'
          "event_name" => "网口接入"
          "event_type" => "1000065"
          "event_subtype" => "1000000214"
          "extract_fields" => ["interface"]
        }
      ]
    }
  }

  # 添加通用字段
  mutate {
    replace => {
      "device_type" => "1000079"
      "product_name" => "华为-CE5855-48T4S2Q-EI"
    }
  }

  # 保存提取字段
  mutate {
    rename => {
      "interface" => "action_object_name"
    }
  }
}
