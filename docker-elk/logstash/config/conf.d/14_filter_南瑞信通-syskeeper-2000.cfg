filter {
  ruby {
    path => "/usr/share/logstash/config/conf.d/ruby/event_processor.rb"
    script_params => {
      "patterns" => [
        {
          "regex" => 'BID\s+0\s+5\s+(?<Username>\S+)\s+.*login'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => ["Username"]
        },
        {
          "regex" => 'FID\s0\s5\s(?<Username>\S+)\s.*login$'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => ["Username"]
        },
        {
          "regex" => 'BID\s+0\s+5\s+(?<Username>\S+)\s+.*login\s+failed'
          "event_name" => "登录失败"
          "event_type" => "1000061"
          "event_subtype" => "1000000140"
          "extract_fields" => ["Username"]
        },
        {
          "regex" => 'FID\s0\s5\s(?<Username>\S+)\s.*login\sfailed$'
          "event_name" => "登录失败"
          "event_type" => "1000061"
          "event_subtype" => "1000000140"
          "extract_fields" => ["Username"]
        },
        {
          "regex" => 'BID\s+0\s5\s+(?<Username>\S+)\s+.*logout$'
          "event_name" => "退出登录"
          "event_type" => "1000068"
          "event_subtype" => "1000000201"
          "extract_fields" => ["Username"]
        },
        {
          "regex" => 'FID\s0\s5\s(?<Username>\S+)\s.*logout$'
          "event_name" => "退出登录"
          "event_type" => "1000068"
          "event_subtype" => "1000000201"
          "extract_fields" => ["Username"]
        },
        {
          "regex" => 'BID\s+0\s+6\s+(?<Policychange>.+)$'
          "event_name" => "配置变更"
          "event_type" => "1000026"
          "event_subtype" => "1000000094"
          "extract_fields" => ["Policychange"]
        },
        {
          "regex" => 'FID\s0\s6\s(?<Policychange>.+)'
          "event_name" => "配置变更"
          "event_type" => "1000026"
          "event_subtype" => "1000000094"
          "extract_fields" => ["Policychange"]
        },
        {
          "regex" => 'BID\s+2\s+1\s+.+\s+(?<src_ip>.+)\s+(?<src_port>.+)\s+(?<dest_ip>.+)\s+(?<dest_port>.+)$'
          "event_name" => "不符合安全策略访问"
          "event_type" => "1000065"
          "event_subtype" => "1000000241"
          "extract_fields" => ["src_ip", "src_port", "dest_ip", "dest_port"]
        },
        {
          "regex" => 'FID\s2\s1\s.+\s+(?<src_ip>.+)\s+(?<dest_ip>.+)$'
          "event_name" => "不符合安全策略访问"
          "event_type" => "1000065"
          "event_subtype" => "1000000241"
          "extract_fields" => ["src_ip", "dest_ip"]
        },
        {
          "regex" => 'FID\s2\s1\s.+\s+(?<src_ip>.+)\s+(?<src_port>.+)\s+(?<dest_ip>.+)\s+(?<dest_port>.+)$'
          "event_name" => "不符合安全策略访问"
          "event_type" => "1000065"
          "event_subtype" => "1000000241"
          "extract_fields" => ["src_ip", "src_port", "dest_ip", "dest_port"]
        },
        {
          "regex" => 'BID\s2\s1\s.+\s+(?<src_ip>.+)\s+(?<src_port>.+)\s+(?<dest_ip>.+)\s+(?<dest_port>.+)$'
          "event_name" => "不符合安全策略访问"
          "event_type" => "1000065"
          "event_subtype" => "1000000241"
          "extract_fields" => ["src_ip", "src_port", "dest_ip", "dest_port"]
        },
        {
          "regex" => '(?<CPURate>.+%)$'
          "event_name" => "性能监控"
          "event_type" => "1000025"
          "event_subtype" => "1000000089"
          "extract_fields" => ["CPURate"]
        },
        {
          "regex" => 'BID\s0\s4\s(?<MemRate>.+%)$'
          "event_name" => "性能监控"
          "event_type" => "1000025"
          "event_subtype" => "1000000089"
          "extract_fields" => ["MemRate"]
        },
        {
          "regex" => 'FID\s0\s4\s(?<MemRate>.+%)'
          "event_name" => "性能监控"
          "event_type" => "1000025"
          "event_subtype" => "1000000089"
          "extract_fields" => ["MemRate"]
        },
        {
          "regex" => '\smem\susage=(?<MemRate>\d+(\.\d+)?%)'
          "event_name" => "性能监控"
          "event_type" => "1000025"
          "event_subtype" => "1000000089"
          "extract_fields" => ["MemRate"]
        },
        {
          "regex" => '^cpu\s+usage=(?<CPURate>\d+(\.\d+)?%)'
          "event_name" => "性能监控"
          "event_type" => "1000025"
          "event_subtype" => "1000000089"
          "extract_fields" => ["CPURate"]
        },
        {
          "regex" => 'FID\s0\s3\s(?<CPURate>.+%)'
          "event_name" => "性能监控"
          "event_type" => "1000025"
          "event_subtype" => "1000000089"
          "extract_fields" => ["CPURate"]
        }
      ]
    }
  }

  # 添加通用字段
  mutate {
    replace => {
      "device_type" => "1000077"
      "product_name" => "南瑞信通-syskeeper-2000"
    }
  }

  # 保存提取字段
  mutate {
    rename => {
      "src_port" => "source_port"
      "dest_ip" => "dest_address"
      "dest_port" => "dest_port"
      "src_ip" => "source_address"
      "Policychange" => "action_details"
      "Username" => "related_object_name"
    }
  }
}
