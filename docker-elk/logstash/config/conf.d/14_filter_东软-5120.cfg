filter {
  ruby {
    path => "/usr/share/logstash/config/conf.d/ruby/event_processor.rb"
    script_params => {
      "patterns" => [
        {
          "regex" => '用户\s+(?<Username>\S+?)从\S+?登录成功\,\s+IP为(?<SourceIP>\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}).*$'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => ["Username", "SourceIP"]
        },
        {
          "regex" => '用户\s(?<Username>\S+?)\s从\s(?<SourceIP>\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})\s通过.*?登录.*?成功.*$'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => ["Username", "SourceIP"]
        },
        {
          "regex" => '用户(?<Username>\S+?)从console:Local登录.*?成功.*$'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => ["Username"]
        },
        {
          "regex" => 'FW\s+0\s+1\s+(?<user>\S+)\s+(?<srcIp>\S+).*$'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => ["user", "srcIp"]
        },
        {
          "regex" => 'user\s+(?<Username>\S+)\s+logged\s+in.*?from\s+(?<SourceIP>\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3}).*$'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => ["Username", "SourceIP"]
        },
        {
          "regex" => '管理用户(?<Username>\S+)通过\S+登录成功.*?IP地址为(?<SourceIP>\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}).*$'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => ["Username", "SourceIP"]
        },
        {
          "regex" => 'user\s+(?<user>\S+)\s+logged\s+in\s+via\s+(?<logintype>\S+)\s+from\s+(?<SourceIP>\S+)..*$'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => ["user", "logintype", "SourceIP"]
        },
        {
          "regex" => '用户\s+(?<Username>\S+?)从\S+?登录失败\,\s+IP为(?<SourceIP>\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}).*$'
          "event_name" => "登录失败"
          "event_type" => "1000061"
          "event_subtype" => "1000000140"
          "extract_fields" => ["Username", "SourceIP"]
        },
        {
          "regex" => '用户\s(?<Username>\S+?)\s从\s(?<SourceIP>\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})\s通过.*?登录.*?失败.*$'
          "event_name" => "登录失败"
          "event_type" => "1000061"
          "event_subtype" => "1000000140"
          "extract_fields" => ["Username", "SourceIP"]
        },
        {
          "regex" => '用户(?<Username>\S+?)从console:Local登录.*?失败.*$'
          "event_name" => "登录失败"
          "event_type" => "1000061"
          "event_subtype" => "1000000140"
          "extract_fields" => ["Username"]
        },
        {
          "regex" => '^FW\s+0\s+3\s+(?<user>\S+)\s+(?<srcIp>\S+).*?$'
          "event_name" => "登录失败"
          "event_type" => "1000061"
          "event_subtype" => "1000000140"
          "extract_fields" => ["user", "srcIp"]
        },
        {
          "regex" => 'user\s+(?<user>\S+)\s+failed\s+to\s+log\s+in\s+through\s+(?<logType>\S+)\s+from\s+(?<srcIp>\S+)..*$'
          "event_name" => "登录失败"
          "event_type" => "1000061"
          "event_subtype" => "1000000140"
          "extract_fields" => ["user", "logType", "srcIp"]
        },
        {
          "regex" => '用户(?<Username>\S+)通过\S+登录失败.*?IP地址为(?<SourceIP>\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}).*$'
          "event_name" => "登录失败"
          "event_type" => "1000061"
          "event_subtype" => "1000000140"
          "extract_fields" => ["Username", "SourceIP"]
        },
        {
          "regex" => 'user\s+(?<user>\S+)\s+logged\s+in\s+failed\s+via\s+(?<logintype>\S+)\s+from\s+(?<SourceIP>\S+)..*$'
          "event_name" => "登录失败"
          "event_type" => "1000061"
          "event_subtype" => "1000000140"
          "extract_fields" => ["user", "logintype", "SourceIP"]
        },
        {
          "regex" => 'FW\s+0+\s+2\s+(?<user>\S+)\s+(?<srcIp>\S+).*?$'
          "event_name" => "退出登录"
          "event_type" => "1000068"
          "event_subtype" => "1000000201"
          "extract_fields" => ["user", "srcIp"]
        },
        {
          "regex" => 'user\s+(?<user>\S+)\s+logged\s+out.*?from\s+(?<srcIp>\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}).*$'
          "event_name" => "退出登录"
          "event_type" => "1000068"
          "event_subtype" => "1000000201"
          "extract_fields" => ["user", "srcIp"]
        },
        {
          "regex" => '用户\s*(?<Username>\S+?)从\S+?注销.*IP为(?<SourceIP>\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}).*$'
          "event_name" => "退出登录"
          "event_type" => "1000068"
          "event_subtype" => "1000000201"
          "extract_fields" => ["Username", "SourceIP"]
        },
        {
          "regex" => 'packet:(?<srcIp>\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})\((?<srcPort>\d+)\)-(?<dstIp>\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})\((?<dstPort>\d+)\)\,\s+protocol.*action:drop.*$'
          "event_name" => "不符合安全策略访问"
          "event_type" => "1000065"
          "event_subtype" => "1000000241"
          "extract_fields" => ["srcIp", "srcPort", "dstIp", "dstPort"]
        },
        {
          "regex" => 'Access\s+Policy.*?packet\S(?<srcIp>\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3})\((?<srcPort>\d+)\)-(?<dstIp>\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3})\((?<dstPort>\d+)\).*?NAT.*?action:\s+Deny.*$'
          "event_name" => "不符合安全策略访问"
          "event_type" => "1000065"
          "event_subtype" => "1000000241"
          "extract_fields" => ["srcIp", "srcPort", "dstIp", "dstPort"]
        },
        {
          "regex" => '访问策略\,\s+名称:\S+?,\s+原始地址:(?<srcIp>\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})\((?<srcPort>\d+)\)-(?<dstIp>\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})\((?<dstPort>\d+)\).*动作:drop.*$'
          "event_name" => "不符合安全策略访问"
          "event_type" => "1000065"
          "event_subtype" => "1000000241"
          "extract_fields" => ["srcIp", "srcPort", "dstIp", "dstPort"]
        },
        {
          "regex" => '访问策略\S+\s+原始地址.*?(?<srcIp>\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3})\((?<srcPort>\d+)\)->(?<dstIp>\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3})\((?<dstPort>\d+)\).*动作：阻断.*$'
          "event_name" => "不符合安全策略访问"
          "event_type" => "1000065"
          "event_subtype" => "1000000241"
          "extract_fields" => ["srcIp", "srcPort", "dstIp", "dstPort"]
        },
        {
          "regex" => '^(?<time>\S+?\s+\d{1,2}\s\d{2}:\d{2}:\d{2})?.*?包过滤,\s+原始地址:(?<srcIp>\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})\((?<srcPort>\d+)\)-(?<dstIp>\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})\((?<dstPort>\d+)\).*动作:drop.*$'
          "event_name" => "不符合安全策略访问"
          "event_type" => "1000065"
          "event_subtype" => "1000000241"
          "extract_fields" => ["srcIp", "srcPort", "dstIp", "dstPort"]
        },
        {
          "regex" => '^FW\s+3\s+1\s+\S+\s+(?<srcIp>\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3})\s+(?<srcPort>\d+)\s+(?<dstIp>\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3})\s+(?<dstPort>\d+).*$'
          "event_name" => "不符合安全策略访问"
          "event_type" => "1000065"
          "event_subtype" => "1000000241"
          "extract_fields" => ["srcIp", "srcPort", "dstIp", "dstPort"]
        },
        {
          "regex" => 'Informational\sNAT\sKernel\srep=1.*NAT转换前:源IP\s(?<srcIp>.*)\((?<srcPort>.*)\)-目的IP\s(?<dstIp>.*)\((?<dstPort>.*)\),.*源IP\s(?<csrcIp>.*)-目的IP\s(?<cdesIp>.*),协议.*$'
          "event_name" => "符合安全策略访问"
          "event_type" => "1000065"
          "event_subtype" => "1000000241"
          "extract_fields" => ["srcIp", "srcPort", "dstIp", "dstPort", "csrcIp", "cdesIp"]
        },
        {
          "regex" => '.*Informational\sSession\sKernel\srep=1.*,\s序列号.*:(?<srcIp>.*)\((?<srcPort>.*)\)-(?<dstIp>.*)\((?<dstPrort>.*)\),\s下一跳:\S+,\s右方向:(?<sip>.*)-(?<dip>.*),\s下一跳:\S+,.*$'
          "event_name" => "符合安全策略访问"
          "event_type" => "1000065"
          "event_subtype" => "1000000241"
          "extract_fields" => ["srcIp", "srcPort", "dstIp", "dstPrort", "sip", "dip"]
        },
        {
          "regex" => '访问策略\,\s名称:\S+?,\s原始地址:(?<srcIp>\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})\((?<srcPort>\d+)\)-(?<dstIp>\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})\((?<dstPort>\d+)\)\,\s协议:(?<protocol>\S*?)\,\s地址转换:\S*?\,\s安全域:\S*?\,\s动作:accept.*$'
          "event_name" => "符合安全策略访问"
          "event_type" => "1000065"
          "event_subtype" => "1000000241"
          "extract_fields" => ["srcIp", "srcPort", "dstIp", "dstPort", "protocol"]
        },
        {
          "regex" => 'Warning\s+FW.*?接口\s+(?<interface>\S+)\s+断开.*'
          "event_name" => "网口拔出"
          "event_type" => "1000065"
          "event_subtype" => "1000000215"
          "extract_fields" => ["interface"]
        },
        {
          "regex" => 'FW\s+1\s+7\s+(?<interface>\S+)\s+Link\s+down.*$'
          "event_name" => "网口拔出"
          "event_type" => "1000065"
          "event_subtype" => "1000000215"
          "extract_fields" => ["interface"]
        },
        {
          "regex" => 'Interface\s+(?<interface>\S+)\s+was\s+disconnected.*$'
          "event_name" => "网口拔出"
          "event_type" => "1000065"
          "event_subtype" => "1000000215"
          "extract_fields" => ["interface"]
        },
        {
          "regex" => 'Interface\s+(?<interface>\S+).*disconnected.*$'
          "event_name" => "网口拔出"
          "event_type" => "1000065"
          "event_subtype" => "1000000215"
          "extract_fields" => ["interface"]
        },
        {
          "regex" => '接口(?<interface>\S+)物理连接断开.*$'
          "event_name" => "网口拔出"
          "event_type" => "1000065"
          "event_subtype" => "1000000215"
          "extract_fields" => ["interface"]
        },
        {
          "regex" => 'Warning\s+FW.*?接口\s+(?<interface>\S+)\s连接.*'
          "event_name" => "网口接入"
          "event_type" => "1000065"
          "event_subtype" => "1000000214"
          "extract_fields" => ["interface"]
        },
        {
          "regex" => 'FW\s+1\s+8\s+(?<interface>\S+)\s+Link\s+up.*$'
          "event_name" => "网口接入"
          "event_type" => "1000065"
          "event_subtype" => "1000000214"
          "extract_fields" => ["interface"]
        },
        {
          "regex" => 'Interface\s+(?<interface>\S+)\s+was\s+connected.*$'
          "event_name" => "网口接入"
          "event_type" => "1000065"
          "event_subtype" => "1000000214"
          "extract_fields" => ["interface"]
        },
        {
          "regex" => 'Interface\s+(?<interface>\S+).*\s+connected.*$'
          "event_name" => "网口接入"
          "event_type" => "1000065"
          "event_subtype" => "1000000214"
          "extract_fields" => ["interface"]
        },
        {
          "regex" => '接口(?<interface>\S+)连接.*$'
          "event_name" => "网口接入"
          "event_type" => "1000065"
          "event_subtype" => "1000000214"
          "extract_fields" => ["interface"]
        }
      ]
    }
  }

  # 添加通用字段
  mutate {
    replace => {
      "device_type" => "1000035"
      "product_name" => "东软-5120"
    }
  }

  # 保存提取字段
  mutate {
    rename => {
      "dstIp" => "dest_address"
      "SourceIP" => "source_address"
      "protocol" => "protocol"
      "dstPort" => "dest_port"
      "srcIp" => "source_address"
      "dstPrort" => "dest_port"
      "dip" => "dest_address"
      "user" => "related_object_name"
      "srcPort" => "source_port"
      "interface" => "action_object_name"
      "Username" => "related_object_name"
    }
  }
}
