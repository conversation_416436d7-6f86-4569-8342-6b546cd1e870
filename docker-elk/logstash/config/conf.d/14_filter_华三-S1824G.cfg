filter {
  ruby {
    path => "/usr/share/logstash/config/conf.d/ruby/event_processor.rb"
    script_params => {
      "patterns" => [
        {
          "regex" => '%%.*WEBOPT_LOGIN_SUC\(\w+\):\s+(?<user>\S+)\s+logged\s+in\s+from\s+(?<srcIp>\S+).*$'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => ["user", "srcIp"]
        },
        {
          "regex" => '%%.*WEBOPT_LOGIN_FAIL\(\w+\):\s+(?<user>\S+)\s+failed\s+to\s+log\s+in\s+from\s+(?<srcIp>\S+).*$'
          "event_name" => "登录失败"
          "event_type" => "1000061"
          "event_subtype" => "1000000140"
          "extract_fields" => ["user", "srcIp"]
        },
        {
          "regex" => '%%.*WEBOPT_LOGOUT\(\w+\):\s+(?<user>\S+)\s+logged\s+out\s+from\s+(?<srcIp>\S+).*$'
          "event_name" => "退出登录"
          "event_type" => "1000068"
          "event_subtype" => "1000000201"
          "extract_fields" => ["user", "srcIp"]
        },
        {
          "regex" => '%%.*UPDOWN\(\w+\):\s+(?<interface>\S+\d+):{0,}\slink\sstatus\sis\sDOWN.*$'
          "event_name" => "网口拔出"
          "event_type" => "1000065"
          "event_subtype" => "1000000215"
          "extract_fields" => ["interface"]
        },
        {
          "regex" => '%%.*UPDOWN\(\w+\):\s+(?<interface>\S+\d+):{0,}\slink\sstatus\sis\sUP.*$'
          "event_name" => "网口接入"
          "event_type" => "1000065"
          "event_subtype" => "1000000214"
          "extract_fields" => ["interface"]
        }
      ]
    }
  }

  # 添加通用字段
  mutate {
    replace => {
      "device_type" => "1000079"
      "product_name" => "华三-S1824G"
    }
  }

  # 保存提取字段
  mutate {
    rename => {
      "interface" => "action_object_name"
      "user" => "related_object_name"
      "srcIp" => "source_address"
    }
  }
}
