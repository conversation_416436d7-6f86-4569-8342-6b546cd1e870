filter {
  ruby {
    path => "/usr/share/logstash/config/conf.d/ruby/event_processor.rb"
    script_params => {
      "patterns" => [
        {
          "regex" => '^"ngtos"\s"1.0"\s"(?<dateTime>\d{4}-\d{2}-\d{2}\s\d{2}:\d{2}:\d{2})"\s"TopsecOS".*user="(?<username>\S+)"\ssrc="(?<srcIp>\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})".*msg="login\ssuccess."'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => ["dateTime", "username", "srcIp"]
        },
        {
          "regex" => '^id=tos\stime="(?<dateTime>\d{4}-\d{2}-\d{2}\s\d{2}:\d{2}:\d{2})".+?type=(?<type>\S+)\suser=(?<username>\S+)\ssrc=(?<srcIp>\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})\sop="login"\sresult=\d+ .+?msg=".*?"'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => ["dateTime", "type", "username", "srcIp"]
        },
        {
          "regex" => '^id=tos\stime="(?<dateTime>\d{4}-\d{2}-\d{2}\s\d{2}:\d{2}:\d{2})".+?type=mgmt\suser=(?<username>\S+)\ssrc=(?<srcIp>\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})\sresult=\d+ recorder=AUTH\smsg=".*?success.*?"'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => ["dateTime", "username", "srcIp"]
        },
        {
          "regex" => '^SW\s0\s2\s\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3}\s\d{4}-\d{2}-\d{2}\s\d{2}:\d{2}:\d{2}\s(?<username>\S+)'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => ["username"]
        },
        {
          "regex" => '^id=tos\stime="(?<time>.*)"\sfw=\S+\s\s\s\spri=\d\stype=user_auth\s\suser="(?<user>\S+)"\sip=(?<srcIp>\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3})\sop="add"\sresult.*login.*$'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => ["user", "srcIp"]
        },
        {
          "regex" => 'id=tos.*time="(?<time>\S+\s+\S+)".*user=(?<user>\S+)\s+src=(?<srcIp>.*)\s+op="login"\s+result=.*success.*$'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => ["user", "srcIp"]
        },
        {
          "regex" => '^id=tos\stime="(?<dateTime>\d{4}-\d{2}-\d{2}\s\d{2}:\d{2}:\d{2})"\sfw.*\spri=\d+\stype=system\suser=(?<username>\S+)\ssrc=(?<srcIp>\d{1,3}(.\d{1,3}){3})\sop="login"{0,1}\sresult=-{0,1}\d+\srecorder=AUTH\smsg="(?<msg>.*faild.*)"'
          "event_name" => "登录失败"
          "event_type" => "1000061"
          "event_subtype" => "1000000140"
          "extract_fields" => ["dateTime", "username", "srcIp", "msg"]
        },
        {
          "regex" => '^id=tos\stime="(?<time>.*)"\sfw=\S+\s+pri=\d\stype=system\s+user=(?<user>\S+)\ssrc=(?<srcIp>\S+)\sop="login".*recorder=AUTH\smsg.*login\sfail.*$'
          "event_name" => "登录失败"
          "event_type" => "1000061"
          "event_subtype" => "1000000140"
          "extract_fields" => ["user", "srcIp"]
        },
        {
          "regex" => 'time="(?<time>\S+\s+\S+)".*user=(?<user>\S+)\s+src=(?<srcIp>.*)\s+op="login".*msg="faild".*$'
          "event_name" => "登录失败"
          "event_type" => "1000061"
          "event_subtype" => "1000000140"
          "extract_fields" => ["user", "srcIp"]
        },
        {
          "regex" => '^id=tos\stime="(?<dateTime>\d{4}-\d{2}-\d{2}\s\d{2}:\d{2}:\d{2})".*user=(?<username>\S+)\ssrc=(?<srcIp>\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})\sop="logout".*msg=""'
          "event_name" => "退出登录"
          "event_type" => "1000068"
          "event_subtype" => "1000000201"
          "extract_fields" => ["dateTime", "username", "srcIp"]
        },
        {
          "regex" => '^"ngtos"\s"1.0"\s"(?<dateTime>\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})"\s"TopsecOS".*user="(?<username>\S+)"\ssrc="(?<srcIp>\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})".*msg="user\slogout."'
          "event_name" => "退出登录"
          "event_type" => "1000068"
          "event_subtype" => "1000000201"
          "extract_fields" => ["dateTime", "username", "srcIp"]
        },
        {
          "regex" => '^id=tos\stime="(?<dateTime>\d{4}-\d{2}-\d{2}\s\d{2}:\d{2}:\d{2})"\sfw.*\spri=\d+\stype=\w+\s{1,2}user="{0,1}(?<username>\S+)"{0,1}\s\w+=(?<srcIp>\d{1,3}(.\d{1,3}){3})\sop="logout"\sresult=-{0,1}\d+\srecorder=\w+\smsg="(?<msg>.*logout.*)"$'
          "event_name" => "退出登录"
          "event_type" => "1000068"
          "event_subtype" => "1000000201"
          "extract_fields" => ["dateTime", "username", "srcIp", "msg"]
        },
        {
          "regex" => '^id=tos\stime="(?<dateTime>\d{4}-\d{2}-\d{2}\s\d{2}:\d{2}:\d{2})".+?type=mgmt\suser=(?<username>\S+)\ssrc=(?<srcIp>\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})\sresult=\d+ recorder=AUTH\smsg=".*?logout.*?"'
          "event_name" => "退出登录"
          "event_type" => "1000068"
          "event_subtype" => "1000000201"
          "extract_fields" => ["dateTime", "username", "srcIp"]
        },
        {
          "regex" => '^id=tos\stime="(?<time>.*)"\sfw=\S+\s\s\s\spri=\d\stype=user_auth\s\suser="(?<user>\S+)"\sip=(?<srcIp>\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3})\sop="add"\sresult.*login.*$'
          "event_name" => "退出登录"
          "event_type" => "1000068"
          "event_subtype" => "1000000201"
          "extract_fields" => ["user", "srcIp"]
        },
        {
          "regex" => '^id=tos\stime="(?<time>.*)"\sfw=\S+\s+pri=\d\stype=system\s+user=(?<user>\S+)\ssrc=(?<srcIp>\S+)\sop="logout".*recorder=AUTH\smsg.*logout.*$'
          "event_name" => "退出登录"
          "event_type" => "1000068"
          "event_subtype" => "1000000201"
          "extract_fields" => ["user", "srcIp"]
        },
        {
          "regex" => 'time="(?<time>\S+\s+\S+)".*user=(?<user>\S+)\s+src=(?<srcIp>.*)\s+op="logout".*$'
          "event_name" => "退出登录"
          "event_type" => "1000068"
          "event_subtype" => "1000000201"
          "extract_fields" => ["user", "srcIp"]
        },
        {
          "regex" => '^id=tos\stime="(?<time>.*)"\sfw=\S+\s\s\s\spri=\d\stype=mgmt\suser=(?<Username>\S+)\ssrc=(?<DeviceIp>\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3})\sresult=0\srecorder=config\smsg="(?<Command>.*)".*$'
          "event_name" => "配置变更"
          "event_type" => "1000026"
          "event_subtype" => "1000000094"
          "extract_fields" => ["Username", "DeviceIp", "Command"]
        },
        {
          "regex" => '^id=tos\stime="(?<time>.*)"\sfw=\S+\s\s\s\spri=\d\stype=mgmt\suser=(?<Username>\S+)\ssrc=(?<DeviceIp>\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3})\sresult=0\srecorder=config\smsg="(?<Command>.*)".*$'
          "event_name" => "配置变更"
          "event_type" => "1000026"
          "event_subtype" => "1000000094"
          "extract_fields" => ["Username", "DeviceIp", "Command"]
        },
        {
          "regex" => '^id=tos\stime="(?<time>.*)"\sfw=\S+\s+pri=\d\stype=mgmt\suser=(?<Username>\S+)\ssrc=(?<DeviceIp>\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3})\sresult=0\srecorder=config\smsg="(?<Command>.*)".*$'
          "event_name" => "配置变更"
          "event_type" => "1000026"
          "event_subtype" => "1000000094"
          "extract_fields" => ["Username", "DeviceIp", "Command"]
        },
        {
          "regex" => '^id=tos\stime="(?<dateTime>\d{4}-\d{2}-\d{2}\s\d{2}:\d{2}:\d{2})"\sfw=\S+\s\spri=\d\stype=dpi\srecorder=dpi_http\sproto=\w+\suser=(?<Username>.*)\sop="GET"\sdstname=(?<dstIP>\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3})\sarg=(?<arg>.*)\sconnid=\d+\spolicyid=\d+\smsg=\"(?<msg>.+?)\".*$'
          "event_name" => "APT"
          "event_type" => "1000014"
          "event_subtype" => "1000000030"
          "extract_fields" => ["dateTime", "Username", "dstIP", "arg", "msg"]
        },
        {
          "regex" => '^id=tos\s+time=.*(?<time>\d{4}-\d+-\d+\s+\S+).\s+.*fw=.*\s+recorder=.*src=(?<srcIp>\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})\s+dst=(?<dstIp>\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})\s+sport=(?<srcPort>\d+)\s+dport=(?<dstPort>\d+).*rule=(deny|reject)\s.*$'
          "event_name" => "不符合安全策略访问"
          "event_type" => "1000065"
          "event_subtype" => "1000000241"
          "extract_fields" => ["srcIp", "dstIp", "srcPort", "dstPort"]
        },
        {
          "regex" => 'id=tos\s+time=.*(?<time>\d{4}-\d+-\d+\s+\S+).\s+.*fw=.*\s+recorder=.*src=(?<srcIp>\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3})\s+dst=(?<dstIp>\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3})\s+sport=(?<srcPort>\d+)\s+dport=(?<dstPort>\d+)\s+smac=(?<smac>(\S{2}:){5}\S{2})\sdmac=(?<dmac>(\S{2}:){5}\S{2})\s+proto=\S+\s+indev=\S+\s+outdev=\S+\s+user=\s+rule=(deny|reject)\s+connid=\S+\s+parentid=\S+\s+dpiid=\S+\s+natid=\S+\s+policyid=\S+\s+msg=\S+\s*$'
          "event_name" => "不符合安全策略访问"
          "event_type" => "1000065"
          "event_subtype" => "1000000241"
          "extract_fields" => ["srcIp", "dstIp", "srcPort", "dstPort", "smac", "dmac"]
        },
        {
          "regex" => '^"ngtos"\s"1.0"\s"(?<dateTime>\d{4}-\d{2}-\d{2}\s\d{2}:\d{2}:\d{2})"\s"TopsecOS"\s"information"\s"pf"\s"pf".* src="(?<srcIp>\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3})"\sdst="(?<dstIp>\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3})"\ssport="(?<srcPort>\d+)"\sdport="(?<dstPort>\d+)"\ssmac="(?<srcMac>\S+)"\sdmac="(?<dstMac>\S+)"\sprotoid="(?<proto>\d+)".* op="deny".*$'
          "event_name" => "不符合安全策略访问"
          "event_type" => "1000065"
          "event_subtype" => "1000000241"
          "extract_fields" => ["dateTime", "srcIp", "dstIp", "srcPort", "dstPort", "srcMac", "dstMac", "proto"]
        },
        {
          "regex" => '^id=tos\stime="(?<time>.*)"\sfw=\S+\s\spri=\d\stype=pf\ssrc=(?<srcIp>\d{1,3}(.\d{1,3}){3})\sdst=(?<dstIp>\d{1,3}(.\d{1,3}){3})\ssport=(?<srcPort>\d+)\sdport=(?<dstPort>\d+)\ssmac=(?<smac>(\S{2}:){5}\S{2})\sdmac=(?<dmac>(\S{2}:){5}\S{2})\sproto=\w+\sindev=\w+\srule=reject\spolicyid=0\smsg=(?<msg>"\w+")$'
          "event_name" => "不符合安全策略访问"
          "event_type" => "1000065"
          "event_subtype" => "1000000241"
          "extract_fields" => ["srcIp", "dstIp", "srcPort", "dstPort", "smac", "dmac", "msg"]
        },
        {
          "regex" => '^"ngtos"\s"2.0"\s"(?<dateTime>\d{4}-\d{2}-\d{2}\s\d{2}:\d{2}:\d{2})"\s"TopsecOS"\s"information"\s"ac"\s"ac".*"(?<srcIp>\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3})"\s"(?<srcPort>\d+)"\s"(?<dstIp>\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3})"\s"(?<dstPort>\d+)".\"拒绝".*'
          "event_name" => "不符合安全策略访问"
          "event_type" => "1000065"
          "event_subtype" => "1000000241"
          "extract_fields" => ["dateTime", "srcIp", "srcPort", "dstIp", "dstPort"]
        },
        {
          "regex" => '^id=tos\stime="(?<time>.*)"\sfw=\S+\s\spri=\d\stype=ac\s\srecorder=FW-NAT\ssrc=(?<srcIp>\d{1,3}(.\d{1,3}){3})\sdst=(?<dstIP>\d{1,3}(.\d{1,3}){3})\ssport=(?<srcPort>\d+)\sdport=(?<dstPort>\d+)\s+smac=(?<smac>(\S{2}:){5}\S{2})\sdmac=(?<dmac>(\S{2}:){5}\S{2})\sproto=\w+\sindev=\w+\soutdev=\w+\suser=\srule=accept\s+connid=\d+.*$'
          "event_name" => "符合安全策略访问"
          "event_type" => "1000065"
          "event_subtype" => "1000000241"
          "extract_fields" => ["srcIp", "dstIP", "srcPort", "dstPort", "smac", "dmac"]
        },
        {
          "regex" => '^"ngtos"\s"2.0"\s"(?<dateTime>\d{4}-\d{2}-\d{2}\s\d{2}:\d{2}:\d{2})"\s"TopsecOS"\s"information"\s"ac"\s"ac".*"(?<srcIp>\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3})"\s"(?<srcPort>\d+)"\s"(?<dstIp>\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3})"\s"(?<dstPort>\d+)".\"允许".*'
          "event_name" => "符合安全策略访问"
          "event_type" => "1000065"
          "event_subtype" => "1000000241"
          "extract_fields" => ["dateTime", "srcIp", "srcPort", "dstIp", "dstPort"]
        },
        {
          "regex" => '^id=tos\stime="(?<dateTime>\d{4}-\d{2}-\d{2}\s\d{2}:\d{2}:\d{2})"\sfw=(\w+-{0,1}){1,6}\s\spri=\d+\stype=system\s\sop="shutdown"\sresult=\d+\srecorder=(?<interface>\w+)\smsg=(?<msg>"{0,1}.*"{0,1})$'
          "event_name" => "网口拔出"
          "event_type" => "1000065"
          "event_subtype" => "1000000215"
          "extract_fields" => ["dateTime", "interface", "msg"]
        },
        {
          "regex" => '^id=tos\stime="(?<dateTime>\d{4}-\d{2}-\d{2}\s\d{2}:\d{2}:\d{2})"\sfw=(\w+-{0,1}){1,6}\s\spri=\d+\stype=system\s\sop="CHECK LINK"\sresult=\S+\srecorder=\w+\smsg="dev=(?<interface>\w+)\sno\slinked.*?".*'
          "event_name" => "网口拔出"
          "event_type" => "1000065"
          "event_subtype" => "1000000215"
          "extract_fields" => ["dateTime", "interface"]
        },
        {
          "regex" => '^id=tos\stime="(?<time>.*)"\sfw=\S+\s+pri=\d\stype=system\s+op="shutdown"\sresult=1\srecorder=(?<interface>\S+)\smsg.*$'
          "event_name" => "网口拔出"
          "event_type" => "1000065"
          "event_subtype" => "1000000215"
          "extract_fields" => ["interface"]
        },
        {
          "regex" => '^id=tos\stime="(?<time>.*)"\s+fw.*op="CHECK\sLINK"\sresult=-1.*dev=(?<interface>\S+)\s+no\s+linked.*$'
          "event_name" => "网口拔出"
          "event_type" => "1000065"
          "event_subtype" => "1000000215"
          "extract_fields" => ["interface"]
        },
        {
          "regex" => '^id=tos\stime="(?<dateTime>\d{4}-\d{2}-\d{2}\s\d{2}:\d{2}:\d{2})"\sfw=(\w+-{0,1}){1,6}\s\spri=\d+\stype=system\s\sop="ifup"\sresult=\d+\srecorder=(?<interface>\w+)\smsg="(?<msg>.*up)"$'
          "event_name" => "网口接入"
          "event_type" => "1000065"
          "event_subtype" => "1000000214"
          "extract_fields" => ["dateTime", "interface", "msg"]
        },
        {
          "regex" => '^id=tos\stime="(?<dateTime>\d{4}-\d{2}-\d{2}\s\d{2}:\d{2}:\d{2})"\sfw=(\w+-{0,1}){1,6}\s\spri=\d+\stype=system\s\sop="interface running"\sresult=\d+\srecorder=(?<interface>\w+)\smsg="(?<msg>.*up)".*'
          "event_name" => "网口接入"
          "event_type" => "1000065"
          "event_subtype" => "1000000214"
          "extract_fields" => ["dateTime", "interface", "msg"]
        },
        {
          "regex" => '^id=tos\stime="(?<time>.*)"\sfw=\S+\s+pri=\d\stype=system\s+op="ifup"\sresult=1\srecorder=(?<interface>\S+)\smsg.*$'
          "event_name" => "网口接入"
          "event_type" => "1000065"
          "event_subtype" => "1000000214"
          "extract_fields" => ["interface"]
        }
      ]
    }
  }

  # 添加通用字段
  mutate {
    replace => {
      "device_type" => "1000035"
      "product_name" => "天融信-NGFW4000-UF"
    }
  }

  # 保存提取字段
  mutate {
    rename => {
      "Username" => "related_object_name"
      "srcIp" => "source_address"
      "interface" => "action_object_name"
      "dateTime" => "action_details"
      "Command" => "action_details"
      "srcPort" => "source_port"
      "srcMac" => "source_mac"
      "dstIP" => "dest_address"
      "username" => "related_object_name"
      "dstPort" => "dest_port"
      "user" => "related_object_name"
      "smac" => "source_mac"
      "dstIp" => "dest_address"
      "proto" => "protocol"
      "DeviceIp" => "device_name"
    }
  }
}
