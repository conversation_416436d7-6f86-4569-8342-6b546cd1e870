filter {
  ruby {
    path => "/usr/share/logstash/config/conf.d/ruby/event_processor.rb"
    script_params => {
      "patterns" => [
        {
          "regex" => '%.*LOGIN:\s-DevIP=(?<Devip>\S+);\s(?<user>\S+)\s+(?<srcIp>\S+)\s+.*$'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => ["Devip", "user", "srcIp"]
        },
        {
          "regex" => '%.*LOGIN_FAILED:\s-DevIP=(?<Devip>\S+);\s(?<user>\S+)\s+(?<srcIp>\S+)\s+.*$'
          "event_name" => "登录失败"
          "event_type" => "1000061"
          "event_subtype" => "1000000140"
          "extract_fields" => ["Devip", "user", "srcIp"]
        },
        {
          "regex" => '%.*LOGOUT:\s-DevIP=(?<Devip>\S+);\s(?<user>\S+)\s+(?<srcIp>\S+)\s+.*$'
          "event_name" => "退出登录"
          "event_type" => "1000068"
          "event_subtype" => "1000000201"
          "extract_fields" => ["Devip", "user", "srcIp"]
        },
        {
          "regex" => '%.*UPDOWN:\s-DevIP=(?<srcIp>\S+);\s.*interface\s(?<interface>\S+)\schanged\sto\sdown.*$'
          "event_name" => "网口拔出"
          "event_type" => "1000065"
          "event_subtype" => "1000000215"
          "extract_fields" => ["srcIp", "interface"]
        },
        {
          "regex" => '\s%.*UPDOWN:\s-DevIP=(?<srcIp>\S+);\s.*interface\s(?<interface>\S+)\schanged\sto\sup.*$'
          "event_name" => "网口接入"
          "event_type" => "1000065"
          "event_subtype" => "1000000214"
          "extract_fields" => ["srcIp", "interface"]
        }
      ]
    }
  }

  # 添加通用字段
  mutate {
    replace => {
      "device_type" => "1000035"
      "product_name" => "华三-F1020"
    }
  }

  # 保存提取字段
  mutate {
    rename => {
      "srcIp" => "source_address"
      "Devip" => "device_name"
      "interface" => "action_object_name"
      "user" => "related_object_name"
    }
  }
}
