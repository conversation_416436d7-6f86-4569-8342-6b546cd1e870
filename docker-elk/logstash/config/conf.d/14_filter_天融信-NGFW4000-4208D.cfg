filter {
  ruby {
    path => "/usr/share/logstash/config/conf.d/ruby/event_processor.rb"
    script_params => {
      "patterns" => [
        {
          "regex" => '^id=tos\stime="(?<time>.*)"\sfw=\S+\s+pri=\d\stype=.*\suser=(?<Username>\S+)\ssrc=(?<DeviceIp>\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3})\sop="(?<Command>.*)"\sresult=0.*$'
          "event_name" => "配置变更"
          "event_type" => "1000026"
          "event_subtype" => "1000000094"
          "extract_fields" => ["Username", "DeviceIp", "Command"]
        }
      ]
    }
  }

  # 添加通用字段
  mutate {
    replace => {
      "device_type" => "1000035"
      "product_name" => "天融信-NGFW4000-4208D"
    }
  }

  # 保存提取字段
  mutate {
    rename => {
      "DeviceIp" => "device_name"
      "Command" => "action_details"
      "Username" => "related_object_name"
    }
  }
}
