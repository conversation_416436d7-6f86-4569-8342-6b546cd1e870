filter {
  ruby {
    path => "/usr/share/logstash/config/conf.d/ruby/event_processor.rb"
    script_params => {
      "patterns" => [
        {
          "regex" => '^<\d+?>.*?:User\s(?<Username>\S+?)\(IP:(?<SourceIP>\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}).*?login\ssucceeded$'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => ["Username", "SourceIP"]
        },
        {
          "regex" => '^<\d+?>.*?User\s(?<Username>\S+?)\sfrom\s(?<SourceIP>\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})\slogin\sfailed.*$'
          "event_name" => "登录失败"
          "event_type" => "1000061"
          "event_subtype" => "1000000140"
          "extract_fields" => ["Username", "SourceIP"]
        },
        {
          "regex" => '^<\d+?>.*?user:(?<Username>\S+?)\(\w+?\)\slogout\sfrom\scon\d+?.*$'
          "event_name" => "退出登录"
          "event_type" => "1000068"
          "event_subtype" => "1000000201"
          "extract_fields" => ["Username"]
        },
        {
          "regex" => '^<\d+?>.*?\[\d+?\]:(?<InterfaceNum>\S+?)\schange\sstatus\sto\sup.*$'
          "event_name" => "网口接入"
          "event_type" => "1000065"
          "event_subtype" => "1000000214"
          "extract_fields" => ["InterfaceNum"]
        },
        {
          "regex" => '^<\d+?>.*?Interface\s(?<InterfaceNum>\d+?)\sturned\sinto\sUP\sstate.*|防火墙-网口接入$'
          "event_name" => "网口接入"
          "event_type" => "1000065"
          "event_subtype" => "1000000214"
          "extract_fields" => ["InterfaceNum"]
        }
      ]
    }
  }

  # 添加通用字段
  mutate {
    replace => {
      "device_type" => "1000035"
      "product_name" => "华为-USG9000"
    }
  }

  # 保存提取字段
  mutate {
    rename => {
      "InterfaceNum" => "action_object_name"
      "SourceIP" => "source_address"
      "Username" => "related_object_name"
    }
  }
}
