filter {
  ruby {
    path => "/usr/share/logstash/config/conf.d/ruby/event_processor.rb"
    script_params => {
      "patterns" => [
        {
          "regex" => '^(?<host>\S+).*/(?<logType>\S+)_.*\s+user\s+(?<user>\S+)\s+\(IP:\s+(?<srcIp>.*)\)\s+connected\s+to\s+the\s+server\s+successfully.*$'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => ["host", "logType", "user", "srcIp"]
        },
        {
          "regex" => '%%10SHELL/5\/\S+:\s-DevIP=(?<Devip>\S+);\s(?<user>\S+)\slogged\sin\sfrom\s(?<srcIp>\S+).*$'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => ["Devip", "user", "srcIp"]
        },
        {
          "regex" => '/(?<logType>\S+)_.*Authentication\s+failed\s+for\s+user\s+(?<user>\S+)\s+from\s+(?<srcIp>\S+).*?$'
          "event_name" => "登录失败"
          "event_type" => "1000061"
          "event_subtype" => "1000000140"
          "extract_fields" => ["logType", "user", "srcIp"]
        },
        {
          "regex" => '%%10SSHS/6/(?<logType>\S+)_.*Connection\s+closed\s+by\s+(?<srcIp>\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3}).*$'
          "event_name" => "退出登录"
          "event_type" => "1000068"
          "event_subtype" => "1000000201"
          "extract_fields" => ["logType", "srcIp"]
        },
        {
          "regex" => '/(?<logType>\S+)_.*\s+user\s+(?<user>\S+)\s+\(IP:\s+(?<srcIp>.*)\)\s+disconnected\s+from\s+the\s+server.*$'
          "event_name" => "退出登录"
          "event_type" => "1000068"
          "event_subtype" => "1000000201"
          "extract_fields" => ["logType", "user", "srcIp"]
        },
        {
          "regex" => '\/(?<logType>\S+)_.*\s+User\s+(?<user>\S+)\s+logged\s+out\s+from\s+(?<srcIp>\S+).*$'
          "event_name" => "退出登录"
          "event_type" => "1000068"
          "event_subtype" => "1000000201"
          "extract_fields" => ["logType", "user", "srcIp"]
        },
        {
          "regex" => '%%10SHELL/5\/\S+:\s-DevIP=(?<Devip>\S+);\s(?<user>\S+)\slogged\sout\sfrom\s(?<srcIp>\S+)..*'
          "event_name" => "退出登录"
          "event_type" => "1000068"
          "event_subtype" => "1000000201"
          "extract_fields" => ["Devip", "user", "srcIp"]
        },
        {
          "regex" => '\/(?<logType>\S+)_.*IPAddr=(?<srcIp>\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3})-User=(?<user>\S+);\s+Command\s+is\s+(?<Command>.*).*$'
          "event_name" => "配置变更"
          "event_type" => "1000026"
          "event_subtype" => "1000000094"
          "extract_fields" => ["logType", "srcIp", "user", "Command"]
        },
        {
          "regex" => '%%10IFNET/5\/\S+\s-DevIP=(?<srcIp>\S+);\s.*interface\s(?<interface>\S+)\schanged\sto\sdown.*'
          "event_name" => "网口拔出"
          "event_type" => "1000065"
          "event_subtype" => "1000000215"
          "extract_fields" => ["srcIp", "interface"]
        },
        {
          "regex" => '%%10IFNET/5\/\S+\s-DevIP=(?<srcIp>\S+);\s.*interface\s(?<interface>\S+)\schanged\sto\sup.*'
          "event_name" => "网口接入"
          "event_type" => "1000065"
          "event_subtype" => "1000000214"
          "extract_fields" => ["device", "srcIp", "interface"]
        }
      ]
    }
  }

  # 添加通用字段
  mutate {
    replace => {
      "device_type" => "1000079"
      "product_name" => "华三-S10510"
    }
  }

  # 保存提取字段
  mutate {
    rename => {
      "Command" => "action_details"
      "device" => "device_name"
      "srcIp" => "source_address"
      "host" => "source_hostname"
      "Devip" => "device_name"
      "interface" => "action_object_name"
      "user" => "related_object_name"
    }
  }
}
