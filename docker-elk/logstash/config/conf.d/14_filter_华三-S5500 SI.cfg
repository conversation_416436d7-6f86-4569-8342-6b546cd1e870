filter {
  ruby {
    path => "/usr/share/logstash/config/conf.d/ruby/event_processor.rb"
    script_params => {
      "patterns" => [
        {
          "regex" => '%%.*LINK\sUPDOWN.*DevIP=(?<devIp>\S+)\s+(?<interface>\S+):\slink\sstatus\sis\sDOWN.*$'
          "event_name" => "网口拔出"
          "event_type" => "1000065"
          "event_subtype" => "1000000215"
          "extract_fields" => ["devIp", "interface"]
        },
        {
          "regex" => '%%.*LINK_UPDOWN\(\w+\):\s(?<interface>\S+)\slink\sstatus\sis\sUP.*$'
          "event_name" => "网口接入"
          "event_type" => "1000065"
          "event_subtype" => "1000000214"
          "extract_fields" => ["interface"]
        }
      ]
    }
  }

  # 添加通用字段
  mutate {
    replace => {
      "device_type" => "1000079"
      "product_name" => "华三-S5500 SI"
    }
  }

  # 保存提取字段
  mutate {
    rename => {
      "interface" => "action_object_name"
    }
  }
}
