filter {
  ruby {
    path => "/usr/share/logstash/config/conf.d/ruby/event_processor.rb"
    script_params => {
      "patterns" => [
        {
          "regex" => 'web\s+user\s+name\s+(?<user>\S+)\s+ip\s+(?<srcIp>\S+)\s+log\s+in\s+succeed.*$'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => ["user", "srcIp"]
        },
        {
          "regex" => 'telnet\s+name\s+ip\s+address\s+(?<srcIp>\S+)\s+log\s+in\s+failed.*$'
          "event_name" => "登录失败"
          "event_type" => "1000061"
          "event_subtype" => "1000000140"
          "extract_fields" => ["srcIp"]
        },
        {
          "regex" => 'web\s+user\s+name\s+(?<user>\S+)\s+ip\s+(?<srcIp>\S+)\s+log\s+in\s+failed.*$'
          "event_name" => "登录失败"
          "event_type" => "1000061"
          "event_subtype" => "1000000140"
          "extract_fields" => ["user", "srcIp"]
        },
        {
          "regex" => 'web\s+user\s+name\s+(?<user>\S+)\s+ip\s+(?<srcIp>\S+)\s+log\s+out\s+succeed.*$'
          "event_name" => "退出登录"
          "event_type" => "1000068"
          "event_subtype" => "1000000201"
          "extract_fields" => ["user", "srcIp"]
        },
        {
          "regex" => 'interface\s+status.*port\s+(?<interface>\S+).*link\s+down.*$'
          "event_name" => "网口拔出"
          "event_type" => "1000065"
          "event_subtype" => "1000000215"
          "extract_fields" => ["interface"]
        },
        {
          "regex" => 'interface\s+status.*port\s+(?<interface>\S+).*link\s+up.*$'
          "event_name" => "网口接入"
          "event_type" => "1000065"
          "event_subtype" => "1000000214"
          "extract_fields" => ["interface"]
        }
      ]
    }
  }

  # 添加通用字段
  mutate {
    replace => {
      "device_type" => "1000079"
      "product_name" => "南瑞-EPS7100"
    }
  }

  # 保存提取字段
  mutate {
    rename => {
      "interface" => "action_object_name"
      "user" => "related_object_name"
      "srcIp" => "source_address"
    }
  }
}
