filter {
  ruby {
    path => "/usr/share/logstash/config/conf.d/ruby/event_processor.rb"
    script_params => {
      "patterns" => [
        {
          "regex" => '%SEC_LOGIN-5-LOGIN_SUCCESS:\sLogin\sSuccess\s\[user:\s(?<Username>\S+)\]\s\[Source:\s(?<loginSourceIp>\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})\]\s\[localport:\s(?<localport>\d+)\]\s.*$'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => ["Username", "loginSourceIp", "localport"]
        },
        {
          "regex" => '^%SEC_LOGIN-4-LOGIN_FAILED:\sLogin\sfailed\s\[user:\s(?<Username>.*?)\]\s\[Source:\s(?<loginSourceIp>\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})\]\s\[localport:\s(?<localport>\d+)\]\s.*'
          "event_name" => "登录失败"
          "event_type" => "1000061"
          "event_subtype" => "1000000140"
          "extract_fields" => ["Username", "loginSourceIp", "localport"]
        },
        {
          "regex" => '%LINK-3-UPDOWN:\sInterface\s(?<interface>\S+),\schanged\sstate\sto\sdown'
          "event_name" => "网口拔出"
          "event_type" => "1000065"
          "event_subtype" => "1000000215"
          "extract_fields" => ["interface"]
        },
        {
          "regex" => 'UPDOWN.*Interface\s(?<interface>\S+),\schanged\sstate\sto\sup'
          "event_name" => "网口接入"
          "event_type" => "1000065"
          "event_subtype" => "1000000214"
          "extract_fields" => ["interface"]
        }
      ]
    }
  }

  # 添加通用字段
  mutate {
    replace => {
      "device_type" => "1000079"
      "product_name" => "思科-6501"
    }
  }

  # 保存提取字段
  mutate {
    rename => {
      "interface" => "action_object_name"
      "loginSourceIp" => "source_address"
      "localport" => "source_port"
      "Username" => "related_object_name"
    }
  }
}
