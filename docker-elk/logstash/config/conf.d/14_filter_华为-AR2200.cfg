filter {
  ruby {
    path => "/usr/share/logstash/config/conf.d/ruby/event_processor.rb"
    script_params => {
      "patterns" => [
        {
          "regex" => '^<\d+?>(?<time>\S+\s+\d+\s+\S+).*%.*CMDRECORD.*Ip=(?<srcIp>\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3}).*User=(?<user>\S+),.*log\s+on\s+\(ssh\s+user\).*Result=Success.*$'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => ["srcIp", "user"]
        },
        {
          "regex" => '^<\d+?>(?<time>\S+\s+\d+\s+\S+).*%.*Failed\s+to\s+log\s+in\s+through\s+SSH.*Ip=(?<srcIp>\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3}).*UserName=(?<user>\S+),.*$'
          "event_name" => "登录失败"
          "event_type" => "1000061"
          "event_subtype" => "1000000140"
          "extract_fields" => ["srcIp", "user"]
        },
        {
          "regex" => '%.*CMDRECORD.*Ip=(?<srcIp>\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3}).*User=(?<user>\S+),.*log\s+off\s+\(ssh\s+user\).*Result=Success.*$'
          "event_name" => "退出登录"
          "event_type" => "1000068"
          "event_subtype" => "1000000201"
          "extract_fields" => ["srcIp", "user"]
        },
        {
          "regex" => 'IF_STATE.*?Interface\s(?<interface>\S+?)\shas\sturned\sinto\sDOWN\sstate.*$'
          "event_name" => "网口拔出"
          "event_type" => "1000065"
          "event_subtype" => "1000000215"
          "extract_fields" => ["interface"]
        },
        {
          "regex" => 'IF_STATE.*?Interface\s(?<interface>\S+?)\shas\sturned\sinto\sUP\sstate.*$'
          "event_name" => "网口接入"
          "event_type" => "1000065"
          "event_subtype" => "1000000214"
          "extract_fields" => ["interface"]
        }
      ]
    }
  }

  # 添加通用字段
  mutate {
    replace => {
      "device_type" => "1000035"
      "product_name" => "华为-AR2200"
    }
  }

  # 保存提取字段
  mutate {
    rename => {
      "interface" => "action_object_name"
      "user" => "related_object_name"
      "srcIp" => "source_address"
    }
  }
}
