filter {
  grok {
    match => {
      "message" => "DCD %{TIMESTAMP_ISO8601:event_start_time} %{DATA:product_version} %{IP:product_name} %{DATA:manufacturer} %{DATA:temp_device_type} %{NUMBER:temp_event_type} %{NUMBER:temp_event_subtype} %{NUMBER:repeat_count} %{GREEDYDATA:event_details}"
    }
  }
  if [hostname] {
      mutate {
          replace => {
              device_name => "%{hostname}"
          }
      }
  }
  mutate {
      replace => {
          "device_type" => "1000081"
      }
  }
  # DCD设备处理
  if [temp_device_type] == "DCD" {
    mutate {
      replace => { "related_object_category" => "DCD" }
    }

    if [temp_event_type] == "5" {
      if [temp_event_subtype] == "15" {
        # 系统登录成功
        grok {
          match => { "event_details" => "%{DATA:related_object_name} %{IP:login_address}" }
        }
        mutate {
          replace => { 
            "event_type" => "1000061" 
            "event_subtype" => "1000000138"
          }
        }
      } else if [temp_event_subtype] == "16" {
        # 系统退出登录
        grok {
          match => { "event_details" => "%{DATA:related_object_name} %{IP:login_address}" }
        }
        mutate {
          replace => { 
            "event_type" => "1000061" 
            "event_subtype" => "1000000141"
          }
        }
      } else if [temp_event_subtype] == "18" {
        # USB设备拔出
        grok {
          match => { "event_details" => "%{NUMBER:usb_status} %{NUMBER:usb_device_type} {%{DATA:device_name}}{%{DATA:manufacturer}}" }
        }
        mutate {
          replace => { 
            "event_type" => "1000061" 
            "event_subtype" => "1000000250"
          }
        }
      } else if [temp_event_subtype] == "1" {
        # 本地管理界面登录成功
        grok {
          match => { "event_details" => "%{DATA:related_object_name} %{DATA:login_source}" }
        }
        mutate {
          replace => { 
            "event_type" => "1000061" 
            "event_subtype" => "1000000251"
          }
        }
      } else if [temp_event_subtype] == "2" {
        # 本地管理界面退出登录
        grok {
          match => { "event_details" => "%{DATA:related_object_name} %{DATA:login_source}" }
        }
        mutate {
          replace => { 
            "event_type" => "1000061" 
            "event_subtype" => "1000000252"
          }
        }
      } else if [temp_event_subtype] == "3" {
        # 配置变更
        grok {
          match => { "event_details" => "%{NUMBER:operation_subject} %{DATA:related_object_name} %{DATA:operation_source} %{GREEDYDATA:config_change}" }
        }
        mutate {
          replace => { 
            "event_type" => "1000061" 
            "event_subtype" => "1000000253"
          }
        }
        translate {
          field => "operation_subject"
          destination => "action_details"
          dictionary => {
            "1" => "主站平台通过服务代理"
            "2" => "通过本地管理界面"
            "3" => "其他"
          }
          fallback => "未知操作主体"
        }
      } else if [temp_event_subtype] == "17" {
        # USB设备（非无线网卡类）插入
        grok {
          match => { "event_details" => "%{NUMBER:usb_status} %{NUMBER:usb_device_type} {%{DATA:device_name}}{%{DATA:manufacturer}}" }
        }
        mutate {
          replace => { 
            "event_type" => "1000065" 
            "event_subtype" => "1000000203"
          }
        }
      } else if [temp_event_subtype] == "42" {
        # USB设备（无线网卡）插入
        grok {
          match => { "event_details" => "%{NUMBER:usb_status} %{NUMBER:usb_device_type} {%{DATA:device_name}}{%{DATA:manufacturer}}" }
        }
        mutate {
          replace => { 
            "event_type" => "1000065" 
            "event_subtype" => "1000000203"
          }
        }
      } else if [temp_event_subtype] == "25" {
        # 外联事件
        grok {
          match => { "event_details" => "%{WORD:protocol} %{IP:source_address} %{NUMBER:source_port} %{IP:dest_address} %{NUMBER:destination_port}" }
        }
        mutate {
          replace => { 
            "event_type" => "1000065" 
            "event_subtype" => "1000000254"
          }
        }
      } else if [temp_event_subtype] == "41" {
        # 系统登录失败超过阈值
        grok {
          match => { "event_details" => "%{DATA:related_object_name} %{IP:login_address}" }
        }
        mutate {
          replace => { 
            "event_type" => "1000065" 
            "event_subtype" => "1000000255"
          }
        }
      } else if [temp_event_subtype] == "36" {
        # 危险操作
        grok {
          match => { "event_details" => "%{IP:action_object_name} %{IP:related_object_address} %{DATA:login_user} %{DATA:operation_path} %{GREEDYDATA:operation_command}" }
        }
        mutate {
          replace => { 
            "event_type" => "1000065" 
            "event_subtype" => "1000000256"
          }
        }
      } else if [temp_event_subtype] == "46" {
        # 开放非法端口
        grok {
          match => { "event_details" => "%{WORD:protocol} %{NUMBER:port}" }
        }
        mutate {
          replace => { 
            "event_type" => "1000065" 
            "event_subtype" => "1000000213"
          }
        }
      } else if [temp_event_subtype] == "47" {
        # 网口UP
        grok {
          match => { "event_details" => "%{DATA:interface_name}" }
        }
        mutate {
          replace => { 
            "event_type" => "1000065" 
            "event_subtype" => "1000000214"
          }
        }
      } else if [temp_event_subtype] == "48" {
        # 网口DOWN
        grok {
          match => { "event_details" => "%{DATA:interface_name}" }
        }
        mutate {
          replace => { 
            "event_type" => "1000065" 
            "event_subtype" => "1000000215"
          }
        }
      } else if [temp_event_subtype] == "4" {
        # CPU利用率超过阈值
        grok {
          match => { "event_details" => "%{NUMBER:current_cpu_usage} %{NUMBER:cpu_threshold}" }
        }
        mutate {
          replace => { 
            "event_type" => "1000065" 
            "event_subtype" => "1000000257"
          }
        }
      } else if [temp_event_subtype] == "5" {
        # 内存使用率超过阈值
        grok {
          match => { "event_details" => "%{NUMBER:current_memory_usage} %{NUMBER:memory_threshold}" }
        }
        mutate {
          replace => { 
            "event_type" => "1000065" 
            "event_subtype" => "1000000258"
          }
        }
      } else if [temp_event_subtype] == "6" {
        # 磁盘空间使用率超过阈值
        grok {
          match => { "event_details" => "%{NUMBER:current_disk_usage} %{NUMBER:disk_threshold} %{DATA:partition_description}" }
        }
        mutate {
          replace => { 
            "event_type" => "1000065" 
            "event_subtype" => "1000000259"
          }
        }
      } else if [temp_event_subtype] == "7" {
        # 本地管理界面登录失败被锁定
        grok {
          match => { "event_details" => "%{DATA:related_object_name} %{IP:login_address}" }
        }
        mutate {
          replace => { 
            "event_type" => "1000065" 
            "event_subtype" => "1000000260"
          }
        }
      } else if [temp_event_subtype] == "8" {
        # 装置异常告警
        grok {
          match => { "event_details" => "%{GREEDYDATA:abnormal_content}" }
        }
        mutate {
          replace => { 
            "event_type" => "1000065" 
            "event_subtype" => "1000000261"
          }
        }
      } else if [temp_event_subtype] == "9" {
        # 对时异常
        grok {
          match => { "event_details" => "%{DATA:time_sync_method} %{GREEDYDATA:error_description}" }
        }
        mutate {
          replace => { 
            "event_type" => "1000065" 
            "event_subtype" => "1000000262"
          }
        }
      } else if [temp_event_subtype] == "10" {
        # 验签错误
        grok {
          match => { "event_details" => "%{NUMBER:process_category} %{IP:communication_peer_ip}" }
        }
        mutate {
          replace => { 
            "event_type" => "1000065" 
            "event_subtype" => "1000000263"
          }
        }
        translate {
          field => "process_classification"
          destination => "action_details"
          dictionary => {
            "1" => "事件上报过程"
            "2" => "服务代理过程"
            "3" => "其他"
          }
          fallback => "未知过程分类"
        }
      }
    }
  } else if [temp_device_type] == "SW" {   # SW设备处理
    mutate {
      replace => {
        "related_object_category" => "SW"
      }
    }

    if [temp_event_type] == "0" {
      mutate {
        replace => { "event_type" => "1000061" }
      }
      if [temp_event_subtype] == "1" {
        # MAC 地址绑定关系变更
        grok {
          match => { "event_details" => "^%{DATA:port} %{GREEDYDATA:mac_addresses}$" }
        }
        mutate {
          replace => { "event_subtype" => "1000000264" }
        }
      } else if [temp_event_subtype] == "2" {
        # 交换机上线
        grok {
          match => { "event_details" => "^%{WORD:status}$" }
        }
        mutate {
          replace => { "event_subtype" => "1000000265" }
        }
      } else if [temp_event_subtype] == "3" {
        # 登录成功
        grok {
          match => { "event_details" => "^%{DATA:related_object_name} %{IP:login_address}$" }
        }
        mutate {
          replace => { "event_subtype" => "1000000138" }
        }
      } else if [temp_event_subtype] == "4" {
        # 退出登录
        grok {
          match => { "event_details" => "^%{DATA:related_object_name} %{IP:login_address}$" }
        }
        mutate {
          replace => { "event_subtype" => "1000000141" }
        }
      } else if [temp_event_subtype] == "5" {
        # 登录失败
        grok {
          match => { "event_details" => "^%{DATA:related_object_name} %{IP:login_address}$" }
        }
        mutate {
          replace => { "event_subtype" => "1000000140" }
        }
      } else if [temp_event_subtype] == "6" {
        # 修改用户密码
        grok {
          match => { "event_details" => "^%{DATA:related_object_name} %{IP:login_address} %{DATA:modified_user}$" }
        }
        mutate {
          replace => { "event_subtype" => "1000000266" }
        }
      } else if [temp_event_subtype] == "7" {
        # 用户操作信息
        grok {
          match => { "event_details" => "^%{DATA:related_object_name} %{IP:login_address} %{GREEDYDATA:operation_content}$" }
        }
        mutate {
          replace => { "event_subtype" => "1000000139" }
        }
      }
    } else if [temp_event_type] == "1" {
      mutate {
        replace => { "event_type" => "1000065" }
      }
      if [temp_event_subtype] == "1" {
        # 网口 UP
        grok {
          match => { "event_details" => "^%{NUMBER:port_number}$" }
        }
        mutate {
          replace => { "event_subtype" => "1000000214" }
        }
      } else if [temp_event_subtype] == "2" {
        # 网口 DOWN
        grok {
          match => { "event_details" => "^%{NUMBER:port_number}$" }
        }
        mutate {
          replace => { "event_subtype" => "1000000215" }
        }
      } else if [temp_event_subtype] == "3" {
        # 网口流量超过阈值
        grok {
          match => { "event_details" => "^%{NUMBER:port_number} %{NUMBER:current_usage} %{NUMBER:threshold}$" }
        }
        mutate {
          replace => { "event_subtype" => "1000000267" }
        }
      } else if [temp_event_subtype] == "4" {
        # 交换机离线
        grok {
          match => { "event_details" => "^%{WORD:status}$" }
        }
        mutate {
          replace => { "event_subtype" => "1000000268" }
        }
      } else if [temp_event_subtype] == "5" {
        # 端口未绑定 MAC 地址
        grok {
          match => { "event_details" => "^%{GREEDYDATA:port_numbers}$" }
        }
        mutate {
          replace => { "event_subtype" => "1000000269" }
        }
      }
    }
  } else if [temp_device_type] == "FW" {   # FW设备处理
    mutate {
      replace => { "related_object_category" => "FW" }
    }

    if [temp_event_type] == "0" {
      mutate {
        replace => { "event_type" => "1000061" }
      }
      if [temp_event_subtype] == "1" {
        # 登录成功
        grok {
          match => { "event_details" => "%{DATA:related_object_name} %{IP:login_address}" }
        }
        mutate {
          replace => { "event_subtype" => "1000000138" }
        }
      } else if [temp_event_subtype] == "2" {
        # 退出登录
        grok {
          match => { "event_details" => "%{DATA:related_object_name} %{IP:login_address}" }
        }
        mutate {
          replace => { "event_subtype" => "1000000141" }
        }
      } else if [temp_event_subtype] == "3" {
        # 登录失败
        grok {
          match => { "event_details" => "%{DATA:related_object_name} %{IP:login_address}" }
        }
        mutate {
          replace => { "event_subtype" => "1000000140" }
        }
      } else if [temp_event_subtype] == "4" {
        # 修改策略
        grok {
          match => { "event_details" => "%{DATA:related_object_name} %{IP:login_address} %{GREEDYDATA:policy_change}" }
        }
        mutate {
          replace => { "event_subtype" => "1000000270" }
        }
      } else if [temp_event_subtype] == "5" {
        # 防火墙上线
        mutate {
          replace => { "event_subtype" => "1000000271" }
        }
      }
    } else if [temp_event_type] == "3" {
      mutate {
        replace => { "event_type" => "1000065" }
      }
      if [temp_event_subtype] == "1" {
        # 不符合安全策略访问
        grok {
          match => { "event_details" => "%{WORD:protocol} %{IP:source_address} %{NUMBER:source_port} %{IP:dest_address} %{NUMBER:destination_port}" }
        }
        mutate {
          replace => { "event_subtype" => "1000000241" }
        }
      } else if [temp_event_subtype] == "2" {
        # 攻击告警
        grok {
          match => { "event_details" => "%{WORD:protocol} %{WORD:attack_type} %{IP:attacker_ip} %{NUMBER:attacker_port} %{IP:target_ip} %{NUMBER:target_port}" }
        }
        mutate {
          replace => { "event_subtype" => "1000000242" }
        }
      } else if [temp_event_subtype] == "3" {
        # 防火墙离线
        mutate {
          replace => { "event_subtype" => "1000000272" }
        }
      } else if [temp_event_subtype] == "4" {
        # CPU利用率超过阈值
        grok {
          match => { "event_details" => "%{NUMBER:current_cpu_usage} %{NUMBER:cpu_threshold}" }
        }
        mutate {
          replace => { "event_subtype" => "1000000257" }
        }
      } else if [temp_event_subtype] == "5" {
        # 内存使用率超过阈值
        grok {
          match => { "event_details" => "%{NUMBER:current_memory_usage} %{NUMBER:memory_threshold}" }
        }
        mutate {
          replace => { "event_subtype" => "1000000258" }
        }
      }
    }
  } else if [temp_device_type] == "FID" or [temp_device_type] == "BID" { # FID设备处理
    mutate {
      replace => {
        "related_object_category" => "%{temp_device_type}"
      }
    }

    # 根据不同的event_type和event_subtype设置具体的事件类型和子类型描述
    if [temp_event_type] == "0" {
      mutate {
        replace => { "event_type" => "1000061" }
      }
      if [temp_event_subtype] == "1" {
        # 隔离装置上线
        mutate {
          replace => { "event_subtype" => "1000000273" }
        }
      }
    } else if [temp_event_type] == "2" {
      mutate {
        replace => { "event_type" => "1000065" }
      }
      if [temp_event_subtype] == "1" {
        # 不符合安全策略访问
        grok {
          match => { "event_details" => "%{WORD:protocol} %{IP:source_address} %{NUMBER:source_port} %{IP:dest_address} %{NUMBER:destination_port}" }
        }
        mutate {
          replace => { "event_subtype" => "1000000241" }
        }
      } else if [temp_event_subtype] == "2" {
        # 隔离装置离线
        mutate {
          replace => { "event_subtype" => "1000000277" }
        }
      } else if [temp_event_subtype] == "3" {
        # CPU利用率超过阈值
        mutate {
          replace => { "event_subtype" => "1000000257" }
        }
      } else if [temp_event_subtype] == "4" {
        # 内存使用率超过阈值
        mutate {
          replace => { "event_subtype" => "1000000258" }
        }
      }
    }
  } else if [temp_device_type] == "SVR" {   # 服务器处理
    mutate {
      replace => {
        "related_object_category" => "SVR"
      }
    }

    # 根据不同的event_type和event_subtype设置具体的事件类型和子类型描述
    if [temp_event_type] == "5" {
      if [temp_event_subtype] == "15" {
        # 登录成功
        grok {
          match => { "event_details" => "%{DATA:related_object_name} %{IP:login_address}" }
        }
        mutate {
          replace => { 
            "event_type" => "1000061" 
            "event_subtype" => "1000000138"
          }
        }
      } else if [temp_event_subtype] == "16" {
        # 退出登录
        grok {
          match => { "event_details" => "%{DATA:related_object_name} %{IP:login_address}" }
        }
        mutate {
          replace => { 
            "event_type" => "1000061" 
            "event_subtype" => "1000000141"
          }
        }
      } else if [temp_event_subtype] == "18" {
        # USB设备拔出
        grok {
          match => { "event_details" => "%{NUMBER:usb_status} %{NUMBER:usb_device_type} {%{DATA:device_name}}{%{DATA:manufacturer}}" }
        }
        mutate {
          replace => { 
            "event_type" => "1000061" 
            "event_subtype" => "1000000250"
          }
        }
      } else if [temp_event_subtype] == "20" {
        # 串口释放
        grok {
          match => { "event_details" => "%{NUMBER:serial_status} %{DATA:serial_port}" }
        }
        mutate {
          replace => { 
            "event_type" => "1000061" 
            "event_subtype" => "1000000278"
          }
        }
      } else if [temp_event_subtype] == "22" {
        # 并口释放
        grok {
          match => { "event_details" => "%{NUMBER:parallel_status} %{DATA:parallel_port}" }
        }
        mutate {
          replace => { 
            "event_type" => "1000061" 
            "event_subtype" => "1000000279"
          }
        }
      } else if [temp_event_subtype] == "24" {
        # 光驱卸载
        grok {
          match => { "event_details" => "%{NUMBER:cdrom_status} %{DATA:cdrom_name}" }
        }
        mutate {
          replace => { 
            "event_type" => "1000061" 
            "event_subtype" => "1000000280"
          }
        }
      } else if [temp_event_subtype] == "43" {
        # 设备上线
        grok {
          match => { "event_details" => "%{DATA:online_status}" }
        }
        mutate {
          replace => { 
            "event_type" => "1000061" 
            "event_subtype" => "1000000281"
          }
        }
      } else if [temp_event_subtype] == "17" {
        # USB设备插入
        grok {
          match => { "event_details" => "%{NUMBER:usb_status} %{NUMBER:usb_device_type} {%{DATA:device_name}}{%{DATA:manufacturer}}" }
        }
        mutate {
          replace => { 
            "event_type" => "1000065" 
            "event_subtype" => "1000000203"
          }
        }
      } else if [temp_event_subtype] == "42" {
        # USB设备插入
        grok {
          match => { "event_details" => "%{NUMBER:usb_status} %{NUMBER:usb_device_type} {%{DATA:device_name}}{%{DATA:manufacturer}}" }
        }
        mutate {
          replace => { 
            "event_type" => "1000065" 
            "event_subtype" => "1000000203"
          }
        }
      } else if [temp_event_subtype] == "19" {
        # 串口占用
        mutate {
          replace => {
            "event_type" => "1000065"
            "event_subtype" => "1000000205"
          }
        }
      } else if [temp_event_subtype] == "21" {
        # 并口占用
        mutate {
          replace => {
            "event_type" => "1000065"
            "event_subtype" => "1000000207"
          }
        }
      } else if [temp_event_subtype] == "23" {
        # 光驱挂载
        mutate {
          replace => {
            "event_type" => "1000065"
            "event_subtype" => "1000000209"
          }
        }
      } else if [temp_event_subtype] == "25" {
        # 外联事件
        grok {
          match => { "event_details" => "%{WORD:protocol} %{IP:source_address} %{NUMBER:source_port} %{IP:dest_address} %{NUMBER:dest_port}" }
        }
        mutate {
          replace => { 
            "event_type" => "1000065" 
            "event_subtype" => "1000000254"
          }
        }
      } else if [temp_event_subtype] == "41" {
        # 登录失败超过阈值
        grok {
          match => { "event_details" => "%{DATA:related_object_name} %{IP:login_address}" }
        }
        mutate {
          replace => { 
            "event_type" => "1000065" 
            "event_subtype" => "1000000255"
          }
        }
      } else if [temp_event_subtype] == "34" {
        # 关键文件变更
        grok {
          match => { "event_details" => "%{IP:action_object_name} %{DATA:related_object_name} %{DATA:file_path} %{NUMBER:change_type} %{NUMBER:old_permission} %{NUMBER:new_permission}" }
        }
        mutate {
          replace => { 
            "event_type" => "1000065" 
            "event_subtype" => "1000000282"
          }
        }
      } else if [temp_event_subtype] == "35" {
        # 用户权限变更
        grok {
          match => { "event_details" => "%{IP:action_object_name} %{DATA:operator} %{DATA:target_user} %{DATA:change_content}" }
        }
        mutate {
          replace => { 
            "event_type" => "1000065" 
            "event_subtype" => "1000000217"
          }
        }
      } else if [temp_event_subtype] == "36" {
        # 危险操作
        grok {
          match => { "event_details" => "%{IP:action_object_name} %{IP:related_object_address} %{DATA:related_object_name} %{DATA:operation_path} %{GREEDYDATA:operation_command}" }
        }
        mutate {
          replace => { 
            "event_type" => "1000065" 
            "event_subtype" => "1000000256"
          }
        }
      } else if [temp_event_subtype] == "44" {
        mutate {
          replace => { 
            "event_type" => "1000065" 
            "event_subtype" => "1000000283"
          }
        }
      } else if [temp_event_subtype] == "45" {
        # 存在光驱告警
        mutate {
          replace => { 
            "event_type" => "1000065" 
            "event_subtype" => "1000000284"
          }
        }
      } else if [temp_event_subtype] == "46" {
        # 开放非法端口
        grok {
          match => { "event_details" => "%{WORD:protocol} %{NUMBER:port_number}" }
        }
        mutate {
          replace => { 
            "event_type" => "1000065" 
            "event_subtype" => "1000000213"
          }
        }
      } else if [temp_event_subtype] == "47" {
        # 网口 UP
        grok {
          match => { "event_details" => "%{DATA:network_interface}" }
        }
        mutate {
          replace => { 
            "event_type" => "1000065" 
            "event_subtype" => "1000000214"
          }
        }
      } else if [temp_event_subtype] == "48" {
        # 网口 DOWN
        grok {
          match => { "event_details" => "%{DATA:network_interface}" }
        }
        mutate {
          replace => { 
            "event_type" => "1000065" 
            "event_subtype" => "1000000215"
          }
        }
      }
    }
  }
}