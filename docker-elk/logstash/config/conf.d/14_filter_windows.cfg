filter {
    mutate {
        replace => { "device_type" => "1000033" }
    }
    if [syslog][facility] {
        mutate {
            add_field => { "event_type" => "1000024" }  # Windows主机
        }
        
        if [syslog][facility] in [0, 1, 3, 4, 5] {
            mutate {
                add_field => { "event_subtype" => "1000000085" }  # 系统日志
            }
        } else if [syslog][facility] in [10, 13] {
            mutate {
                add_field => { "event_subtype" => "1000000086" }  # 安全日志
            }
        } else if [syslog][facility] in [2, 9, 11, 7] {
            mutate {
                add_field => { "event_subtype" => "1000000087" }  # 应用程序日志
            }
        } else {
            mutate {
                add_field => { "event_subtype" => "1000000088" }  # 其他
            }
        }
    }
}