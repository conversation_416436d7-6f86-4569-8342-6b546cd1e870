filter {
  ruby {
    path => "/usr/share/logstash/config/conf.d/ruby/event_processor.rb"
    script_params => {
      "patterns" => [
        {
          "regex" => 'SSHS_CONNECT.*SSH\s+user\s+(?<Username>.*)\s+\(IP:\s+(?<srcIp>.*)\)\s+connected\s+to\s+the\s+server\s+successfully.*$'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => ["Username", "srcIp"]
        },
        {
          "regex" => '^(.*?)LOGIN.*?: -\s{0,1}DevIP=(?<srcIp>\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}); (?<Username>\S+?) logged in from (?<DeviceIp>\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}).*$'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => ["srcIp", "Username", "DeviceIp"]
        },
        {
          "regex" => '132>.*?LOGIN.*?: -DevIP=(?<DeviceIp>\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3});   Trap 1\.3\.6\.1\.4\.1\.2011\.10\.2\.2\.1\.1\.3\.0\.1<h3cLogIn>:(?<Username>\S+?) login from.*$'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => ["DeviceIp", "Username"]
        },
        {
          "regex" => 'LOGIN.*?;{0,1}\s+(?<user>\S+)\s+logged\s+in\s+from\s+(?<srcIp>\S+)\..*'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => ["user", "srcIp"]
        },
        {
          "regex" => 'SSHS_LOG.*Authentication\s+failed\s+for\s+user\s+(?<Username>.*)\s+from\s+(?<srcIp>.*)\s+port.*$'
          "event_name" => "登录失败"
          "event_type" => "1000061"
          "event_subtype" => "1000000140"
          "extract_fields" => ["Username", "srcIp"]
        },
        {
          "regex" => '%%.*SSHS_VERSION_MISMATCH.*client\s(?<srcIp>\S+)\s+failed\sto\slog\sin.*$'
          "event_name" => "登录失败"
          "event_type" => "1000061"
          "event_subtype" => "1000000140"
          "extract_fields" => ["srcIp"]
        },
        {
          "regex" => '%%.*SSHS_VERSION_MISMATCH.*client\s(?<srcIp>\S+)\s+failed\sto\slog\sin.*$'
          "event_name" => "登录失败"
          "event_type" => "1000061"
          "event_subtype" => "1000000140"
          "extract_fields" => ["srcIp"]
        },
        {
          "regex" => 'LOGINFAIL.*?: -DevIP=(?<srcIp>\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}); (?<LoginType>\S+?) user (?<Username>\S+?) failed to log in from (?<DeviceIp>\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}) on.*$'
          "event_name" => "登录失败"
          "event_type" => "1000061"
          "event_subtype" => "1000000140"
          "extract_fields" => ["srcIp", "LoginType", "Username", "DeviceIp"]
        },
        {
          "regex" => 'TrapAuthFailed.*?: -DevIP=(?<DeviceIp>\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3});  1\.3\.6\.1\.4\.1\.2011\.10\.2\.22\.1\.3\.0\.1<h3cSSHUserAuthFailure>.*$'
          "event_name" => "登录失败"
          "event_type" => "1000061"
          "event_subtype" => "1000000140"
          "extract_fields" => ["DeviceIp"]
        },
        {
          "regex" => '^ZiYuanChiSW.*SSHS_ALGORITHM_MISMATCH.*client\s(?<srcIp>\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3})\sfailed\sto\slog\sin.*mismatch.*$'
          "event_name" => "登录失败"
          "event_type" => "1000061"
          "event_subtype" => "1000000140"
          "extract_fields" => ["srcIp"]
        },
        {
          "regex" => 'SSHS_DISCONNECT.*SSH\s+user\s+(?<Username>.*)\s+\(IP:\s+(?<srcIp>.*)\)\s+disconnected\s+from\s+the\s+server.*$'
          "event_name" => "退出登录"
          "event_type" => "1000068"
          "event_subtype" => "1000000201"
          "extract_fields" => ["Username", "srcIp"]
        },
        {
          "regex" => '%%.*SSHS_DISCONNECT.*user\s(?<user>.*)\s+\(IP:\s(?<srcIp>\S+)\)\sdisconnected\sfrom.*$'
          "event_name" => "退出登录"
          "event_type" => "1000068"
          "event_subtype" => "1000000201"
          "extract_fields" => ["user", "srcIp"]
        },
        {
          "regex" => 'LOGOUT.*?: -DevIP=(?<DeviceIp>\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3});   Trap 1\.3\.6\.1\.4\.1\.2011\.10\.2\.2\.1\.1\.3\.0\.2<h3cLogOut>:(?<Username>\S+?) logout from.*$'
          "event_name" => "退出登录"
          "event_type" => "1000068"
          "event_subtype" => "1000000201"
          "extract_fields" => ["DeviceIp", "Username"]
        },
        {
          "regex" => 'SHELL_LOGOUT.*?: -DevIP=(?<srcIp>\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}); (?<Username>\S+?) logged out from (?<DeviceIp>\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}).*$'
          "event_name" => "退出登录"
          "event_type" => "1000068"
          "event_subtype" => "1000000201"
          "extract_fields" => ["srcIp", "Username", "DeviceIp"]
        },
        {
          "regex" => '%%\d+SHELL/\d+/SHELL_LOGOUT:\s+TTY\s+logged\s+out\s+from\s+(?<srcIp>\S+)\..*'
          "event_name" => "退出登录"
          "event_type" => "1000068"
          "event_subtype" => "1000000201"
          "extract_fields" => ["srcIp"]
        },
        {
          "regex" => '%%.*SHELL_CMD.*IPAddr=(?<DeviceIp>\S+)-User=(?<Username>\w+)-{0,1}.*;.*Command\si{0,1}s{0,1}\s{0,1}(?<Command>.*)$'
          "event_name" => "配置变更"
          "event_type" => "1000026"
          "event_subtype" => "1000000094"
          "extract_fields" => ["DeviceIp", "Username", "Command"]
        },
        {
          "regex" => 'SHELL_CMD:\s-DevIP=(?<DeviceIp>\S+);.*User=(?<Username>\w+)-{0,1}.*;.*Command\si{0,1}s{0,1}\s{0,1}(?<Command>.*)$'
          "event_name" => "配置变更"
          "event_type" => "1000026"
          "event_subtype" => "1000000094"
          "extract_fields" => ["DeviceIp", "Username", "Command"]
        },
        {
          "regex" => '%%.*UPDOWN:\s-DevIP=(?<devIp>\S+);.*interface\s(?<interface>\S+)\schanged\sto\sdown.*$'
          "event_name" => "网口拔出"
          "event_type" => "1000065"
          "event_subtype" => "1000000215"
          "extract_fields" => ["devIp", "interface"]
        },
        {
          "regex" => 'PHY_UPDOWN:.*Physical\s+state\s+on\s+the\s+interface\s*(?<interface>\S+?)\s+changed\s+to\s+(down|DOWN).*?$'
          "event_name" => "网口拔出"
          "event_type" => "1000065"
          "event_subtype" => "1000000215"
          "extract_fields" => ["interface"]
        },
        {
          "regex" => '%%.*UPDOWN:\s-DevIP=(?<devIp>\S+);.*interface\s(?<interface>\S+)\schanged\sto\sup.*$'
          "event_name" => "网口接入"
          "event_type" => "1000065"
          "event_subtype" => "1000000214"
          "extract_fields" => ["devIp", "interface"]
        },
        {
          "regex" => 'PHY_UPDOWN:.*Physical\s+state\s+on\s+the\s+interface\s*(?<interface>\S+?)\s+changed\s+to\s+(up|UP).*?$'
          "event_name" => "网口接入"
          "event_type" => "1000065"
          "event_subtype" => "1000000214"
          "extract_fields" => ["interface"]
        }
      ]
    }
  }

  # 添加通用字段
  mutate {
    replace => {
      "device_type" => "1000079"
      "product_name" => "华三-S7506E"
    }
  }

  # 保存提取字段
  mutate {
    rename => {
      "Command" => "action_details"
      "device" => "device_name"
      "srcIp" => "source_address"
      "DeviceIp" => "device_name"
      "interface" => "action_object_name"
      "user" => "related_object_name"
      "Username" => "related_object_name"
    }
  }
}
