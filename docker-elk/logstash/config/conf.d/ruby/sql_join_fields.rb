
def register(params)
    @separator = params["separator"] || "|||"
end
  
def filter(event)
    begin
        info_fields = event.get("sql_feilds")
        if info_fields
            keys = info_fields.keys.sort
            sorted_values = keys.map { |key| info_fields[key] }
            joined_value = sorted_values.join(@separator)
            event.set("message", joined_value)
        end
    rescue => e
        # 错误处理
        event.tag("_rubyexception")
        event.set("ruby_exception", e.message)
    end

    # 返回包含修改后事件的数组
    return [event]
end