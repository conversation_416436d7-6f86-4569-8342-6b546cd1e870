def register(params)
    @patterns = params["patterns"]
  end
  
def filter(event)
message = event.get("message")
@patterns.each do |pattern|
    regex = Regexp.new(pattern["regex"])
    if message =~ regex
    match_data = message.match(regex)
    pattern["extract_fields"].each do |field|
        event.set(field, match_data[field])
    end
    event.set("event_name", pattern["event_name"])
    event.set("event_type", pattern["event_type"])
    event.set("event_subtype", pattern["event_subtype"])
    break
    end
end
return [event]
end