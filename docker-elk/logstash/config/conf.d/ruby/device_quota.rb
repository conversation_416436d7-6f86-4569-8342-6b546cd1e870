require 'logger'

# 设备配额管理类
class DeviceQuotaManager
    def initialize(es_host, max_devices, es_user, es_password, cache_ttl=3600, logger)
        require 'elasticsearch'
        require 'concurrent'
        
        @es_client = Elasticsearch::Client.new(
          hosts: [es_host],
          user: es_user,
          password: es_password,
          log: false
        )
        @max_devices = max_devices
        @cache_ttl = cache_ttl
        @device_cache = Concurrent::Map.new
        @device_count = Concurrent::AtomicFixnum.new(0)
        @logger = logger || Logger.new(STDOUT)
        
        # 确保索引存在
        create_registry_index unless @es_client.indices.exists?(index: 'device_registry')
        
        # 初始化设备计数
        update_device_count
    end
    
    def can_ingest?(device_id)
      return true if device_registered?(device_id)
      @logger.info("Device not registered: #{device_id}")
      register_device(device_id)
    end
    
    private
    
    def device_registered?(device_id)
      # 检查缓存
      cache_entry = @device_cache[device_id]
      if cache_entry && !cache_expired?(cache_entry)
        update_cache(device_id)
        return true
      end
      
      # 查询ES
      @logger.info("Query ES for device: #{device_id}")
      result = @es_client.search(
        index: 'device_registry',
        body: {
          query: {
            term: { device_id: device_id }
          }
        },
        _source: false
      )
      
      if result['hits']['total']['value'] > 0
        update_cache(device_id)
        true
      else
        false
      end
    rescue => e
      # ES查询失败时，使用缓存数据
      cache_entry ? true : false
    end
    
    def register_device(device_id)
      update_device_count
      return false if @device_count.value >= @max_devices
      @logger.info("Registering device: #{device_id}")
      begin
        @es_client.create(
          index: 'device_registry',
          id: device_id,
          body: {
            device_id: device_id,
            registration_time: Time.now.utc.iso8601
          }
        )
        
        update_cache(device_id)
        @logger.info("Device registered: #{device_id}")
        true
      rescue => e
        @logger.info("Failed to register device", :device_id => device_id, :error => e)
        true
      end
    end
    
    def update_cache(device_id)
      @device_cache[device_id] = Time.now.to_i
    end
    
    def cache_expired?(timestamp)
      Time.now.to_i - timestamp > @cache_ttl
    end
    
    def update_device_count
      count = @es_client.count(index: 'device_registry')['count']
      @device_count.value = count
    rescue => e
      @logger.error("Failed to update device count", :error => e)
    end
    
    def create_registry_index
      @es_client.indices.create(
        index: 'device_registry',
        body: {
          mappings: {
            properties: {
              device_id: { type: 'keyword' },
              registration_time: { type: 'date' }
            }
          }
        }
      )
    rescue => e
      # 忽略索引已存在的错误
    end
  end
  
  # 全局变量存储管理器实例
  $device_quota_manager = nil
  
  # 初始化函数
  def register(params)
    $device_quota_manager = DeviceQuotaManager.new(
      params["es_host"],
      params["max_devices"].to_i,
      params["es_user"],
      params["es_password"],
      params["cache_ttl"].to_i,
      logger
    )
  end
  
  # 过滤函数
  def filter(event)
    device_id = event.get("device_address")
    # 打印日志
    # logger.info("Device ID: #{device_id}")
    if device_id
      unless $device_quota_manager.can_ingest?(device_id)
        logger.info("Device quota exceeded for device ID: #{device_id}")
        event.tag("device_quota_exceeded")
        return []
      end
    end
    
    return [event]
  end