filter {
  ruby {
    path => "/usr/share/logstash/config/conf.d/ruby/event_processor.rb"
    script_params => {
      "patterns" => [
        {
          "regex" => 'LOGIN.*DevIP=.*;\s+(?<user>\S+)\s+logged\s+in\s+from\s+(?<srcIp>\S+).*?$'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => ["user", "srcIp"]
        },
        {
          "regex" => 'LOGIN.********.4.1.25506.*******.3.0.1.*:(?<Username>.*)\s+login\s+from\s+VTY.*$'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => ["Username"]
        },
        {
          "regex" => '^(?<time>).*?;\s+(?<user>\S+)\s+login\s+from\s+(?<srcIp>\S+).*?$'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => ["user", "srcIp"]
        },
        {
          "regex" => 'SHELL.*LOGIN\(l\).*-DevIP=\S+\s+(?<user>\S+)\s+login\s+from\s+(?<srcIp>\S+).*?$'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => ["user", "srcIp"]
        },
        {
          "regex" => 'LOGIN\(l\).*-\s+(?<user>\S+)\((?<srcIp>\S+)\).*login.*$'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => ["user", "srcIp"]
        },
        {
          "regex" => '\s+SSH\s+user\s+(?<Username>\S+)\s+\(IP:(?<srcIp>\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3})\)\s+success\s+to\s+process\s+PASSWORD\s+Authentication.*$'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => ["Username", "srcIp"]
        },
        {
          "regex" => 'LOGIN.*DevIP=.*\s+Console\s+login\s+from\s+(?<srcIp>\S+)$'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => ["srcIp"]
        },
        {
          "regex" => 'SSH.*LOGIN\(l\).*user\s+(?<user>\S+)\s+\(IP:(?<srcIp>.*)\)\s+login\s+succeeded.*$'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => ["user", "srcIp"]
        },
        {
          "regex" => 'SSH.*LOGIN\(l\).*user\s+(?<user>\S+)\s+\(IP:(?<srcIp>.*)\)\s+login\s+succeeded.*$'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => ["user", "srcIp"]
        },
        {
          "regex" => 'ADDBLACKLIST.*:\s+User\s+(?<Username>\S+)\s+was\s+added\s+to\s+the\s+blacklist\s+for\s+failed\s+login\s+attempts.*$'
          "event_name" => "登录失败"
          "event_type" => "1000061"
          "event_subtype" => "1000000140"
          "extract_fields" => ["Username"]
        },
        {
          "regex" => 'SSH.*AUTHFAIL.*IP:(?<srcIp>\S+)\)\s+login\s+failed.*$'
          "event_name" => "登录失败"
          "event_type" => "1000061"
          "event_subtype" => "1000000140"
          "extract_fields" => ["srcIp"]
        },
        {
          "regex" => 'LOGINFAIL.*DevIP=(?<Devip>\S+)\s+AUX\s+user\s+failed\s+to\s+login\s+on.*$'
          "event_name" => "登录失败"
          "event_type" => "1000061"
          "event_subtype" => "1000000140"
          "extract_fields" => ["Devip"]
        },
        {
          "regex" => 'SHELL.*LOGINFAIL\(l\).*-DevIP=\S+\s+SSH\s+user\s+(?<user>\S+)\s+failed\s+to\s+login\s+from\s+(?<srcIp>\S+).*$'
          "event_name" => "登录失败"
          "event_type" => "1000061"
          "event_subtype" => "1000000140"
          "extract_fields" => ["user", "srcIp"]
        },
        {
          "regex" => 'FAILURE.*DevIP=.*-.*login-UserName=(?<user>\S+);\s+Authentication\s+is\s+failed.*$'
          "event_name" => "登录失败"
          "event_type" => "1000061"
          "event_subtype" => "1000000140"
          "extract_fields" => ["user"]
        },
        {
          "regex" => 'LOGOUT.*DevIP=.*;\s+(?<user>\S+)\s+logged\s+out\s+from\s+(?<srcIP>\S+)..*$'
          "event_name" => "退出登录"
          "event_type" => "1000068"
          "event_subtype" => "1000000201"
          "extract_fields" => ["user", "srcIP"]
        },
        {
          "regex" => 'SHELL.*LOGOUT\(l\).*-DevIP=\S+\s+(?<user>\S+)\s+logout\s+from\s+(?<srcIp>.*).*$'
          "event_name" => "退出登录"
          "event_type" => "1000068"
          "event_subtype" => "1000000201"
          "extract_fields" => ["user", "srcIp"]
        },
        {
          "regex" => '\/LOGOUT.*DevIP=.*\s+Console\s+logout\s+from\s+(?<srcIp>\S+).*?$'
          "event_name" => "退出登录"
          "event_type" => "1000068"
          "event_subtype" => "1000000201"
          "extract_fields" => ["srcIp"]
        },
        {
          "regex" => 'LOGOUT.*;\s+(?<user>\S+)\s+logout\s+from\s+(?<srcIp>\S+).*$'
          "event_name" => "退出登录"
          "event_type" => "1000068"
          "event_subtype" => "1000000201"
          "extract_fields" => ["user", "srcIp"]
        },
        {
          "regex" => 'LOGOUT\(l\).*-\s+(?<user>\S+)\((?<srcIp>\S+)\).*logout.*$'
          "event_name" => "退出登录"
          "event_type" => "1000068"
          "event_subtype" => "1000000201"
          "extract_fields" => ["user", "srcIp"]
        },
        {
          "regex" => ';\s+STEL\s+user\s+\(IP:(?<srcip>\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3})\)\s+logout\s+\S+\s+:SSH\s+Client\s+close.*$'
          "event_name" => "退出登录"
          "event_type" => "1000068"
          "event_subtype" => "1000000201"
          "extract_fields" => ["srcip"]
        },
        {
          "regex" => 'SSH.*LOGOUT\(l\).*user\s+(?<user>\S+)\s+\(IP:(?<srcIp>.*)\)\s+logout.*$'
          "event_name" => "退出登录"
          "event_type" => "1000068"
          "event_subtype" => "1000000201"
          "extract_fields" => ["user", "srcIp"]
        },
        {
          "regex" => 'LOGOUT.********.4.1.25506.*******.3.0.2.*:(?<Username>.*)\s+logout\s+from\s+VTY.*$'
          "event_name" => "退出登录"
          "event_type" => "1000068"
          "event_subtype" => "1000000201"
          "extract_fields" => ["Username"]
        },
        {
          "regex" => '%%10IFNET/4/INTERFACE UPDOWN\(t\):-DevIP=(?<DevIP>\S+);   Trap 1\.3\.6\.1\.6\.3\.1\.1\.5\.3<linkDown>: Interface (?<InterfaceID>\d+) is Down, ifAdminStatus is \d+, ifOperStatus is \d+$'
          "event_name" => "网口拔出"
          "event_type" => "1000065"
          "event_subtype" => "1000000215"
          "extract_fields" => ["DevIP", "InterfaceID"]
        },
        {
          "regex" => 'UPDOWN.*Interface\s+(?<interface>.*),\s+changed\s+state\s+to\s+down.*$'
          "event_name" => "网口拔出"
          "event_type" => "1000065"
          "event_subtype" => "1000000215"
          "extract_fields" => ["interface"]
        },
        {
          "regex" => 'LINK\s+UPDOWN\(l\).*;{0,1}\s+(?<interface>\S+\d+):{0,1}\s+link\s+status\s+is\s+DOWN.*$'
          "event_name" => "网口拔出"
          "event_type" => "1000065"
          "event_subtype" => "1000000215"
          "extract_fields" => ["interface"]
        },
        {
          "regex" => '%%.*LINK_UPDOWN.*:\s+(?<interface>\S+)\s+link\s+status\s+is\s+DOWN.*$'
          "event_name" => "网口拔出"
          "event_type" => "1000065"
          "event_subtype" => "1000000215"
          "extract_fields" => ["interface"]
        },
        {
          "regex" => '%%10IFNET/4/INTERFACE\s+UPDOWN\(t\):\-DevIP=(?<DevIP>\d+\.\d+\.\d+\.\d+);.*Trap\s+1\.3\.6\.1\.6\.3\.1\.1\.5\.4<linkUp>:\s+Interface\s+\d+\s+is\s+Up,.*?$'
          "event_name" => "网口接入"
          "event_type" => "1000065"
          "event_subtype" => "1000000214"
          "extract_fields" => ["DevIP"]
        },
        {
          "regex" => 'UPDOWN.*Interface\s+(?<interface>.*),\s+changed\s+state\s+to\s+up.*$'
          "event_name" => "网口接入"
          "event_type" => "1000065"
          "event_subtype" => "1000000214"
          "extract_fields" => ["interface"]
        },
        {
          "regex" => 'LINK\s+UPDOWN\(l\).*;{0,1}\s+(?<interface>\S+\d+):{0,1}\s+link\s+status\s+is\s+UP.*$'
          "event_name" => "网口接入"
          "event_type" => "1000065"
          "event_subtype" => "1000000214"
          "extract_fields" => ["interface"]
        },
        {
          "regex" => '%%.*LINK_UPDOWN.*:\s+(?<interface>\S+)\s+link\s+status\s+is\s+UP.*$'
          "event_name" => "网口接入"
          "event_type" => "1000065"
          "event_subtype" => "1000000214"
          "extract_fields" => ["interface"]
        }
      ]
    }
  }

  # 添加通用字段
  mutate {
    replace => {
      "device_type" => "1000079"
      "product_name" => "华三-3610"
    }
  }

  # 保存提取字段
  mutate {
    rename => {
      "srcip" => "source_address"
      "InterfaceID" => "action_object_name"
      "srcIp" => "source_address"
      "DevIP" => "device_name"
      "Devip" => "device_name"
      "interface" => "action_object_name"
      "user" => "related_object_name"
      "Username" => "related_object_name"
    }
  }
}
