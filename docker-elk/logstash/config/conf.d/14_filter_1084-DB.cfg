filter {
    grok {
        match => {
            "message" => [
                "DB %{INT:temp_event_type} %{INT:temp_event_subtype} %{GREEDYDATA:event_details}$"
            ]
        }
    }
    if [hostname] {
        mutate {
            replace => {
                device_name => "%{hostname}"
            }
        }
    }
    mutate {
        replace => {
            "device_type" => "1000042"
        }
    }
    if [temp_event_type] == "0" {
        mutate {
            replace => { "event_type" => "1000069" }
        }
        if [temp_event_subtype] == "1" {
            # 数据库 CPU 利用率
            grok {
                match => {
                    "event_details" => "%{DATA:product_version} %{NUMBER:cpu_usage}%%"
                }
            }
        } else if [temp_event_subtype] == "2" {
            # 数据库内存利用率
            grok {
                match => {
                    "event_details" => "%{DATA:product_version} %{NUMBER:memory_usage}%%"
                }
            }
        } else if [temp_event_subtype] == "3" {
            # 数据库磁盘信息-数据文件
            grok {
                match => {
                    "event_details" => "%{DATA:product_version} (?:%{DATA:disk_info})+"
                }
            }
        } else if [temp_event_subtype] == "4" {
            # 数据库磁盘信息-归档文件
            grok {
                match => {
                    "event_details" => "%{DATA:product_version} (?:%{DATA:archive_disk_info})+"
                }
            }
        } else if [temp_event_subtype] == "5" {
            # 数据库磁盘信息-备份文件
            grok {
                match => {
                    "event_details" => "%{DATA:product_version} (?:%{DATA:backup_disk_info})+"
                }
            }
        } else if [temp_event_subtype] == "6" {
            # 数据库表空间使用情况
            grok {
                match => {
                    "event_details" => "%{DATA:product_version} %{WORD:database_name} %{DATA:tablespace_size} %{DATA:tablespace_used} %{NUMBER:tablespace_usage}%%"
                }
            }
        } else if [temp_event_subtype] == "7" {
            # 数据库连接使用情况
            grok {
                match => {
                    "event_details" => "%{DATA:product_version} %{NUMBER:max_connections} %{NUMBER:current_connections} (?:%{DATA:connection_info})+"
                }
            }
        } else if [temp_event_subtype] == "8" {
            # 数据库运行时长
            grok {
                match => {
                    "event_details" => "%{DATA:product_version} %{IP:database_ip} %{TIMESTAMP_ISO8601:start_time} %{GREEDYDATA:runtime_description}"
                }
            }
        } else if [temp_event_subtype] == "9" {
            # 数据库状态
            grok {
                match => {
                    "event_details" => "%{DATA:product_version} %{IP:database_ip} %{NUMBER:status} %{GREEDYDATA:status_description}"
                }
            }
        } else if [temp_event_subtype] == "10" {
            # 数据库操作记录
            grok {
                match => {
                    "event_details" => "%{DATA:product_version} %{IP:related_object_address} %{WORD:database} %{WORD:operation_type} %{WORD:object_type} %{WORD:affected_object} %{GREEDYDATA:details}"
                }
            }
        } else if [temp_event_subtype] == "11" {
            # 数据库用户信息变更
            grok {
                match => {
                    "event_details" => "%{DATA:product_version} %{IP:related_object_address} %{WORD:database} %{WORD:related_object_name} %{WORD:operation_type} %{GREEDYDATA:details}"
                }
            }
        }
    } else if [temp_event_type] == "1" {
        mutate {
            replace => { "event_type" => "1000070" }
        }
        if [temp_event_subtype] == "1" {
            # 数据库用户登录失败
            grok {
                match => {
                    "event_details" => "%{DATA:product_version} %{DATA:related_object_name} %{IP:related_object_address} %{GREEDYDATA:login_app}"
                }
            }
        } else if [temp_event_subtype] == "2" {
            # 锁表-sql 语句长时间未提交
            grok {
                match => {
                    "event_details" => "%{DATA:product_version} %{DATA:related_object_name} %{IP:related_object_address} %{WORD:app_name} %{DATA:connection_id} %{DATA:sql_id}"
                }
            }
        } else if [temp_event_subtype] == "3" {
            # 数据计划任务执行失败
            grok {
                match => {
                    "event_details" => "%{DATA:product_version} %{DATA:task_name} %{GREEDYDATA:details}"
                }
            }
        } else if [temp_event_subtype] == "4" {
            # 锁表-sql 语句执行时间异常
            grok {
                match => {
                    "event_details" => "%{DATA:product_version} %{DATA:related_object_name} %{IP:related_object_address} %{WORD:app_name} %{DATA:connection_id} %{DATA:sql_id} %{GREEDYDATA:sql_statement}"
                }
            }
        } else if [temp_event_subtype] == "5" {
            # 锁表-数据库脏页面占比情况
            grok {
                match => {
                    "event_details" => "%{DATA:product_version} %{WORD:database_name} %{NUMBER:total_pages} %{NUMBER:dirty_pages} %{NUMBER:dirty_pages_percentage}%%"
                }
            }
        }
    }
}