filter {
    prune {
        whitelist_names => [
            "^security_object_id$",
            "^security_object_ip$",
            "^standard_event_code$",
            "^message$",
            "^event_name$",
            "^credibility$",
            "^event_type$",
            "^event_subtype$",
            "^severity_level$",
            "^original_level$",
            "^device_type$",
            "^device_address$",
            "^device_name$",
            "^product_name$",
            "^product_version$",
            "^received_time$",
            "^source_address$",
            "^source_hostname$",
            "^source_port$",
            "^source_mask$",
            "^source_user$",
            "^source_mac$",
            "^source_zone$",
            "^source_os$",
            "^source_browser$",
            "^source_isp$",
            "^dest_address$",
            "^dest_hostname$",
            "^dest_port$",
            "^dest_user$",
            "^dest_mask$",
            "^dest_mac$",
            "^dest_zone$",
            "^dest_isp$",
            "^collector_id$",
            "^collector_name$",
            "^collector_address$",
            "^sent_traffic$",
            "^sent_traffic_unit$",
            "^received_traffic$",
            "^received_traffic_unit$",
            "^total_traffic$",
            "^total_traffic_unit$",
            "^related_object_category$",
            "^related_object_subcategory$",
            "^related_object_name$",
            "^related_object_address$",
            "^related_object_account$",
            "^action_account$",
            "^action_address$",
            "^action_object_type$",
            "^action_object_name$",
            "^action_object_address$",
            "^action_details$",
            "^action_duration$",
            "^source_country_code$",
            "^source_ip$",
            "^source_longitude$",
            "^source_city$",
            "^source_province$",
            "^source_country$",
            "^source_latitude$",
            "^dest_country_code$",
            "^dest_ip$",
            "^dest_longitude$",
            "^dest_city$",
            "^dest_province$",
            "^dest_country$",
            "^dest_latitude$",
            "^source_org$",
            "^source_org_location$",
            "^dest_org$",
            "^dest_org_location$",
            "^domain$",
            "^group_name$",
            "^network_service$",
            "^nat_ip$",
            "^other_port$",
            "^protocol$",
            "^action$",
            "^action_result$",
            "^related_filename$",
            "^session_id$",
            "^result_code$",
            "^url$",
            "^referrer$",
            "^referrer_domain$",
            "^rule_type$",
            "^application_protocol$",
            "^@timestamp$",
            "^day_time$"
        ]
    }
}