filter {
  ruby {
    path => "/usr/share/logstash/config/conf.d/ruby/event_processor.rb"
    script_params => {
      "patterns" => [
        {
          "regex" => '^FID\s0\s5\s(?<user>\S+)\s.*$'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => ["user"]
        },
        {
          "regex" => 'FID 0 6\s(?<Info>.+$)'
          "event_name" => "配置变更"
          "event_type" => "1000026"
          "event_subtype" => "1000000094"
          "extract_fields" => []
        },
        {
          "regex" => 'FID\s2\s1\s\S+\s(?<srcIp>\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})\s(?<srcPort>\S+)\s(?<dstIp>\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})\s(?<dstPort>\S+).*$'
          "event_name" => "不符合安全策略访问"
          "event_type" => "1000065"
          "event_subtype" => "1000000241"
          "extract_fields" => ["srcIp", "srcPort", "dstIp", "dstPort"]
        },
        {
          "regex" => '%%\d+SYSTATE/\d+/.*:\s+Mgmtplane\s+CPU\s+Usage=(?<MgmtCPU>\d+%)\sDataplane\sCPU\s+Usage=(?<DataCPU>\d+%)\s+Memory\s+Usage=(?<MemUsg>\d+%).*$'
          "event_name" => "性能监控"
          "event_type" => "1000025"
          "event_subtype" => "1000000089"
          "extract_fields" => ["MgmtCPU", "DataCPU", "MemUsg"]
        },
        {
          "regex" => 'FID 0 3\s(?<MgmtCPU>\d+(.\d+)?%)'
          "event_name" => "性能监控"
          "event_type" => "1000025"
          "event_subtype" => "1000000089"
          "extract_fields" => ["MgmtCPU"]
        },
        {
          "regex" => 'FID 0 4\s(?<MemUsg>\S+%)'
          "event_name" => "性能监控"
          "event_type" => "1000025"
          "event_subtype" => "1000000089"
          "extract_fields" => ["MemUsg"]
        },
        {
          "regex" => '%%\d+SYSTATE/\d+/.*:\s+Mgmtplane\s+CPU\s+Usage=(?<MgmtCPU>\d+%)\sDataplane\sCPU\s+Usage=(?<DataCPU>\d+%)\s+Memory\s+Usage=(?<MemUsg>\d+%).*$'
          "event_name" => "性能监控"
          "event_type" => "1000025"
          "event_subtype" => "1000000089"
          "extract_fields" => ["MgmtCPU", "DataCPU", "MemUsg"]
        }
      ]
    }
  }

  # 添加通用字段
  mutate {
    replace => {
      "device_type" => "1000076"
      "product_name" => "珠海鸿瑞-HRWALL-85MII"
    }
  }

  # 保存提取字段
  mutate {
    rename => {
      "dstIp" => "dest_address"
      "dstPort" => "dest_port"
      "srcIp" => "source_address"
      "user" => "related_object_name"
      "srcPort" => "source_port"
    }
  }
}
