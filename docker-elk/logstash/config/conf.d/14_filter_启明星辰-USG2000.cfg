filter {
  ruby {
    path => "/usr/share/logstash/config/conf.d/ruby/event_processor.rb"
    script_params => {
      "patterns" => [
        {
          "regex" => '-DevIP=\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3}.*user:(?<Username>\S+)\slogin from (?<DeviceIp>\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3})$'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => ["Username", "DeviceIp"]
        },
        {
          "regex" => '-登录成功$'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => []
        },
        {
          "regex" => '-DevIP=\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}.*user:(?<Username>\S+)\slogin from (?<DeviceIp>\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}) failed'
          "event_name" => "登录失败"
          "event_type" => "1000061"
          "event_subtype" => "1000000140"
          "extract_fields" => ["Username", "DeviceIp"]
        },
        {
          "regex" => '-DevIP=(?<DeviceIp>\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3}).*user:(?<Username>\S+)\slogin from con\d+ failed'
          "event_name" => "登录失败"
          "event_type" => "1000061"
          "event_subtype" => "1000000140"
          "extract_fields" => ["DeviceIp", "Username"]
        },
        {
          "regex" => '^<\d+>.*-DevIP=\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}.*user:(?<Username>\S+)\slogout from (?<DeviceIp>\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})$'
          "event_name" => "退出登录"
          "event_type" => "1000068"
          "event_subtype" => "1000000201"
          "extract_fields" => ["Username", "DeviceIp"]
        },
        {
          "regex" => '^<\d+>.*-DevIP=(?<DeviceIp>\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3}).*user:(?<Username>\S+)\slogout from con\d+$'
          "event_name" => "退出登录"
          "event_type" => "1000068"
          "event_subtype" => "1000000201"
          "extract_fields" => ["DeviceIp", "Username"]
        },
        {
          "regex" => '^<\d+>.*-DevIP=(?<DeviceIp>\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3}).*user:(?<Username>\S+).*command:(?<Command>.*)'
          "event_name" => "配置变更"
          "event_type" => "1000026"
          "event_subtype" => "1000000094"
          "extract_fields" => ["DeviceIp", "Username", "Command"]
        },
        {
          "regex" => 'SrcIP=(?<srcIp>.*)\s+DstIP=(?<dstIp>.*)\s+\S+\s+SrcPort=(?<srcPort>.*)\s+DstPort=(?<dstPort>\d+).*Action=DENY.*\s+policy\s+is\s+deny.*$'
          "event_name" => "不符合安全策略访问"
          "event_type" => "1000065"
          "event_subtype" => "1000000241"
          "extract_fields" => ["srcIp", "dstIp", "srcPort", "dstPort"]
        },
        {
          "regex" => '(?<src>\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3})->(?<dst>\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3})'
          "event_name" => "不符合安全策略访问"
          "event_type" => "1000065"
          "event_subtype" => "1000000241"
          "extract_fields" => ["src", "dst"]
        }
      ]
    }
  }

  # 添加通用字段
  mutate {
    replace => {
      "device_type" => "1000035"
      "product_name" => "启明星辰-USG2000"
    }
  }

  # 保存提取字段
  mutate {
    rename => {
      "dstIp" => "dest_address"
      "Command" => "action_details"
      "dstPort" => "dest_port"
      "srcIp" => "source_address"
      "DeviceIp" => "device_name"
      "srcPort" => "source_port"
      "src" => "source_address"
      "Username" => "related_object_name"
    }
  }
}
