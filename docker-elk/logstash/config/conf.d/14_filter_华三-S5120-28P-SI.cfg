filter {
  ruby {
    path => "/usr/share/logstash/config/conf.d/ruby/event_processor.rb"
    script_params => {
      "patterns" => [
        {
          "regex" => 'LOGIN.*?Trap 1\.3\.6\.1\.4\.1\.25506\.2\.2\.1\.1\.3\.0\.1<hh3cLogIn>:(?<Username>\S+?) login from.*$'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => ["Username"]
        },
        {
          "regex" => 'LOGIN.*?:\s+(?<Username>\S+?)\slog.*from (?<DeviceIp>\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}).*$'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => ["Username", "DeviceIp"]
        },
        {
          "regex" => 'LOGIN.*?Trap 1\.3\.6\.1\.4\.1\.25506\.2\.2\.1\.1\.3\.0\.1<hh3cLogIn>:(?<Username>\S+?) login from.*$'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => ["Username"]
        },
        {
          "regex" => 'LOGIN.*?:\s+(?<Username>\S+?)\slog.*from (?<DeviceIp>\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}).*$'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => ["Username", "DeviceIp"]
        },
        {
          "regex" => 'LOGOUT.*?:\s+(?<Username>\S+?)\slog.*out\sfrom\s(?<DeviceIp>\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}).*$'
          "event_name" => "退出登录"
          "event_type" => "1000068"
          "event_subtype" => "1000000201"
          "extract_fields" => ["Username", "DeviceIp"]
        },
        {
          "regex" => 'LOGOUT.*?:\s+Trap\s1\.3\.6\.1\.4\.1\.25506\.2\.2\.1\.1\.3\.0\.2<hh3cLogOut>:(?<Username>\S+?)\s+logout\sfrom\s.*$'
          "event_name" => "退出登录"
          "event_type" => "1000068"
          "event_subtype" => "1000000201"
          "extract_fields" => ["Username"]
        },
        {
          "regex" => 'LOGOUT.*?:\s+(?<Username>\S+?)\slog.*out\sfrom (?<DeviceIp>\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}).*$'
          "event_name" => "退出登录"
          "event_type" => "1000068"
          "event_subtype" => "1000000201"
          "extract_fields" => ["Username", "DeviceIp"]
        }
      ]
    }
  }

  # 添加通用字段
  mutate {
    replace => {
      "device_type" => "1000079"
      "product_name" => "华三-S5120-28P-SI"
    }
  }

  # 保存提取字段
  mutate {
    rename => {
      "DeviceIp" => "device_name"
      "Username" => "related_object_name"
    }
  }
}
