filter {
  ruby {
    path => "/usr/share/logstash/config/conf.d/ruby/event_processor.rb"
    script_params => {
      "patterns" => [
        {
          "regex" => '日志类型:.*用户:(?<UserName>\S+)\((?<Type>\S+)\),\s+IP地址:(?<SourceIP>\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3}),\s+.*操作.*:.*登录,\s+.*登录成功.*?$'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => ["UserName", "Type", "SourceIP"]
        },
        {
          "regex" => '日志类型:.*\s+用户:(?<UserName>\S+)\((?<Type>\S+)\),\s+IP地址:(?<SourceIP>\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}),\s+.*操作.*:.*登录,\s+.*登录失败.*?$'
          "event_name" => "登录失败"
          "event_type" => "1000061"
          "event_subtype" => "1000000140"
          "extract_fields" => ["UserName", "Type", "SourceIP"]
        },
        {
          "regex" => '日志类型:.*\s+用户:(?<UserName>\S+),.*IP地址:(?<SourceIP>\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3}),\s+.*操作对象:注销,\s+登录时间.*登录时长.*?$'
          "event_name" => "退出登录"
          "event_type" => "1000068"
          "event_subtype" => "1000000201"
          "extract_fields" => ["UserName", "SourceIP"]
        },
        {
          "regex" => '日志类型:.*\s+用户:(?<UserName>\S+)\((?<Type>\S+)\),\s+IP地址:(?<SourceIP>\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3}),\s+.*操作.*:.*注销,\s+.*注销成功.*?$'
          "event_name" => "退出登录"
          "event_type" => "1000068"
          "event_subtype" => "1000000201"
          "extract_fields" => ["UserName", "Type", "SourceIP"]
        },
        {
          "regex" => '日志类型:系统操作,\s+.*IP地址:(?<srcIp>\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3}),\s+.*操作类型:.*?,\s+描述:(?<Command>.*)：成功.*$'
          "event_name" => "配置变更"
          "event_type" => "1000026"
          "event_subtype" => "1000000094"
          "extract_fields" => ["srcIp", "Command"]
        },
        {
          "regex" => '日志类型:WAF应用防护日志,\s*源IP:(?<srcIp>\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3}),\s*源端口:(?<srcPort>\d+?),\s*目的IP:(?<dstIp>\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3}),\s*目的端口:(?<dstPort>\d+?),\s*攻击类型:(?<attacktype>.*?),\s*严重级别:(?<securitylevel>.*?),\s*系统动作:拒绝,\sURL:(?<url>.*?)$'
          "event_name" => "APT"
          "event_type" => "1000014"
          "event_subtype" => "1000000030"
          "extract_fields" => ["srcIp", "srcPort", "dstIp", "dstPort", "attacktype", "securitylevel", "url"]
        },
        {
          "regex" => '日志类型:.*?,\s{1,2}策略名称:(?<policyname>.*?),\s+.*源IP:(?<srcIp>\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3}),\s源端口:(?<srcPort>\d+?),\s目的IP:(?<dstIp>\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3}),\s目的端口:(?<dstPort>\d+?),\s+.*应用名称:.*?,\s+系统动作:拒绝$'
          "event_name" => "不符合安全策略访问"
          "event_type" => "1000065"
          "event_subtype" => "1000000241"
          "extract_fields" => ["policyname", "srcIp", "srcPort", "dstIp", "dstPort"]
        },
        {
          "regex" => '日志类型:.*?,\s{1,2}策略名称:(?<policyname>.*?),\s+.*源IP:(?<srcIp>\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3}),\s源端口:(?<srcPort>\d+?),\s目的IP:(?<dstIp>\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3}),\s目的端口:(?<dstPort>\d+?),\s+.*应用名称:.*?,\s+系统动作:允许$'
          "event_name" => "符合安全策略访问"
          "event_type" => "1000065"
          "event_subtype" => "1000000241"
          "extract_fields" => ["policyname", "srcIp", "srcPort", "dstIp", "dstPort"]
        }
      ]
    }
  }

  # 添加通用字段
  mutate {
    replace => {
      "device_type" => "1000035"
      "product_name" => "深信服-AF-1000"
    }
  }

  # 保存提取字段
  mutate {
    rename => {
      "dstIp" => "dest_address"
      "SourceIP" => "source_address"
      "url" => "url"
      "Command" => "action_details"
      "dstPort" => "dest_port"
      "srcIp" => "source_address"
      "policyname" => "action_details"
      "srcPort" => "source_port"
      "UserName" => "related_object_name"
    }
  }
}
