filter {
  ruby {
    path => "/usr/share/logstash/config/conf.d/ruby/event_processor.rb"
    script_params => {
      "patterns" => [
        {
          "regex" => 'SSH_FAIL.*Failed\s+to\s+login\s+through.*IP=(?<srcIp>\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3}),.*UserName=(?<user>\S+),.*$'
          "event_name" => "登录失败"
          "event_type" => "1000061"
          "event_subtype" => "1000000140"
          "extract_fields" => ["srcIp", "user"]
        },
        {
          "regex" => 'CMDRECORD.*:.*\s+command\s+information.*IP=(?<srcIp>\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3}).*User=(?<user>\S+),.*Command="(?<Command>.*)".*$'
          "event_name" => "配置变更"
          "event_type" => "1000026"
          "event_subtype" => "1000000094"
          "extract_fields" => ["srcIp", "user", "Command"]
        },
        {
          "regex" => 'IF_STATE.*?Interface\s(?<interface>\S+?)\shas\sturned\sinto\sDOWN\sstate.*$'
          "event_name" => "网口拔出"
          "event_type" => "1000065"
          "event_subtype" => "1000000215"
          "extract_fields" => ["interface"]
        },
        {
          "regex" => 'IF_STATE.*?Interface\s(?<interface>\S+?)\shas\sturned\sinto\sUP\sstate.*$'
          "event_name" => "网口接入"
          "event_type" => "1000065"
          "event_subtype" => "1000000214"
          "extract_fields" => ["interface"]
        }
      ]
    }
  }

  # 添加通用字段
  mutate {
    replace => {
      "device_type" => "1000032"
      "product_name" => "华为-AR2500"
    }
  }

  # 保存提取字段
  mutate {
    rename => {
      "Command" => "action_details"
      "srcIp" => "source_address"
      "interface" => "action_object_name"
      "user" => "related_object_name"
    }
  }
}
