filter {
  ruby {
    path => "/usr/share/logstash/config/conf.d/ruby/event_processor.rb"
    script_params => {
      "patterns" => [
        {
          "regex" => '^<\d+>(?<time>\S+\s+\S+\s+\S+).*Interface\s+(?<interface>\S+),\s+changed\s+state\s+to\s+down.*'
          "event_name" => "网口拔出"
          "event_type" => "1000065"
          "event_subtype" => "1000000215"
          "extract_fields" => ["interface"]
        },
        {
          "regex" => 'Interface\s+(?<interface>\S+),\s+changed\s+state\s+to\s+up.*'
          "event_name" => "网口接入"
          "event_type" => "1000065"
          "event_subtype" => "1000000214"
          "extract_fields" => ["interface"]
        }
      ]
    }
  }

  # 添加通用字段
  mutate {
    replace => {
      "device_type" => "1000079"
      "product_name" => "左岸-ZVANCOM 44-N"
    }
  }

  # 保存提取字段
  mutate {
    rename => {
      "interface" => "action_object_name"
    }
  }
}
