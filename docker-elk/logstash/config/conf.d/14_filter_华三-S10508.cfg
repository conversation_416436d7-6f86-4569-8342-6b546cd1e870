filter {
  ruby {
    path => "/usr/share/logstash/config/conf.d/ruby/event_processor.rb"
    script_params => {
      "patterns" => [
        {
          "regex" => '%%\d+(?<logType>.*)/\d+\/\S+:\s\S+\s\S+\s(?<user>.*)from\s(?<srcIp>.*)\sport\s(?<srcPort>.*)\spassed\spassword.*$'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => ["logType", "user", "srcIp", "srcPort"]
        },
        {
          "regex" => '%%\d+(?<logType>.*)/\d+\/\S+:\s\S+\s\S+\s(?<user>.*)\(\S+:\s(?<srcIp>.*)\)\sconnected\sto\sthe\sserver\ssuccessfully.$'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => ["logType", "user", "srcIp"]
        },
        {
          "regex" => '%%\d+(?<logType>.*)/\d+\/\S+:\s(?<user>.*)\slogged\sin\sfrom\s(?<srcIp>.*).$'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => ["logType", "user", "srcIp"]
        },
        {
          "regex" => 'HeXin.*SHELL_LOGIN:.*;\s(?<user>\S+)\slogged\sin\sfrom\s(?<srcIp>\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3}).*$'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => ["user", "srcIp"]
        },
        {
          "regex" => '%%\d+SHELL.*logged out\sfrom\s(?<srcIp>\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3}).*$'
          "event_name" => "退出登录"
          "event_type" => "1000068"
          "event_subtype" => "1000000201"
          "extract_fields" => ["srcIp"]
        },
        {
          "regex" => '%%\d+SSHS.*logged out\sfrom\s(?<srcIp>\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3}).*$'
          "event_name" => "退出登录"
          "event_type" => "1000068"
          "event_subtype" => "1000000201"
          "extract_fields" => ["srcIp"]
        },
        {
          "regex" => 'HeXin.*SHELL_LOGOUT:.*;\s(?<user>\S+)\slogged\sout\sfrom\s(?<srcIp>\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3}).*$'
          "event_name" => "退出登录"
          "event_type" => "1000068"
          "event_subtype" => "1000000201"
          "extract_fields" => ["user", "srcIp"]
        },
        {
          "regex" => '%%\d+CFGMAN.*\sConfiguration changed.$'
          "event_name" => "配置变更"
          "event_type" => "1000026"
          "event_subtype" => "1000000094"
          "extract_fields" => []
        },
        {
          "regex" => '^HeXin.*SHELL_CMD:\s-DevIP=(?<DeviceIp>\S+);.*User=(?<Username>\w+)-{0,1}.*;.*Command\si{0,1}s{0,1}\s{0,1}(?<Command>.*)$'
          "event_name" => "配置变更"
          "event_type" => "1000026"
          "event_subtype" => "1000000094"
          "extract_fields" => ["DeviceIp", "Username", "Command"]
        },
        {
          "regex" => '%%\d+(?<logType>.*)/\d+\/\S+:.*\sinterface\s(?<interface>.*)\schanged\sto\sdown.$'
          "event_name" => "网口拔出"
          "event_type" => "1000065"
          "event_subtype" => "1000000215"
          "extract_fields" => ["logType", "interface"]
        },
        {
          "regex" => '%%\d+IFNET.*\sinterface\s(?<srcIp>\S+)\schanged\sto\sdown.$'
          "event_name" => "网口拔出"
          "event_type" => "1000065"
          "event_subtype" => "1000000215"
          "extract_fields" => ["srcIp"]
        },
        {
          "regex" => '%%.*UPDOWN\(\w+\):\s-DevIP=(?<devIp>\S+);\s+(?<interface>\S+):{0,1}\slink\sstatus\sis\sDOWN.*$'
          "event_name" => "网口拔出"
          "event_type" => "1000065"
          "event_subtype" => "1000000215"
          "extract_fields" => ["devIp", "interface"]
        },
        {
          "regex" => 'HeXin.*PHY_UPDOWN:\s-DevIP=(?<devIp>\S+);.*interface\s(?<interface>\S+)\schanged\sto\sdown.*$'
          "event_name" => "网口拔出"
          "event_type" => "1000065"
          "event_subtype" => "1000000215"
          "extract_fields" => ["devIp", "interface"]
        },
        {
          "regex" => '%%\d+(?<logType>.*)/\d+\/\S+:\sPhysical.*\sinterface\s(?<interface>.*)\schanged\sto\sup.$'
          "event_name" => "网口接入"
          "event_type" => "1000065"
          "event_subtype" => "1000000214"
          "extract_fields" => ["logType", "logType", "interface"]
        },
        {
          "regex" => '%%\d+(?<logType>.*)/\d+/\S+:\sLine.*\sinterface\s(?<interface>.*)\schanged\sto\sup.$'
          "event_name" => "网口接入"
          "event_type" => "1000065"
          "event_subtype" => "1000000214"
          "extract_fields" => ["logType", "interface"]
        },
        {
          "regex" => '%%.*LINK_UPDOWN\(\w+\):\s-DevIP=(?<devIp>\S+);\s+(?<interface>\S+):{0,1}\slink\sstatus\sis\sUP.*$'
          "event_name" => "网口接入"
          "event_type" => "1000065"
          "event_subtype" => "1000000214"
          "extract_fields" => ["devIp", "interface"]
        },
        {
          "regex" => '^HeXin.*PHY_UPDOWN:\s-DevIP=(?<devIp>\S+);.*interface\s(?<interface>\S+)\schanged\sto\sup.*$'
          "event_name" => "网口接入"
          "event_type" => "1000065"
          "event_subtype" => "1000000214"
          "extract_fields" => ["devIp", "interface"]
        }
      ]
    }
  }

  # 添加通用字段
  mutate {
    replace => {
      "device_type" => "1000079"
      "product_name" => "华三-S10508"
    }
  }

  # 保存提取字段
  mutate {
    rename => {
      "Command" => "action_details"
      "srcIp" => "source_address"
      "DeviceIp" => "device_name"
      "interface" => "action_object_name"
      "user" => "related_object_name"
      "srcPort" => "source_port"
      "Username" => "related_object_name"
    }
  }
}
