filter {
  ruby {
    path => "/usr/share/logstash/config/conf.d/ruby/event_processor.rb"
    script_params => {
      "patterns" => [
        {
          "regex" => '%%\d+SHELL\/\d.*\s(?<user>\S+)\slogin\sfrom(?<srcIp>.*)\.$'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => ["user", "srcIp"]
        },
        {
          "regex" => '%%\d+(?<logType>\S+)/\d+.*succeeded\s+in\s+logging\s+in.*UserName=(?<user>\S+),\s+Ip=(?<srcIp>.*),.*$'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => ["logType", "user", "srcIp"]
        },
        {
          "regex" => '%%\d+SSH.*PASSWORDAUTH_FAIL.*\suser\s(?<user>\S+)\son.*\sfailed.*$'
          "event_name" => "登录失败"
          "event_type" => "1000061"
          "event_subtype" => "1000000140"
          "extract_fields" => ["user"]
        },
        {
          "regex" => '%%.*USER_OFFLINERESULT.*USER:(?<user>\S+);MAC.*$'
          "event_name" => "登录失败"
          "event_type" => "1000061"
          "event_subtype" => "1000000140"
          "extract_fields" => ["user"]
        },
        {
          "regex" => '%%\d+\SHELL.*\s\s(?<user>\S+)\s+logout\sfrom\s(?<srcIp>.*)\.$'
          "event_name" => "退出登录"
          "event_type" => "1000068"
          "event_subtype" => "1000000201"
          "extract_fields" => ["user", "srcIp"]
        },
        {
          "regex" => '%%\d+(?<logType>\S+)/\d+.*succeeded\s+in\s+logging\s+out.*UserName=(?<user>\S+),\s+Ip=(?<srcIp>.*),.*$'
          "event_name" => "退出登录"
          "event_type" => "1000068"
          "event_subtype" => "1000000201"
          "extract_fields" => ["logType", "user", "srcIp"]
        },
        {
          "regex" => '%%01CFM/\d+\/SAVE.*\schose\sY.$'
          "event_name" => "配置变更"
          "event_type" => "1000026"
          "event_subtype" => "1000000094"
          "extract_fields" => []
        },
        {
          "regex" => 'IF_STATE.*Interface\s+(?<interface>\S+).*into\s+DOWN\s+state.*$'
          "event_name" => "网口拔出"
          "event_type" => "1000065"
          "event_subtype" => "1000000215"
          "extract_fields" => ["interface"]
        },
        {
          "regex" => '%%01IFNET/\d+\/UPDOWN.*interface\s+(?<interface>.*)\s+was\s+changed\s+to\s+DOWN.*$'
          "event_name" => "网口拔出"
          "event_type" => "1000065"
          "event_subtype" => "1000000215"
          "extract_fields" => ["interface"]
        },
        {
          "regex" => '(?<logType>\S+)\/\d.*\sinterface\s(?<interface>.*)\s+was.*\sDOWN.*$'
          "event_name" => "网口拔出"
          "event_type" => "1000065"
          "event_subtype" => "1000000215"
          "extract_fields" => ["logType", "interface"]
        },
        {
          "regex" => 'IF_STATE.*Interface\s+(?<interface>\S+).*into\s+UP\s+state.*$'
          "event_name" => "网口接入"
          "event_type" => "1000065"
          "event_subtype" => "1000000214"
          "extract_fields" => ["interface"]
        },
        {
          "regex" => '%%01IFNET/\d+\/UPDOWN.*interface\s(?<interface>.*)\s+was\s+changed\sto\sUP.*$'
          "event_name" => "网口接入"
          "event_type" => "1000065"
          "event_subtype" => "1000000214"
          "extract_fields" => ["interface"]
        }
      ]
    }
  }

  # 添加通用字段
  mutate {
    replace => {
      "device_type" => "1000079"
      "product_name" => "华为-S5328C"
    }
  }

  # 保存提取字段
  mutate {
    rename => {
      "srcIp" => "source_address"
      "interface" => "action_object_name"
      "user" => "related_object_name"
      "time" => "action_details"
    }
  }
}
