filter {
  ruby {
    path => "/usr/share/logstash/config/conf.d/ruby/event_processor.rb"
    script_params => {
      "patterns" => [
        {
          "regex" => '%%\d+(?<LoginType>.*)/\d+\/LOGIN\(\w+\).*logging\sin.*UserName=(?<user>.*),\s+Ip=(?<srcIp>.*),\s+.*$'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => ["LoginType", "user", "srcIp"]
        },
        {
          "regex" => '(?<LoginType>.*)/\d+/.*login.*UserName=(?<user>.*),\s+UserIP=(?<srcIp>.*),\s+.*$'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => ["LoginType", "user", "srcIp"]
        },
        {
          "regex" => '%%\d+\SHELL.*:Failed\sto\slogin.*Ip=(?<srcIp>\S+),\sUserName=(?<user>\S+),\sTimes.*AccessType.*$'
          "event_name" => "登录失败"
          "event_type" => "1000061"
          "event_subtype" => "1000000140"
          "extract_fields" => ["srcIp", "user"]
        },
        {
          "regex" => '%%\d+(?<LoginType>.*)/\d+\/LOGOUT\(\w+\).*logging\sout.*UserName=(?<user>.*),\s+Ip=(?<srcIp>.*),\s+.*$'
          "event_name" => "退出登录"
          "event_type" => "1000068"
          "event_subtype" => "1000000201"
          "extract_fields" => ["LoginType", "user", "srcIp"]
        },
        {
          "regex" => '(?<LoginType>.*)/\d+/.*LOGOUT.*logout.*UserName=(?<user>.*),\s+UserIP=(?<srcIp>.*),\s+.*$'
          "event_name" => "退出登录"
          "event_type" => "1000068"
          "event_subtype" => "1000000201"
          "extract_fields" => ["LoginType", "user", "srcIp"]
        },
        {
          "regex" => 'LINK_STATE.*interface\s+(?<interface>\S+)\s+has\s+entered\s+the\s+DOWN\s+state.*$'
          "event_name" => "网口拔出"
          "event_type" => "1000065"
          "event_subtype" => "1000000215"
          "extract_fields" => ["interface"]
        },
        {
          "regex" => '%%\d+(?<logType>\S+)\/\d+.*\STATE.*\Interface\s(?<interface>.*)\shas.*\sUP\sstate.$'
          "event_name" => "网口拔出"
          "event_type" => "1000065"
          "event_subtype" => "1000000215"
          "extract_fields" => ["logType", "interface"]
        },
        {
          "regex" => '(?<logType>\S+)\/\d+.*\s(?<interface>.*)\sturned\s.*DOWN\sstate.$'
          "event_name" => "网口拔出"
          "event_type" => "1000065"
          "event_subtype" => "1000000215"
          "extract_fields" => ["logType", "interface"]
        },
        {
          "regex" => 'LINK_STATE.*interface\s+(?<interface>\S+)\s+has\s+entered\s+the\s+UP\s+state.*$'
          "event_name" => "网口接入"
          "event_type" => "1000065"
          "event_subtype" => "1000000214"
          "extract_fields" => ["interface"]
        },
        {
          "regex" => '%%\d+(?<logType>\S+)\/\d+.*\STATE.*\Interface\s(?<interface>.*)\shas.*\sUP\sstate.$'
          "event_name" => "网口接入"
          "event_type" => "1000065"
          "event_subtype" => "1000000214"
          "extract_fields" => ["logType", "interface"]
        },
        {
          "regex" => '(?<logType>\S+)\/\d+.*\s(?<interface>.*)\sturned\sinto\sUP\sstate.$'
          "event_name" => "网口接入"
          "event_type" => "1000065"
          "event_subtype" => "1000000214"
          "extract_fields" => ["logType", "interface"]
        }
      ]
    }
  }

  # 添加通用字段
  mutate {
    replace => {
      "device_type" => "1000079"
      "product_name" => "华为-S5328"
    }
  }

  # 保存提取字段
  mutate {
    rename => {
      "srcIp" => "source_address"
      "interface" => "action_object_name"
      "user" => "related_object_name"
    }
  }
}
