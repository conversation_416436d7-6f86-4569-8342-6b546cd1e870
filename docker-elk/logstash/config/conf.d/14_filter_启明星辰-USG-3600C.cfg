filter {
  ruby {
    path => "/usr/share/logstash/config/conf.d/ruby/event_processor.rb"
    script_params => {
      "patterns" => [
        {
          "regex" => 'SrcIP=(?<srcIp>\S+)\sUserName=(?<user>\S+)\s+Operate="login"\s+ManageStyle=(?<logType>\S+)\s+Content="success.*$'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => ["srcIp", "user", "logType"]
        },
        {
          "regex" => '^<\d+>.*date="(?<time>\S+\s\S+)".*from=(?<srcIp>\S+).*admin=(?<user>\S+).*administrator 登陆.*$'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => ["srcIp", "user"]
        },
        {
          "regex" => 'SrcIP=(?<srcIp>\S+)\sUserName=(?<user>\S+)\s+Operate="login"\s+ManageStyle=(?<logType>\S+)\s+Content="failed for.*$'
          "event_name" => "登录失败"
          "event_type" => "1000061"
          "event_subtype" => "1000000140"
          "extract_fields" => ["srcIp", "user", "logType"]
        },
        {
          "regex" => '^<\d+>.*date="(?<time>\S+\s\S+)".*from=(?<srcIp>\S+).*msg="帐号登录错误" .*$'
          "event_name" => "登录失败"
          "event_type" => "1000061"
          "event_subtype" => "1000000140"
          "extract_fields" => ["srcIp"]
        },
        {
          "regex" => 'SrcIP=(?<srcIp>\S+)\sUserName=(?<user>\S+)\s+ManageStyle=(?<logType>\S+)\s+Content="Logout.*$'
          "event_name" => "退出登录"
          "event_type" => "1000068"
          "event_subtype" => "1000000201"
          "extract_fields" => ["srcIp", "user", "logType"]
        },
        {
          "regex" => '^<\d+>.*date="(?<time>\S+\s\S+)"\s.*logtype=(?<logType>\S+).*webui\sfrom=(?<srcIp>\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3}).*admin=(?<user>\S+)\sact=退出.*$'
          "event_name" => "退出登录"
          "event_type" => "1000068"
          "event_subtype" => "1000000201"
          "extract_fields" => ["logType", "srcIp", "user"]
        },
        {
          "regex" => '^<\d+>.*date="(?<time>\S+\s\S+)"\s.*sa=(?<srcIp>.*)\ssport=(?<srcPort>\d+)\sda=(?<dstIp>.*)\sdport=(?<dstPort>\d+).*policy=POLICY_DENY.*$'
          "event_name" => "不符合安全策略访问"
          "event_type" => "1000065"
          "event_subtype" => "1000000241"
          "extract_fields" => ["srcIp", "srcPort", "dstIp", "dstPort"]
        },
        {
          "regex" => 'SerialNum=.*?\sGenTime="(?<time>.*?)"\sSrcIP=(?<srcIp>\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3}) DstIP=(?<dstIp>\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3}) Protocol=(?<protocol>\S+?)\sSrcPort=(?<srcPort>\d+?)\sDstPort=(?<dstPort>\d+?)\s.*?Action=DENY Content=.*$'
          "event_name" => "不符合安全策略访问"
          "event_type" => "1000065"
          "event_subtype" => "1000000241"
          "extract_fields" => ["srcIp", "dstIp", "protocol", "srcPort", "dstPort"]
        },
        {
          "regex" => 'SerialNum=.*?\sGenTime="(?<time>.*?)"\sSrcIP=(?<srcIp>\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3}) DstIP=(?<dstIp>\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3}) Protocol=(?<protocol>\S+?)\s.*?Action=PERMIT Content=.*$'
          "event_name" => "符合安全策略访问"
          "event_type" => "1000065"
          "event_subtype" => "1000000241"
          "extract_fields" => ["srcIp", "dstIp", "protocol"]
        },
        {
          "regex" => 'SerialNum=.*?\sGenTime="(?<time>.*?)"\sSrcIP=(?<srcIp>\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3}) DstIP=(?<dstIp>\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3}) Protocol=(?<protocol>\S+?)\sSrcPort=(?<srcPort>\d+?)\sDstPort=(?<dstPort>\d+?)\s.*?Action=PERMIT Content=.*$'
          "event_name" => "符合安全策略访问"
          "event_type" => "1000065"
          "event_subtype" => "1000000241"
          "extract_fields" => ["srcIp", "dstIp", "protocol", "srcPort", "dstPort"]
        },
        {
          "regex" => '^(?:SerialNum=\S+\s+GenTime="(?<time>\S+\s+\S+)")?\s*Content="interface\s+(?<interface>\S+)\s+linkdown".*$'
          "event_name" => "网口拔出"
          "event_type" => "1000065"
          "event_subtype" => "1000000215"
          "extract_fields" => ["interface"]
        },
        {
          "regex" => '^<\d+>.*e1000:\s(?<interface>.*):\se1000_watchdog.*Link\sis\sDown.*$'
          "event_name" => "网口拔出"
          "event_type" => "1000065"
          "event_subtype" => "1000000215"
          "extract_fields" => ["interface"]
        },
        {
          "regex" => 'Content="interface\s+(?<interface>\S+)\s+linkup".*$'
          "event_name" => "网口接入"
          "event_type" => "1000065"
          "event_subtype" => "1000000214"
          "extract_fields" => ["interface"]
        },
        {
          "regex" => '^<\d+>.*e1000:\s+(?<interface>.*?):.*Link\sis\sUp.*$'
          "event_name" => "网口接入"
          "event_type" => "1000065"
          "event_subtype" => "1000000214"
          "extract_fields" => ["interface"]
        }
      ]
    }
  }

  # 添加通用字段
  mutate {
    replace => {
      "device_type" => "1000035"
      "product_name" => "启明星辰-USG-3600C"
    }
  }

  # 保存提取字段
  mutate {
    rename => {
      "dstIp" => "dest_address"
      "protocol" => "protocol"
      "dstPort" => "dest_port"
      "srcIp" => "source_address"
      "interface" => "action_object_name"
      "user" => "related_object_name"
      "srcPort" => "source_port"
    }
  }
}
