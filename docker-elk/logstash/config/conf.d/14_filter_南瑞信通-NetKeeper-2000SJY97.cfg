filter {
  ruby {
    path => "/usr/share/logstash/config/conf.d/ruby/event_processor.rb"
    script_params => {
      "patterns" => [
        {
          "regex" => '0\s+1\s+(?<user>\S+).*'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => ["user"]
        },
        {
          "regex" => 'VEAD\s0\s1\s(?<Username>\S+).*$'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => ["Username"]
        },
        {
          "regex" => '0\s1\sname\slogin\sok!$'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => []
        },
        {
          "regex" => '0\s1\sname\slogin\sok!$'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => []
        },
        {
          "regex" => 'VEAD\s0\s1\s(?<Username>\S+).*$'
          "event_name" => "root用户登录"
          "event_type" => "1000061"
          "event_subtype" => "1000000138"
          "extract_fields" => ["Username"]
        },
        {
          "regex" => 'VEAD\s+0\s+2\s+(?<Username>\S+).*$'
          "event_name" => "登录失败"
          "event_type" => "1000061"
          "event_subtype" => "1000000140"
          "extract_fields" => ["Username"]
        },
        {
          "regex" => '0\s+2\s+login\s+with\s+error\s+password.*$'
          "event_name" => "登录失败"
          "event_type" => "1000061"
          "event_subtype" => "1000000140"
          "extract_fields" => []
        },
        {
          "regex" => 'VEAD\s+0\s+2\s+(?<Username>\S+).*?$'
          "event_name" => "登录失败"
          "event_type" => "1000061"
          "event_subtype" => "1000000140"
          "extract_fields" => ["Username"]
        },
        {
          "regex" => '0\s+2\s+login\s+with\s+error\s+password.*$'
          "event_name" => "登录失败"
          "event_type" => "1000061"
          "event_subtype" => "1000000140"
          "extract_fields" => []
        },
        {
          "regex" => '0\s+2\s+user\s+logout.*'
          "event_name" => "退出登录"
          "event_type" => "1000068"
          "event_subtype" => "1000000201"
          "extract_fields" => []
        },
        {
          "regex" => '0\s+4\s+(?<user>\S+).*'
          "event_name" => "退出登录"
          "event_type" => "1000068"
          "event_subtype" => "1000000201"
          "extract_fields" => ["user"]
        },
        {
          "regex" => 'VEAD\s+0\s+4\s+(?<Username>\S+).*$'
          "event_name" => "退出登录"
          "event_type" => "1000068"
          "event_subtype" => "1000000201"
          "extract_fields" => ["Username"]
        },
        {
          "regex" => 'VEAD\s+0\s+4\s+(?<Username>\S+).*?$'
          "event_name" => "退出登录"
          "event_type" => "1000068"
          "event_subtype" => "1000000201"
          "extract_fields" => ["Username"]
        },
        {
          "regex" => 'VEAD\s0\s3\s5.*$'
          "event_name" => "配置变更"
          "event_type" => "1000026"
          "event_subtype" => "1000000094"
          "extract_fields" => []
        },
        {
          "regex" => 'VEAD\s0\s3\s6.*$'
          "event_name" => "配置变更"
          "event_type" => "1000026"
          "event_subtype" => "1000000094"
          "extract_fields" => []
        },
        {
          "regex" => 'VEAD\s0\s3\s1.*$'
          "event_name" => "配置变更"
          "event_type" => "1000026"
          "event_subtype" => "1000000094"
          "extract_fields" => []
        },
        {
          "regex" => 'VEAD\s0\s3\s2.*$'
          "event_name" => "配置变更"
          "event_type" => "1000026"
          "event_subtype" => "1000000094"
          "extract_fields" => []
        },
        {
          "regex" => 'VEAD\s0\s3\s3.*$'
          "event_name" => "配置变更"
          "event_type" => "1000026"
          "event_subtype" => "1000000094"
          "extract_fields" => []
        },
        {
          "regex" => 'VEAD\s0\s3\s3.*$'
          "event_name" => "配置变更"
          "event_type" => "1000026"
          "event_subtype" => "1000000094"
          "extract_fields" => []
        },
        {
          "regex" => 'VEAD\s0\s3\s2.*$'
          "event_name" => "配置变更"
          "event_type" => "1000026"
          "event_subtype" => "1000000094"
          "extract_fields" => []
        },
        {
          "regex" => 'VEAD\s0\s3\s1.*$'
          "event_name" => "配置变更"
          "event_type" => "1000026"
          "event_subtype" => "1000000094"
          "extract_fields" => []
        },
        {
          "regex" => 'VEAD\s0\s3\s6.*$'
          "event_name" => "配置变更"
          "event_type" => "1000026"
          "event_subtype" => "1000000094"
          "extract_fields" => []
        },
        {
          "regex" => 'VEAD\s0\s3\s5.*$'
          "event_name" => "配置变更"
          "event_type" => "1000026"
          "event_subtype" => "1000000094"
          "extract_fields" => []
        },
        {
          "regex" => 'VEAD\s0\s3\s4.*$'
          "event_name" => "配置变更"
          "event_type" => "1000026"
          "event_subtype" => "1000000094"
          "extract_fields" => []
        },
        {
          "regex" => '2\s+7\s+8\s+(?<src_ip>\S+)\s+(?<src_port>\S+)\s+(?<dest_ip>\S+)\s+(?<dest_port>\S+).*?$'
          "event_name" => "不符合安全策略访问"
          "event_type" => "1000065"
          "event_subtype" => "1000000241"
          "extract_fields" => ["src_ip", "src_port", "dest_ip", "dest_port"]
        },
        {
          "regex" => '^\s*2\s+5\s+\[\d+\]\s+\[(?<srcIp>\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})\]\s*\[(?<srcPort>\d+)\]\s*\[(?<dstIp>\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})\]\s*\[(?<dstPort>\d+)\].*$'
          "event_name" => "不符合安全策略访问"
          "event_type" => "1000065"
          "event_subtype" => "1000000241"
          "extract_fields" => ["srcIp", "srcPort", "dstIp", "dstPort"]
        },
        {
          "regex" => '^2\s+6\s+\[\d+\]\s+\[(?<srcIp>\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3})\]\s*\[(?<dstIp>\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3})\]\s*\[(?<srcPort>\d+)\]\s*\[(?<dstPort>\d+)\].*$'
          "event_name" => "不符合安全策略访问"
          "event_type" => "1000065"
          "event_subtype" => "1000000241"
          "extract_fields" => ["srcIp", "dstIp", "srcPort", "dstPort"]
        },
        {
          "regex" => '^<\d+>(?<time>\d{4}-\d{1,2}-\d{1,2}\s+\d{1,2}.*\d{1,2}.\d{1,2}).*kernel:\s+(?<interface>\S+):Link\s+down.*$'
          "event_name" => "网口拔出"
          "event_type" => "1000065"
          "event_subtype" => "1000000215"
          "extract_fields" => ["time", "interface"]
        },
        {
          "regex" => 'VEAD\s1\s6\s(?<Eth>.+)\sdown$'
          "event_name" => "网口拔出"
          "event_type" => "1000065"
          "event_subtype" => "1000000215"
          "extract_fields" => ["Eth"]
        },
        {
          "regex" => 'VEAD\s1\s6\s(?<Eth>.+)\sdown$'
          "event_name" => "网口拔出"
          "event_type" => "1000065"
          "event_subtype" => "1000000215"
          "extract_fields" => ["Eth"]
        },
        {
          "regex" => 'kernel:\s+(?<interface>\S+):.*Full\s+dupex.*$'
          "event_name" => "网口接入"
          "event_type" => "1000065"
          "event_subtype" => "1000000214"
          "extract_fields" => ["interface"]
        },
        {
          "regex" => 'VEAD\s1\s7\s(?<Eth>.+)\sup$'
          "event_name" => "网口接入"
          "event_type" => "1000065"
          "event_subtype" => "1000000214"
          "extract_fields" => ["Eth"]
        },
        {
          "regex" => 'VEAD\s1\s7\s(?<Eth>.+)\sup$'
          "event_name" => "网口接入"
          "event_type" => "1000065"
          "event_subtype" => "1000000214"
          "extract_fields" => ["Eth"]
        },
        {
          "regex" => 'VEAD\s1\s8\s(?<msg>.*[Ll]ost)$'
          "event_name" => "连接中断"
          "event_type" => "1000027"
          "event_subtype" => "1000000107"
          "extract_fields" => ["msg"]
        },
        {
          "regex" => 'VEAD\s1\s8\s(?<msg>.*[Ll]ost)$'
          "event_name" => "连接中断"
          "event_type" => "1000027"
          "event_subtype" => "1000000107"
          "extract_fields" => ["msg"]
        },
        {
          "regex" => '^\s*2\s+1\s+6\s+(?<localIP>\S+)\s+(?<dstIP>\S+)\s+(?<msg>.*)$'
          "event_name" => "私钥签名错误"
          "event_type" => "1000065"
          "event_subtype" => "1000000263"
          "extract_fields" => ["localIP", "dstIP", "msg"]
        },
        {
          "regex" => '^\s*2\s+1\s+4\s+(?<localTunnelAddress>\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})\s+(?<distantTunnelAddress>\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})\s+(?<Msg>.*?)$'
          "event_name" => "隧道没有配置"
          "event_type" => "1000065"
          "event_subtype" => "1000000247"
          "extract_fields" => ["localTunnelAddress", "distantTunnelAddress", "Msg"]
        },
        {
          "regex" => '^\s*1\s+3\s+(?<MemUsg>.*)%.*$'
          "event_name" => "性能监控"
          "event_type" => "1000025"
          "event_subtype" => "1000000089"
          "extract_fields" => ["MemUsg"]
        },
        {
          "regex" => '^1\s2\s(?<MgmtCPU>.*)%\scpu\sloadavg$'
          "event_name" => "性能监控"
          "event_type" => "1000025"
          "event_subtype" => "1000000089"
          "extract_fields" => ["MgmtCPU"]
        },
        {
          "regex" => 'VEAD\s1\s3\s(?<MemRate>.+%)$'
          "event_name" => "性能监控"
          "event_type" => "1000025"
          "event_subtype" => "1000000089"
          "extract_fields" => ["MemRate"]
        },
        {
          "regex" => '^1\s3\s(?<MemUsg>.*)%\smemory\sfree$'
          "event_name" => "性能监控"
          "event_type" => "1000025"
          "event_subtype" => "1000000089"
          "extract_fields" => ["MemUsg"]
        },
        {
          "regex" => 'VEAD\s1\s2\s(?<CPURate>.+%) cpu.+$'
          "event_name" => "性能监控"
          "event_type" => "1000025"
          "event_subtype" => "1000000089"
          "extract_fields" => ["CPURate"]
        },
        {
          "regex" => 'VEAD\s1\s2\s(?<CPURate>.+%) cpu.+$'
          "event_name" => "性能监控"
          "event_type" => "1000025"
          "event_subtype" => "1000000089"
          "extract_fields" => ["CPURate"]
        },
        {
          "regex" => '^1\s3\s(?<MemUsg>.*)%\smemory\sfree$'
          "event_name" => "性能监控"
          "event_type" => "1000025"
          "event_subtype" => "1000000089"
          "extract_fields" => ["MemUsg"]
        },
        {
          "regex" => '^\s*VEAD\s1\s3\s(?<MemRate>.+%)$'
          "event_name" => "性能监控"
          "event_type" => "1000025"
          "event_subtype" => "1000000089"
          "extract_fields" => ["MemRate"]
        },
        {
          "regex" => '^1\s2\s(?<MgmtCPU>.*)%\scpu\sloadavg$'
          "event_name" => "性能监控"
          "event_type" => "1000025"
          "event_subtype" => "1000000089"
          "extract_fields" => ["MgmtCPU"]
        },
        {
          "regex" => '^(?<MgmtCPU>.+)\scpu.*$'
          "event_name" => "性能监控"
          "event_type" => "1000025"
          "event_subtype" => "1000000089"
          "extract_fields" => ["MgmtCPU"]
        },
        {
          "regex" => '^\s*1\s+3\s+(?<MemUsg>.*)%.*$'
          "event_name" => "性能监控"
          "event_type" => "1000025"
          "event_subtype" => "1000000089"
          "extract_fields" => ["MemUsg"]
        }
      ]
    }
  }

  # 添加通用字段
  mutate {
    replace => {
      "device_type" => "1000080"
      "product_name" => "南瑞信通-NetKeeper-2000SJY97"
    }
  }

  # 保存提取字段
  mutate {
    rename => {
      "srcIp" => "source_address"
      "interface" => "action_object_name"
      "Eth" => "action_object_name"
      "srcPort" => "source_port"
      "dstIP" => "dest_address"
      "dstPort" => "dest_port"
      "dest_port" => "dest_port"
      "src_ip" => "source_address"
      "user" => "related_object_name"
      "time" => "action_details"
      "src_port" => "source_port"
      "dstIp" => "dest_address"
      "dest_ip" => "dest_address"
      "Username" => "related_object_name"
    }
  }
}
