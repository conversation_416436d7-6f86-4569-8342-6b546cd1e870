/*
 Navicat Premium Dump SQL

 Source Server         : ************
 Source Server Type    : MySQL
 Source Server Version : 80020 (8.0.20)
 Source Host           : ************:3306
 Source Schema         : rzsj

 Target Server Type    : MySQL
 Target Server Version : 80020 (8.0.20)
 File Encoding         : 65001

 Date: 19/12/2024 08:57:02
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- 创建新数据库
CREATE DATABASE IF NOT EXISTS rzsj;

-- 切换到新创建的数据库
USE rzsj;

-- ----------------------------
-- Table structure for alarm_monitoring
-- ----------------------------
DROP TABLE IF EXISTS `alarm_monitoring`;
CREATE TABLE `alarm_monitoring`  (
  `id` varchar(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '告警ID',
  `alert_code` int NULL DEFAULT NULL COMMENT '告警编号',
  `alert_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '告警名称',
  `rule_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '关联策略ID',
  `alert_level` int NULL DEFAULT NULL COMMENT '级别 0-一般 1-警告 2-严重 3-极度严重',
  `action_object_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '对象名称',
  `action_object_address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '对象IP',
  `os_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '系统类型-资产列表',
  `alert_type` int NULL DEFAULT NULL COMMENT '告警类别',
  `alert_sub_type` int NULL DEFAULT NULL COMMENT '告警子类',
  `detail` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '描述',
  `source_ip` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '源IP',
  `dest_ip` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '目的IP',
  `source_city` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '源城市',
  `source_country` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '源国家',
  `total_number` int NULL DEFAULT 0 COMMENT '总次',
  `state` int NULL DEFAULT NULL COMMENT '状态：0：待处理 1：已确认 2：已归档',
  `clean_reason` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '清除原因',
  `confirm_reason` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '确认原因',
  `created_at` datetime NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '产生时间',
  `create_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '确认人',
  `create_time` datetime NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '操作时间',
  `event_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '对应ES的_id标识',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '告警监控信息表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of alarm_monitoring
-- ----------------------------

-- ----------------------------
-- Table structure for alarm_monitoring_es
-- ----------------------------
DROP TABLE IF EXISTS `alarm_monitoring_es`;
CREATE TABLE `alarm_monitoring_es`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `alarm_monitoring_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `event_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 30350 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of alarm_monitoring_es
-- ----------------------------

-- ----------------------------
-- Table structure for alert_level_dict
-- ----------------------------
DROP TABLE IF EXISTS `alert_level_dict`;
CREATE TABLE `alert_level_dict`  (
  `id` int NOT NULL,
  `level_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of alert_level_dict
-- ----------------------------
INSERT INTO `alert_level_dict` VALUES (0, '一般');
INSERT INTO `alert_level_dict` VALUES (1, '警告');
INSERT INTO `alert_level_dict` VALUES (2, '严重');
INSERT INTO `alert_level_dict` VALUES (3, '极度严重');

-- ----------------------------
-- Table structure for asset_category_type
-- ----------------------------
DROP TABLE IF EXISTS `asset_category_type`;
CREATE TABLE `asset_category_type`  (
  `id` int NOT NULL,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '资产类别字典表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of asset_category_type
-- ----------------------------
INSERT INTO `asset_category_type` VALUES (1, '服务器');
INSERT INTO `asset_category_type` VALUES (2, '终端');
INSERT INTO `asset_category_type` VALUES (3, '网络设备');
INSERT INTO `asset_category_type` VALUES (4, '安全设备');

-- ----------------------------
-- Table structure for asset_manage
-- ----------------------------
DROP TABLE IF EXISTS `asset_manage`;
CREATE TABLE `asset_manage`  (
  `asset_id` bigint NOT NULL AUTO_INCREMENT COMMENT '资产ID',
  `asset_number` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '资产编号',
  `asset_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '资产名称',
  `asset_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '资产类型',
  `asset_ip` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '资产IP',
  `asset_category` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '资产类别',
  `system_version` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '系统版本',
  `hardware_version` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '硬件型号',
  `serial_number` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '序列号',
  `purpose` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '用途',
  `mac_address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT 'MAC地址',
  `security` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '保密性',
  `integrality` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '完整性',
  `availability` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '可用性',
  `asset_value` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '资产价值',
  `person_charge` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '责任人',
  `warranty_date` varchar(24) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '保修日期',
  `putaway_information` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '上架信息',
  `create_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '更新人',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`asset_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 104 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '资产管理' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of asset_manage
-- ----------------------------

-- ----------------------------
-- Table structure for asset_manage_type
-- ----------------------------
DROP TABLE IF EXISTS `asset_manage_type`;
CREATE TABLE `asset_manage_type`  (
  `id` int NOT NULL,
  `asset_type_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '系统类型名称',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '资产管理系统类型表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of asset_manage_type
-- ----------------------------
INSERT INTO `asset_manage_type` VALUES (1, 'Centos 服务器');
INSERT INTO `asset_manage_type` VALUES (2, 'Debian 服务器');
INSERT INTO `asset_manage_type` VALUES (3, 'D型 态势感知采集装置');
INSERT INTO `asset_manage_type` VALUES (4, 'Fedora 服务器');
INSERT INTO `asset_manage_type` VALUES (5, 'SunOS 服务器');
INSERT INTO `asset_manage_type` VALUES (6, 'Suse 服务器');
INSERT INTO `asset_manage_type` VALUES (7, 'Ubuntu 服务器');
INSERT INTO `asset_manage_type` VALUES (8, 'solaris 10 服务器');
INSERT INTO `asset_manage_type` VALUES (9, '上海博达 交换机');
INSERT INTO `asset_manage_type` VALUES (10, '上海宽裕 交换机');
INSERT INTO `asset_manage_type` VALUES (11, '上海贝尔 交换机');
INSERT INTO `asset_manage_type` VALUES (12, '东土 交换机');
INSERT INTO `asset_manage_type` VALUES (13, '东软 防火墙');
INSERT INTO `asset_manage_type` VALUES (14, '中兴 交换机');
INSERT INTO `asset_manage_type` VALUES (15, '中标麒麟 服务器');
INSERT INTO `asset_manage_type` VALUES (16, '优麒麟 服务器');
INSERT INTO `asset_manage_type` VALUES (17, '兴唐 纵向加密认证装置');
INSERT INTO `asset_manage_type` VALUES (18, '凝思 服务器');
INSERT INTO `asset_manage_type` VALUES (19, '华三 交换机');
INSERT INTO `asset_manage_type` VALUES (20, '华三 防火墙');
INSERT INTO `asset_manage_type` VALUES (21, '华为 交换机');
INSERT INTO `asset_manage_type` VALUES (22, '华为 路由器');
INSERT INTO `asset_manage_type` VALUES (23, '华为 防火墙');
INSERT INTO `asset_manage_type` VALUES (24, '南瑞 交换机');
INSERT INTO `asset_manage_type` VALUES (25, '南瑞信通 态势感知采集装置');
INSERT INTO `asset_manage_type` VALUES (26, '南瑞信通 横向反向隔离装置');
INSERT INTO `asset_manage_type` VALUES (27, '南瑞信通 横向正向隔离装置');
INSERT INTO `asset_manage_type` VALUES (28, '南瑞信通 纵向加密认证装置');
INSERT INTO `asset_manage_type` VALUES (29, '南瑞继保 交换机');
INSERT INTO `asset_manage_type` VALUES (30, '博维亚讯 交换机');
INSERT INTO `asset_manage_type` VALUES (31, '卫士通 纵向加密认证装置');
INSERT INTO `asset_manage_type` VALUES (32, '启明星辰 防火墙');
INSERT INTO `asset_manage_type` VALUES (33, '国电南思 服务器');
INSERT INTO `asset_manage_type` VALUES (34, '天融信 防火墙');
INSERT INTO `asset_manage_type` VALUES (35, '宝讯 交换机');
INSERT INTO `asset_manage_type` VALUES (36, '左岸 交换机');
INSERT INTO `asset_manage_type` VALUES (37, '广州兆和 态势感知采集装置');
INSERT INTO `asset_manage_type` VALUES (38, '微软 服务器');
INSERT INTO `asset_manage_type` VALUES (39, '微软 通用主机');
INSERT INTO `asset_manage_type` VALUES (40, '思科 交换机');
INSERT INTO `asset_manage_type` VALUES (41, '思科 路由器');
INSERT INTO `asset_manage_type` VALUES (42, '思科 防火墙');
INSERT INTO `asset_manage_type` VALUES (43, '惠普 服务器');
INSERT INTO `asset_manage_type` VALUES (44, '摩莎 交换机');
INSERT INTO `asset_manage_type` VALUES (45, '深信服 防火墙');
INSERT INTO `asset_manage_type` VALUES (46, '珠海鸿瑞 横向反向隔离装置');
INSERT INTO `asset_manage_type` VALUES (47, '珠海鸿瑞 横向正向隔离装置');
INSERT INTO `asset_manage_type` VALUES (48, '瑞斯康达 交换机');
INSERT INTO `asset_manage_type` VALUES (49, '科东 纵向加密认证装置');
INSERT INTO `asset_manage_type` VALUES (50, '红帽 服务器');
INSERT INTO `asset_manage_type` VALUES (51, '红帽 通用主机');
INSERT INTO `asset_manage_type` VALUES (52, '绿盟 防火墙');
INSERT INTO `asset_manage_type` VALUES (53, '罗杰康 交换机');
INSERT INTO `asset_manage_type` VALUES (54, '许继电气 交换机');
INSERT INTO `asset_manage_type` VALUES (55, '赫斯曼 交换机');
INSERT INTO `asset_manage_type` VALUES (56, '迪普 防火墙');
INSERT INTO `asset_manage_type` VALUES (57, '银河麒麟 服务器');
INSERT INTO `asset_manage_type` VALUES (58, '麒麟 服务器');
INSERT INTO `asset_manage_type` VALUES (59, '默安 安全设备');
INSERT INTO `asset_manage_type` VALUES (60, '其它');

-- ----------------------------
-- Table structure for audit_category
-- ----------------------------
DROP TABLE IF EXISTS `audit_category`;
CREATE TABLE `audit_category`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT 'id',
  `category_id` int NULL DEFAULT NULL COMMENT '类型Id',
  `category_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '类型名称',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 11 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '审计类型' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of audit_category
-- ----------------------------
INSERT INTO `audit_category` VALUES (1, 1, '访问控制审计');
INSERT INTO `audit_category` VALUES (2, 2, '配置状态');
INSERT INTO `audit_category` VALUES (3, 3, '连接');
INSERT INTO `audit_category` VALUES (4, 4, '有害程序类告警');
INSERT INTO `audit_category` VALUES (5, 5, '账户管理');
INSERT INTO `audit_category` VALUES (6, 6, '策略管理');
INSERT INTO `audit_category` VALUES (7, 7, '数据文件');
INSERT INTO `audit_category` VALUES (8, 8, '网络攻击类告警');
INSERT INTO `audit_category` VALUES (9, 9, '其它');
INSERT INTO `audit_category` VALUES (10, 10, '用户命令');

-- ----------------------------
-- Table structure for audit_event
-- ----------------------------
DROP TABLE IF EXISTS `audit_event`;
CREATE TABLE `audit_event`  (
  `id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `value` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '显示值',
  `field_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '对应字段',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '审计事件信息' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of audit_event
-- ----------------------------
INSERT INTO `audit_event` VALUES ('1', '审计类型', 'rule_type');
INSERT INTO `audit_event` VALUES ('2', '审计策略', 'rule_name');
INSERT INTO `audit_event` VALUES ('3', '审计行为执行者', 'related_object_name');
INSERT INTO `audit_event` VALUES ('4', '审计行为执行者地址', 'related_object_address');
INSERT INTO `audit_event` VALUES ('5', '审计行为执行者账号', 'related_object_account');
INSERT INTO `audit_event` VALUES ('6', '审计行为来源地址', 'action_address');
INSERT INTO `audit_event` VALUES ('7', '审计对象名称', 'device_name');
INSERT INTO `audit_event` VALUES ('8', '审计对象地址', 'device_address');
INSERT INTO `audit_event` VALUES ('9', '动作', 'action');

-- ----------------------------
-- Table structure for audit_event_analysis
-- ----------------------------
DROP TABLE IF EXISTS `audit_event_analysis`;
CREATE TABLE `audit_event_analysis`  (
  `id` varchar(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'ID',
  `event_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '事件名称',
  `audit_level` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '事件级别',
  `device_address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '对象IP',
  `created_at` datetime NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '产生时间',
  `rule_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '审计类型',
  `rule_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '审计策略',
  `related_object_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '审计行为执行者',
  `related_object_address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '审计行为执行者地址',
  `related_object_account` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '审计行为执行者账号',
  `action_address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '审计行为来源地址',
  `device_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '审计对象名称',
  `device_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '审计目标类型',
  `action` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '动作',
  `action_details` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '审计行为详细信息',
  `action_result` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '审计行为结果',
  `total_count` int NULL DEFAULT NULL COMMENT '原始事件数量',
  `event_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '对应ES的_id,查询日志',
  `update_time` datetime NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '审计事件分析' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of audit_event_analysis
-- ----------------------------

-- ----------------------------
-- Table structure for audit_event_analysis_es
-- ----------------------------
DROP TABLE IF EXISTS `audit_event_analysis_es`;
CREATE TABLE `audit_event_analysis_es`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `audit_event_analysis_id` varchar(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `event_id` varchar(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 46189 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of audit_event_analysis_es
-- ----------------------------

-- ----------------------------
-- Table structure for event_category
-- ----------------------------
DROP TABLE IF EXISTS `event_category`;
CREATE TABLE `event_category`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT 'id',
  `category_id` int NULL DEFAULT NULL COMMENT '分类Id',
  `category_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '分类名称',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 26 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '事件分类' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of event_category
-- ----------------------------
INSERT INTO `event_category` VALUES (1, 1, '有害程序');
INSERT INTO `event_category` VALUES (2, 2, '网络攻击');
INSERT INTO `event_category` VALUES (3, 3, '信息破坏');
INSERT INTO `event_category` VALUES (4, 4, '信息内容安全');
INSERT INTO `event_category` VALUES (5, 5, '设备故障');
INSERT INTO `event_category` VALUES (6, 6, '访问控制');
INSERT INTO `event_category` VALUES (7, 7, '帐户管理');
INSERT INTO `event_category` VALUES (8, 8, '策略管理');
INSERT INTO `event_category` VALUES (9, 9, '配置状态');
INSERT INTO `event_category` VALUES (10, 10, '数据文件');
INSERT INTO `event_category` VALUES (11, 11, '连接');
INSERT INTO `event_category` VALUES (12, 12, '用户命令');
INSERT INTO `event_category` VALUES (13, 13, '脆弱性');
INSERT INTO `event_category` VALUES (14, 14, '数据泄漏');
INSERT INTO `event_category` VALUES (15, 15, '邮件异常');
INSERT INTO `event_category` VALUES (16, 16, 'Webshell');
INSERT INTO `event_category` VALUES (17, 17, '暴力破解');
INSERT INTO `event_category` VALUES (18, 18, 'linux主机');
INSERT INTO `event_category` VALUES (19, 19, 'windows主机');
INSERT INTO `event_category` VALUES (20, 20, '运维监控');
INSERT INTO `event_category` VALUES (21, 21, '帐号异常');
INSERT INTO `event_category` VALUES (22, 22, '主机异常');
INSERT INTO `event_category` VALUES (23, 23, '违规行为');
INSERT INTO `event_category` VALUES (24, 24, '可疑活动');
INSERT INTO `event_category` VALUES (25, 25, '其它');

-- ----------------------------
-- Table structure for event_condition_category
-- ----------------------------
DROP TABLE IF EXISTS `event_condition_category`;
CREATE TABLE `event_condition_category`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT 'id',
  `condition_id` int NULL DEFAULT NULL COMMENT '条件id',
  `condition_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '条件名称',
  `condition_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '条件分类',
  `condition_combine` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '是否合并条件（0-否1-是）',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 76 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '事件条件分类' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of event_condition_category
-- ----------------------------
INSERT INTO `event_condition_category` VALUES (1, 1, '名称', '基础', '1');
INSERT INTO `event_condition_category` VALUES (2, 2, '标准事件编号', '基础', '1');
INSERT INTO `event_condition_category` VALUES (3, 3, '详细信息', '基础', '0');
INSERT INTO `event_condition_category` VALUES (4, 4, '事件类型', '基础', '1');
INSERT INTO `event_condition_category` VALUES (5, 5, '事件子类', '基础', '1');
INSERT INTO `event_condition_category` VALUES (6, 6, '严重级别', '基础', '1');
INSERT INTO `event_condition_category` VALUES (7, 7, '原始级别', '基础', '1');
INSERT INTO `event_condition_category` VALUES (8, 8, '设备类型', '基础', '1');
INSERT INTO `event_condition_category` VALUES (9, 9, '设备地址', '基础', '1');
INSERT INTO `event_condition_category` VALUES (10, 10, '设备名称', '基础', '1');
INSERT INTO `event_condition_category` VALUES (11, 11, '产品名称', '基础', '1');
INSERT INTO `event_condition_category` VALUES (12, 12, '产品版本', '基础', '1');
INSERT INTO `event_condition_category` VALUES (13, 13, '接收日期时间', '基础', '1');
INSERT INTO `event_condition_category` VALUES (14, 14, '原始时间', '基础', '0');
INSERT INTO `event_condition_category` VALUES (15, 15, '可信度', '基础', '0');
INSERT INTO `event_condition_category` VALUES (16, 16, '威胁分', '基础', '0');
INSERT INTO `event_condition_category` VALUES (17, 17, '源地址', '源', '1');
INSERT INTO `event_condition_category` VALUES (18, 18, '源主机名', '源', '1');
INSERT INTO `event_condition_category` VALUES (19, 19, '源端口', '源', '1');
INSERT INTO `event_condition_category` VALUES (20, 20, '源地址掩码', '源', '1');
INSERT INTO `event_condition_category` VALUES (21, 21, '源用户', '源', '1');
INSERT INTO `event_condition_category` VALUES (22, 22, '源MAC', '源', '1');
INSERT INTO `event_condition_category` VALUES (23, 23, '源区域', '源', '1');
INSERT INTO `event_condition_category` VALUES (24, 24, '源操作系统', '源', '0');
INSERT INTO `event_condition_category` VALUES (25, 25, '源浏览器', '源', '0');
INSERT INTO `event_condition_category` VALUES (26, 26, '源运营商', '源', '0');
INSERT INTO `event_condition_category` VALUES (27, 27, '目的地址', '目的', '1');
INSERT INTO `event_condition_category` VALUES (28, 28, '目的主机名', '目的', '1');
INSERT INTO `event_condition_category` VALUES (29, 29, '目的端口', '目的', '1');
INSERT INTO `event_condition_category` VALUES (30, 30, '目的地址掩码', '目的', '1');
INSERT INTO `event_condition_category` VALUES (31, 31, '目的用户', '目的', '1');
INSERT INTO `event_condition_category` VALUES (32, 32, '目的MAC', '目的', '1');
INSERT INTO `event_condition_category` VALUES (33, 33, '目的区域', '目的', '1');
INSERT INTO `event_condition_category` VALUES (34, 34, '目的运营商', '目的', '0');
INSERT INTO `event_condition_category` VALUES (35, 35, '采集器名称', '采集器', '1');
INSERT INTO `event_condition_category` VALUES (36, 36, '采集器标识', '采集器', '1');
INSERT INTO `event_condition_category` VALUES (37, 37, '采集器地址', '采集器', '1');
INSERT INTO `event_condition_category` VALUES (38, 38, '采集器所属网络名称', '采集器', '0');
INSERT INTO `event_condition_category` VALUES (39, 39, '发送流量', '流量', '0');
INSERT INTO `event_condition_category` VALUES (40, 40, '发送流量单位', '流量', '0');
INSERT INTO `event_condition_category` VALUES (41, 41, '接收流量', '流量', '0');
INSERT INTO `event_condition_category` VALUES (42, 42, '接收流量单位', '流量', '0');
INSERT INTO `event_condition_category` VALUES (43, 43, '总流量', '流量', '0');
INSERT INTO `event_condition_category` VALUES (44, 44, '总流量单位', '流量', '0');
INSERT INTO `event_condition_category` VALUES (45, 45, '关联对象分类', '审计', '0');
INSERT INTO `event_condition_category` VALUES (46, 46, '关联对象子类', '审计', '0');
INSERT INTO `event_condition_category` VALUES (47, 47, '关联对象名称', '审计', '0');
INSERT INTO `event_condition_category` VALUES (48, 48, '关联对象地址', '审计', '0');
INSERT INTO `event_condition_category` VALUES (49, 49, '关联对象账号', '审计', '0');
INSERT INTO `event_condition_category` VALUES (50, 50, '执行动作账号', '审计', '0');
INSERT INTO `event_condition_category` VALUES (51, 51, '执行动作地址', '审计', '0');
INSERT INTO `event_condition_category` VALUES (52, 52, '执行对象类型', '审计', '0');
INSERT INTO `event_condition_category` VALUES (53, 53, '执行对象名称', '审计', '0');
INSERT INTO `event_condition_category` VALUES (54, 54, '执行对象地址', '审计', '0');
INSERT INTO `event_condition_category` VALUES (55, 55, '动作详细信息', '审计', '0');
INSERT INTO `event_condition_category` VALUES (56, 56, '动作持续时间', '审计', '0');
INSERT INTO `event_condition_category` VALUES (57, 57, '域名', '其他', '1');
INSERT INTO `event_condition_category` VALUES (58, 58, '组名', '其他', '1');
INSERT INTO `event_condition_category` VALUES (59, 59, '网络服务名', '其他', '1');
INSERT INTO `event_condition_category` VALUES (60, 60, '其他地址', '其他', '0');
INSERT INTO `event_condition_category` VALUES (61, 61, '其他端口', '其他', '0');
INSERT INTO `event_condition_category` VALUES (62, 62, '协议', '其他', '1');
INSERT INTO `event_condition_category` VALUES (63, 63, '动作', '其他', '0');
INSERT INTO `event_condition_category` VALUES (64, 64, '动作结果', '其他', '0');
INSERT INTO `event_condition_category` VALUES (65, 65, '相关文件名', '其他', '0');
INSERT INTO `event_condition_category` VALUES (66, 66, '归并时间', '其他', '0');
INSERT INTO `event_condition_category` VALUES (67, 67, '归并数量', '其他', '0');
INSERT INTO `event_condition_category` VALUES (68, 68, '会话编号', '其他', '0');
INSERT INTO `event_condition_category` VALUES (69, 69, '接收时间', '其他', '0');
INSERT INTO `event_condition_category` VALUES (70, 70, '接收日期', '其他', '0');
INSERT INTO `event_condition_category` VALUES (71, 71, '接受日（星期几）', '其他', '0');
INSERT INTO `event_condition_category` VALUES (72, 72, '接收月份', '其他', '0');
INSERT INTO `event_condition_category` VALUES (73, 73, 'URL', '其他', '0');
INSERT INTO `event_condition_category` VALUES (74, 74, '结果码', '其他', '0');

-- ----------------------------
-- Table structure for event_field_config
-- ----------------------------
DROP TABLE IF EXISTS `event_field_config`;
CREATE TABLE `event_field_config`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT 'id',
  `field_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '字段key',
  `field_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '字段值',
  `field_flag` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '是否显示（0-否1-是）',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 71 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of event_field_config
-- ----------------------------
INSERT INTO `event_field_config` VALUES (1, 'event_name', '事件名称', '1');
INSERT INTO `event_field_config` VALUES (2, 'event_type', '事件类型', '1');
INSERT INTO `event_field_config` VALUES (3, 'event_subtype', '事件子类型', '1');
INSERT INTO `event_field_config` VALUES (4, 'severity_level', '严重级别', '1');
INSERT INTO `event_field_config` VALUES (5, 'device_address', '设备地址', '1');
INSERT INTO `event_field_config` VALUES (6, 'received_time', '时间', '1');
INSERT INTO `event_field_config` VALUES (7, 'source_address', '源地址', '0');
INSERT INTO `event_field_config` VALUES (8, 'dest_address', '目的地址', '0');
INSERT INTO `event_field_config` VALUES (9, 'standard_event_code', '标准事件编号', '0');
INSERT INTO `event_field_config` VALUES (10, 'message', '详细信息', '0');
INSERT INTO `event_field_config` VALUES (11, 'original_level', '原始级别', '0');
INSERT INTO `event_field_config` VALUES (12, 'device_type', '设备类型', '0');
INSERT INTO `event_field_config` VALUES (13, 'device_name', '设备名称', '0');
INSERT INTO `event_field_config` VALUES (14, 'product_name', '产品名称', '0');
INSERT INTO `event_field_config` VALUES (15, 'product_version', '产品版本', '0');
INSERT INTO `event_field_config` VALUES (16, 'timestamp', '原始时间', '1');
INSERT INTO `event_field_config` VALUES (17, 'credibility', '可信度', '0');
INSERT INTO `event_field_config` VALUES (18, 'source_hostname', '源主机名', '0');
INSERT INTO `event_field_config` VALUES (19, 'source_port', '源端口', '0');
INSERT INTO `event_field_config` VALUES (20, 'source_user', '源用户', '0');
INSERT INTO `event_field_config` VALUES (21, 'source_mask', '源地址掩码', '0');
INSERT INTO `event_field_config` VALUES (22, 'dest_hostname', '目的主机名', '0');
INSERT INTO `event_field_config` VALUES (23, 'dest_port', '目的端口', '0');
INSERT INTO `event_field_config` VALUES (24, 'dest_user', '目的用户', '0');
INSERT INTO `event_field_config` VALUES (25, 'dest_mask', '目的地址掩码', '0');
INSERT INTO `event_field_config` VALUES (26, 'collector_name', '采集器名称', '0');
INSERT INTO `event_field_config` VALUES (27, 'collector_address', '采集器地址', '0');
INSERT INTO `event_field_config` VALUES (28, 'sent_traffic', '发送流量', '0');
INSERT INTO `event_field_config` VALUES (29, 'sent_traffic_unit', '发送流量单位', '0');
INSERT INTO `event_field_config` VALUES (30, 'received_traffic', '接收流量', '0');
INSERT INTO `event_field_config` VALUES (31, 'received_traffic_unit', '接收流量单位', '0');
INSERT INTO `event_field_config` VALUES (32, 'total_traffic', '总流量', '0');
INSERT INTO `event_field_config` VALUES (33, 'total_traffic_unit', '总流量单位', '0');
INSERT INTO `event_field_config` VALUES (34, 'related_object_category', '行为对象类别', '0');
INSERT INTO `event_field_config` VALUES (35, 'related_object_subcategory', '行为对象子类别', '0');
INSERT INTO `event_field_config` VALUES (36, 'related_object_name', '行为对象名称', '0');
INSERT INTO `event_field_config` VALUES (37, 'related_object_address', '行为对象地址', '0');
INSERT INTO `event_field_config` VALUES (38, 'related_object_account', '行为对象账号', '0');
INSERT INTO `event_field_config` VALUES (39, 'action_account', '执行动作账号', '0');
INSERT INTO `event_field_config` VALUES (40, 'action_address', '执行动作地址', '0');
INSERT INTO `event_field_config` VALUES (41, 'action_object_type', '动作对象类型', '0');
INSERT INTO `event_field_config` VALUES (42, 'action_object_name', '动作对象名称', '0');
INSERT INTO `event_field_config` VALUES (43, 'action_object_address', '动作对象地址', '0');
INSERT INTO `event_field_config` VALUES (44, 'action_details', '动作详细信息', '0');
INSERT INTO `event_field_config` VALUES (45, 'action_duration', '动作持续时间', '0');
INSERT INTO `event_field_config` VALUES (46, 'domain', '域名', '0');
INSERT INTO `event_field_config` VALUES (47, 'group_name', '组名', '0');
INSERT INTO `event_field_config` VALUES (48, 'network_service', '网络服务名', '0');
INSERT INTO `event_field_config` VALUES (49, 'nat_ip', 'NAT IP', '0');
INSERT INTO `event_field_config` VALUES (50, 'other_port', '其他端口', '0');
INSERT INTO `event_field_config` VALUES (51, 'protocol', '协议', '0');
INSERT INTO `event_field_config` VALUES (52, 'action_result', '动作结果', '0');
INSERT INTO `event_field_config` VALUES (53, 'related_filename', '相关文件名', '0');
INSERT INTO `event_field_config` VALUES (54, 'source_country_code', '源国家编号', '0');
INSERT INTO `event_field_config` VALUES (55, 'source_longitude', '源经度', '0');
INSERT INTO `event_field_config` VALUES (56, 'source_city', '源城市', '0');
INSERT INTO `event_field_config` VALUES (57, 'source_province', '源省份', '0');
INSERT INTO `event_field_config` VALUES (58, 'source_country', '源国家', '0');
INSERT INTO `event_field_config` VALUES (59, 'source_latitude', '源纬度', '0');
INSERT INTO `event_field_config` VALUES (60, 'dest_country_code', '目的国家编号', '0');
INSERT INTO `event_field_config` VALUES (61, 'dest_longitude', '目的经度', '0');
INSERT INTO `event_field_config` VALUES (62, 'dest_city', '目的城市', '0');
INSERT INTO `event_field_config` VALUES (63, 'dest_province', '目的省份', '0');
INSERT INTO `event_field_config` VALUES (64, 'dest_country', '目的国家', '0');
INSERT INTO `event_field_config` VALUES (65, 'dest_latitude', '目的纬度', '0');
INSERT INTO `event_field_config` VALUES (66, 'source_org', '源组织', '0');
INSERT INTO `event_field_config` VALUES (67, 'source_org_location', '源组织归属地', '0');
INSERT INTO `event_field_config` VALUES (68, 'dest_org', '目的组织', '0');
INSERT INTO `event_field_config` VALUES (69, 'dest_org_location', '目的组织归属地', '0');
INSERT INTO `event_field_config` VALUES (70, 'action', '动作', '0');

-- ----------------------------
-- Table structure for event_focus_device
-- ----------------------------
DROP TABLE IF EXISTS `event_focus_device`;
CREATE TABLE `event_focus_device`  (
  `device_type_id` int NOT NULL COMMENT '设备类型id',
  `device_type_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '设备类型名称',
  PRIMARY KEY (`device_type_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of event_focus_device
-- ----------------------------
INSERT INTO `event_focus_device` VALUES (1000032, 'Unix/Linux主机');
INSERT INTO `event_focus_device` VALUES (1000033, 'Windows主机');
INSERT INTO `event_focus_device` VALUES (1000034, '网络设备');
INSERT INTO `event_focus_device` VALUES (1000035, '防火墙');
INSERT INTO `event_focus_device` VALUES (1000037, '入侵检测系统');
INSERT INTO `event_focus_device` VALUES (1000038, '入侵防御系统');
INSERT INTO `event_focus_device` VALUES (1000040, 'VPN');
INSERT INTO `event_focus_device` VALUES (1000042, '数据库');

-- ----------------------------
-- Table structure for event_sub_category
-- ----------------------------
DROP TABLE IF EXISTS `event_sub_category`;
CREATE TABLE `event_sub_category`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `category_id` int NULL DEFAULT NULL COMMENT '类型Id',
  `sub_category_id` int NULL DEFAULT NULL COMMENT '子类型Id',
  `sub_category_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '子类型名称',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 160 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '事件子类' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of event_sub_category
-- ----------------------------
INSERT INTO `event_sub_category` VALUES (1, 1, 1, '计算机病毒');
INSERT INTO `event_sub_category` VALUES (2, 1, 2, '网页内嵌恶意代码');
INSERT INTO `event_sub_category` VALUES (3, 1, 3, '木马');
INSERT INTO `event_sub_category` VALUES (4, 1, 4, '蠕虫');
INSERT INTO `event_sub_category` VALUES (5, 1, 5, '僵尸软件');
INSERT INTO `event_sub_category` VALUES (6, 1, 6, '混合攻击程序');
INSERT INTO `event_sub_category` VALUES (7, 1, 7, '其它');
INSERT INTO `event_sub_category` VALUES (8, 1, 86, '可疑通信');
INSERT INTO `event_sub_category` VALUES (9, 1, 87, '挖矿木马');
INSERT INTO `event_sub_category` VALUES (10, 1, 88, '黑客工具');
INSERT INTO `event_sub_category` VALUES (11, 1, 89, '勒索软件');
INSERT INTO `event_sub_category` VALUES (12, 1, 90, 'APT');
INSERT INTO `event_sub_category` VALUES (13, 1, 91, '漏洞利用');
INSERT INTO `event_sub_category` VALUES (14, 1, 92, '远控后门');
INSERT INTO `event_sub_category` VALUES (15, 1, 93, '广告');
INSERT INTO `event_sub_category` VALUES (16, 1, 94, '下载者');
INSERT INTO `event_sub_category` VALUES (17, 2, 8, '拒绝服务攻击');
INSERT INTO `event_sub_category` VALUES (18, 2, 9, '后门攻击');
INSERT INTO `event_sub_category` VALUES (19, 2, 10, '漏洞攻击');
INSERT INTO `event_sub_category` VALUES (20, 2, 11, '网络扫描探测');
INSERT INTO `event_sub_category` VALUES (21, 2, 12, '非法访问');
INSERT INTO `event_sub_category` VALUES (22, 2, 13, '逃避');
INSERT INTO `event_sub_category` VALUES (23, 2, 14, '欺骗攻击');
INSERT INTO `event_sub_category` VALUES (24, 2, 15, '网络钓鱼攻击');
INSERT INTO `event_sub_category` VALUES (25, 2, 16, '流量异常');
INSERT INTO `event_sub_category` VALUES (26, 2, 17, '干扰攻击');
INSERT INTO `event_sub_category` VALUES (27, 2, 18, '其它');
INSERT INTO `event_sub_category` VALUES (28, 2, 95, '命令执行');
INSERT INTO `event_sub_category` VALUES (29, 2, 96, 'CC攻击');
INSERT INTO `event_sub_category` VALUES (30, 2, 97, '反射型DDoS');
INSERT INTO `event_sub_category` VALUES (31, 2, 98, '其他拒绝服务');
INSERT INTO `event_sub_category` VALUES (32, 2, 99, 'XSS攻击');
INSERT INTO `event_sub_category` VALUES (33, 2, 100, 'CSRF攻击');
INSERT INTO `event_sub_category` VALUES (34, 2, 101, 'SQL注入攻击');
INSERT INTO `event_sub_category` VALUES (35, 2, 102, '文件探测');
INSERT INTO `event_sub_category` VALUES (36, 2, 103, '文件上传');
INSERT INTO `event_sub_category` VALUES (37, 2, 104, '文件包含');
INSERT INTO `event_sub_category` VALUES (38, 2, 105, '文件下载');
INSERT INTO `event_sub_category` VALUES (39, 2, 106, '本地漏洞');
INSERT INTO `event_sub_category` VALUES (40, 2, 107, '其他漏洞利用');
INSERT INTO `event_sub_category` VALUES (41, 2, 108, '漏洞扫描');
INSERT INTO `event_sub_category` VALUES (42, 2, 109, '扫描工具');
INSERT INTO `event_sub_category` VALUES (43, 2, 110, '端口扫描');
INSERT INTO `event_sub_category` VALUES (44, 2, 111, 'IP扫描');
INSERT INTO `event_sub_category` VALUES (45, 2, 112, '爬虫');
INSERT INTO `event_sub_category` VALUES (46, 2, 113, '其他扫描探测');
INSERT INTO `event_sub_category` VALUES (47, 3, 19, '信息篡改');
INSERT INTO `event_sub_category` VALUES (48, 3, 20, '信息假冒');
INSERT INTO `event_sub_category` VALUES (49, 3, 21, '信息泄露');
INSERT INTO `event_sub_category` VALUES (50, 3, 22, '信息窃取');
INSERT INTO `event_sub_category` VALUES (51, 3, 23, '信息丢失');
INSERT INTO `event_sub_category` VALUES (52, 3, 24, '其它');
INSERT INTO `event_sub_category` VALUES (53, 4, 25, '垃圾邮件');
INSERT INTO `event_sub_category` VALUES (54, 4, 26, '违法事件');
INSERT INTO `event_sub_category` VALUES (55, 4, 27, '恶意炒作事件');
INSERT INTO `event_sub_category` VALUES (56, 4, 28, '恶意煽动事件');
INSERT INTO `event_sub_category` VALUES (57, 4, 29, '其它');
INSERT INTO `event_sub_category` VALUES (58, 5, 30, '硬件故障');
INSERT INTO `event_sub_category` VALUES (59, 5, 31, '软件故障');
INSERT INTO `event_sub_category` VALUES (60, 5, 32, '外围设备设施故障');
INSERT INTO `event_sub_category` VALUES (61, 5, 33, '网络链路故障');
INSERT INTO `event_sub_category` VALUES (62, 5, 34, '人为破坏故障');
INSERT INTO `event_sub_category` VALUES (63, 5, 35, '资源告警');
INSERT INTO `event_sub_category` VALUES (64, 5, 36, '其它');
INSERT INTO `event_sub_category` VALUES (65, 6, 37, '用户登录');
INSERT INTO `event_sub_category` VALUES (66, 6, 38, '用户注销');
INSERT INTO `event_sub_category` VALUES (67, 6, 39, '用户切换');
INSERT INTO `event_sub_category` VALUES (68, 6, 40, '其它');
INSERT INTO `event_sub_category` VALUES (69, 7, 41, '帐户新建');
INSERT INTO `event_sub_category` VALUES (70, 7, 42, '帐户删除');
INSERT INTO `event_sub_category` VALUES (71, 7, 43, '帐户失效');
INSERT INTO `event_sub_category` VALUES (72, 7, 44, '帐户变更');
INSERT INTO `event_sub_category` VALUES (73, 7, 45, '口令变更');
INSERT INTO `event_sub_category` VALUES (74, 7, 46, '权限变更');
INSERT INTO `event_sub_category` VALUES (75, 7, 47, '其它');
INSERT INTO `event_sub_category` VALUES (76, 8, 48, '策略新建');
INSERT INTO `event_sub_category` VALUES (77, 8, 49, '策略删除');
INSERT INTO `event_sub_category` VALUES (78, 8, 50, '策略失效');
INSERT INTO `event_sub_category` VALUES (79, 8, 51, '策略修改');
INSERT INTO `event_sub_category` VALUES (80, 8, 52, '策略应用');
INSERT INTO `event_sub_category` VALUES (81, 8, 53, '其它');
INSERT INTO `event_sub_category` VALUES (82, 9, 54, '信息');
INSERT INTO `event_sub_category` VALUES (83, 9, 55, '配置变更');
INSERT INTO `event_sub_category` VALUES (84, 9, 56, '状态变更');
INSERT INTO `event_sub_category` VALUES (85, 9, 57, '状态跟踪');
INSERT INTO `event_sub_category` VALUES (86, 9, 58, '资源调用');
INSERT INTO `event_sub_category` VALUES (87, 9, 59, '设备重启');
INSERT INTO `event_sub_category` VALUES (88, 9, 60, '启动');
INSERT INTO `event_sub_category` VALUES (89, 9, 61, '停止关闭');
INSERT INTO `event_sub_category` VALUES (90, 9, 62, '安装维护');
INSERT INTO `event_sub_category` VALUES (91, 9, 63, '其它');
INSERT INTO `event_sub_category` VALUES (92, 10, 64, '文件新增');
INSERT INTO `event_sub_category` VALUES (93, 10, 65, '文件删除');
INSERT INTO `event_sub_category` VALUES (94, 10, 66, '文件修改');
INSERT INTO `event_sub_category` VALUES (95, 10, 67, '文件复制');
INSERT INTO `event_sub_category` VALUES (96, 10, 68, '数据新增');
INSERT INTO `event_sub_category` VALUES (97, 10, 69, '数据删除');
INSERT INTO `event_sub_category` VALUES (98, 10, 70, '数据修改');
INSERT INTO `event_sub_category` VALUES (99, 10, 71, '数据备份/还原');
INSERT INTO `event_sub_category` VALUES (100, 10, 72, '其它');
INSERT INTO `event_sub_category` VALUES (101, 11, 73, '连接接受');
INSERT INTO `event_sub_category` VALUES (102, 11, 74, '连接拒绝');
INSERT INTO `event_sub_category` VALUES (103, 11, 75, '主动连接');
INSERT INTO `event_sub_category` VALUES (104, 11, 76, '连接断开');
INSERT INTO `event_sub_category` VALUES (105, 11, 77, '连接中断');
INSERT INTO `event_sub_category` VALUES (106, 11, 78, '连接超时');
INSERT INTO `event_sub_category` VALUES (107, 11, 79, '连接跟踪');
INSERT INTO `event_sub_category` VALUES (108, 11, 80, '其它');
INSERT INTO `event_sub_category` VALUES (109, 11, 85, '流量');
INSERT INTO `event_sub_category` VALUES (110, 11, 124, '地址转换');
INSERT INTO `event_sub_category` VALUES (111, 12, 81, '用户命令');
INSERT INTO `event_sub_category` VALUES (112, 13, 82, '漏洞');
INSERT INTO `event_sub_category` VALUES (113, 13, 83, '配置脆弱性');
INSERT INTO `event_sub_category` VALUES (114, 14, 114, '身份信息泄露');
INSERT INTO `event_sub_category` VALUES (115, 14, 115, '帐号信息泄漏');
INSERT INTO `event_sub_category` VALUES (116, 14, 116, '财务信息泄漏');
INSERT INTO `event_sub_category` VALUES (117, 14, 117, '配置信息泄漏');
INSERT INTO `event_sub_category` VALUES (118, 14, 118, '其他信息泄漏');
INSERT INTO `event_sub_category` VALUES (119, 15, 119, '钓鱼邮件');
INSERT INTO `event_sub_category` VALUES (120, 15, 120, '垃圾邮件');
INSERT INTO `event_sub_category` VALUES (121, 15, 121, '邮件伪造');
INSERT INTO `event_sub_category` VALUES (122, 15, 122, '恶意附件');
INSERT INTO `event_sub_category` VALUES (123, 15, 123, '其他邮件异常');
INSERT INTO `event_sub_category` VALUES (124, 16, 125, 'webshell上传');
INSERT INTO `event_sub_category` VALUES (125, 16, 126, 'webshell连接');
INSERT INTO `event_sub_category` VALUES (126, 16, 127, 'webshell后门');
INSERT INTO `event_sub_category` VALUES (127, 16, 128, '其他webshell');
INSERT INTO `event_sub_category` VALUES (128, 17, 129, 'Windows爆破');
INSERT INTO `event_sub_category` VALUES (129, 17, 130, 'Linux爆破');
INSERT INTO `event_sub_category` VALUES (130, 17, 131, '应用爆破');
INSERT INTO `event_sub_category` VALUES (131, 17, 132, '其他暴力破解');
INSERT INTO `event_sub_category` VALUES (132, 18, 133, '系统日志');
INSERT INTO `event_sub_category` VALUES (133, 18, 134, '安全日志');
INSERT INTO `event_sub_category` VALUES (134, 18, 135, '应用程序日志');
INSERT INTO `event_sub_category` VALUES (135, 18, 136, '其他');
INSERT INTO `event_sub_category` VALUES (136, 19, 137, '系统日志');
INSERT INTO `event_sub_category` VALUES (137, 19, 138, '安全日志');
INSERT INTO `event_sub_category` VALUES (138, 19, 139, '应用程序日志');
INSERT INTO `event_sub_category` VALUES (139, 19, 140, '其他');
INSERT INTO `event_sub_category` VALUES (140, 20, 141, '性能监控');
INSERT INTO `event_sub_category` VALUES (141, 20, 142, '故障监控');
INSERT INTO `event_sub_category` VALUES (142, 20, 143, '操作审计');
INSERT INTO `event_sub_category` VALUES (143, 20, 144, '其他行为');
INSERT INTO `event_sub_category` VALUES (144, 21, 145, '登录异常');
INSERT INTO `event_sub_category` VALUES (145, 21, 146, '异地登录');
INSERT INTO `event_sub_category` VALUES (146, 21, 147, '弱密码');
INSERT INTO `event_sub_category` VALUES (147, 21, 148, '其他帐号异常');
INSERT INTO `event_sub_category` VALUES (148, 22, 149, '扫描行为');
INSERT INTO `event_sub_category` VALUES (149, 22, 150, '攻击行为');
INSERT INTO `event_sub_category` VALUES (150, 22, 151, '可疑进程');
INSERT INTO `event_sub_category` VALUES (151, 22, 152, '提权');
INSERT INTO `event_sub_category` VALUES (152, 22, 153, '其他异常行为');
INSERT INTO `event_sub_category` VALUES (153, 23, 154, '违规软件');
INSERT INTO `event_sub_category` VALUES (154, 23, 155, '违规访问');
INSERT INTO `event_sub_category` VALUES (155, 23, 156, '违规操作');
INSERT INTO `event_sub_category` VALUES (156, 23, 157, '业务违规');
INSERT INTO `event_sub_category` VALUES (157, 23, 158, '其他违规行为');
INSERT INTO `event_sub_category` VALUES (158, 24, 159, '可疑活动');
INSERT INTO `event_sub_category` VALUES (159, 50, 84, '其他');

-- ----------------------------
-- Table structure for export_task_manage
-- ----------------------------
DROP TABLE IF EXISTS `export_task_manage`;
CREATE TABLE `export_task_manage`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT 'id',
  `task_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '任务名称',
  `task_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '任务类型',
  `query_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '查询类型',
  `query_period` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '查询时间段',
  `query_condition` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '查询条件',
  `task_state` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '任务状态(0-未开始1-进行中2-完成)',
  `file_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '文件名称',
  `file_path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '文件路径',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `create_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 57 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '导出任务管理' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of export_task_manage
-- ----------------------------

-- ----------------------------
-- Table structure for licence_upgrade_info
-- ----------------------------
DROP TABLE IF EXISTS `licence_upgrade_info`;
CREATE TABLE `licence_upgrade_info`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '许可证ID',
  `upgrade_date` datetime NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '描述',
  `create_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 19 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '许可证升级列表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of licence_upgrade_info
-- ----------------------------

-- ----------------------------
-- Table structure for log_backup_record
-- ----------------------------
DROP TABLE IF EXISTS `log_backup_record`;
CREATE TABLE `log_backup_record`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT 'id',
  `mode` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '方式',
  `host` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '主机地址',
  `port` int NULL DEFAULT NULL COMMENT '端口',
  `account` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '账号',
  `password` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '密码',
  `path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '路径',
  `status` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '状态（0-失败1-成功）',
  `category` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '类别（0-备份1-恢复）',
  `progress` int NULL DEFAULT NULL COMMENT '进度',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 262 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of log_backup_record
-- ----------------------------

-- ----------------------------
-- Table structure for log_settings_server
-- ----------------------------
DROP TABLE IF EXISTS `log_settings_server`;
CREATE TABLE `log_settings_server`  (
  `id` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `is_enabled` int NULL DEFAULT 0 COMMENT '是否启用：1、是 0、否',
  `docking_standards` int NULL DEFAULT 0 COMMENT '对接标准： 0、默认 1、国电网rzsj',
  `ip_address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT 'IP地址',
  `ip_port` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '端口',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '日志服务器' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of log_settings_server
-- ----------------------------
INSERT INTO `log_settings_server` VALUES ('1', 0, 0, NULL, '514');

-- ----------------------------
-- Table structure for mon_network_inter
-- ----------------------------
DROP TABLE IF EXISTS `mon_network_inter`;
CREATE TABLE `mon_network_inter`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '网口系统名称',
  `nickname` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '网口显示名称',
  `ismgr` int NOT NULL DEFAULT 0 COMMENT '是否管理口',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of mon_network_inter
-- ----------------------------

-- ----------------------------
-- Table structure for report_example
-- ----------------------------
DROP TABLE IF EXISTS `report_example`;
CREATE TABLE `report_example`  (
  `id` int NOT NULL,
  `example_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '实例名称',
  `report_group_id` int NULL DEFAULT NULL COMMENT '分组设备ID',
  `public_field_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '设备类型ID',
  `statistics_field` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '统计字段',
  `describe` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '描述',
  `create_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人',
  `create_time` datetime NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '报表实例表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of report_example
-- ----------------------------
INSERT INTO `report_example` VALUES (1, 'Unix类主机日志分布日报', 1, '1000032', '事件名称,事件类型,事件严重级别,设备地址', NULL, '超级管理员', '2024-12-17 17:22:28');
INSERT INTO `report_example` VALUES (2, 'Windows类主机日志分布日报', 1, '1000033', '事件名称,事件类型,事件严重级别,设备地址', NULL, '超级管理员', '2024-11-05 17:24:17');
INSERT INTO `report_example` VALUES (3, '网络设备类日志分布日报', 2, '1000034', '事件名称,事件类型,事件严重级别,设备地址', NULL, '超级管理员', '2024-12-13 10:19:11');
INSERT INTO `report_example` VALUES (4, '网络安全类日志分布日报', 3, '1000035,1000036,1000037,1000038,1000041,1000044', '事件名称,事件类型,事件严重级别,设备地址', NULL, '超级管理员', '2024-11-11 17:05:46');
INSERT INTO `report_example` VALUES (5, '入侵防御系统类日志分布日报', 3, '1000037,1000038', '事件名称,事件类型,事件严重级别,设备地址', NULL, '超级管理员', '2024-10-29 18:18:51');
INSERT INTO `report_example` VALUES (6, '防火墙类日志分布日报', 3, '1000035', '事件名称,事件类型,事件严重级别,设备地址', NULL, '超级管理员', '2024-10-29 18:18:51');
INSERT INTO `report_example` VALUES (7, '防病毒类日志分布日报', 3, '1000041', '事件名称,事件类型,事件严重级别,设备地址', NULL, '超级管理员', '2024-11-29 09:07:01');
INSERT INTO `report_example` VALUES (8, '堡垒机类日志分布日报', 3, '1000044', '事件名称,事件类型,事件严重级别,设备地址', NULL, '超级管理员', '2024-10-29 18:18:51');
INSERT INTO `report_example` VALUES (9, '数据库类日志分布日报', 4, '1000042', '事件名称,事件类型,事件严重级别,设备地址', NULL, '超级管理员', '2024-10-29 18:18:51');
INSERT INTO `report_example` VALUES (10, '应用类日志分布日报', 5, '1000045', '事件名称,事件类型,事件严重级别,设备地址', NULL, '超级管理员', '2024-10-29 18:18:51');

-- ----------------------------
-- Table structure for report_previous
-- ----------------------------
DROP TABLE IF EXISTS `report_previous`;
CREATE TABLE `report_previous`  (
  `id` varchar(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '历次报表ID',
  `report_date` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '报表时间',
  `report_example_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '报表实例ID',
  `file_address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '报表文件存储目录',
  `create_time` datetime NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '历次报表列表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of report_previous
-- ----------------------------

-- ----------------------------
-- Table structure for rzcj_access_method_dict
-- ----------------------------
DROP TABLE IF EXISTS `rzcj_access_method_dict`;
CREATE TABLE `rzcj_access_method_dict`  (
  `id` int NOT NULL COMMENT 'ID',
  `access_method_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '接入方式名称',
  `alias` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '别名',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '接入方式列表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of rzcj_access_method_dict
-- ----------------------------
INSERT INTO `rzcj_access_method_dict` VALUES (1, 'Syslog', '日志采集器(SYSLOG)');
INSERT INTO `rzcj_access_method_dict` VALUES (2, 'SNMP Trap', '日志采集器(SNMP Trap)');
INSERT INTO `rzcj_access_method_dict` VALUES (3, '数据库', '日志采集器(DATABASE)');

-- ----------------------------
-- Table structure for rzcj_acquisition_controller
-- ----------------------------
DROP TABLE IF EXISTS `rzcj_acquisition_controller`;
CREATE TABLE `rzcj_acquisition_controller`  (
  `id` varchar(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `component_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '组件名称',
  `access_method` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '接入方式',
  `standard_strategy` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '标准化策略',
  `ip_range` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT 'IP范围',
  `database_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '数据库类型',
  `port` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '端口',
  `account_number` varchar(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '账号',
  `password` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '口令',
  `database_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '数据库名称',
  `table_view_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '表名或视图名',
  `seq_field_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '序列字段名称',
  `seq_field_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '序列字段类型',
  `query_fields` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '查询字段',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '描述',
  `is_enable` int NULL DEFAULT 1 COMMENT '是否启用 1： 运行正常 2：运行异常 3：停用',
  `create_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人',
  `create_time` datetime NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人',
  `update_time` datetime NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '日志采集控制器' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of rzcj_acquisition_controller
-- ----------------------------

-- ----------------------------
-- Table structure for rzcj_configure
-- ----------------------------
DROP TABLE IF EXISTS `rzcj_configure`;
CREATE TABLE `rzcj_configure`  (
  `id` int NOT NULL COMMENT 'ID',
  `controller_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '控制器名称',
  `ip_address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT 'IP地址段',
  `update_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人',
  `update_time` datetime NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '采集配置表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of rzcj_configure
-- ----------------------------
INSERT INTO `rzcj_configure` VALUES (1, 'CC-b51fd0ad-c273-4010-9adb-9590f1404ec1', '0.0.0.0/0', 'admin', '2024-12-17 17:34:13');

-- ----------------------------
-- Table structure for rzcj_database_type_dict
-- ----------------------------
DROP TABLE IF EXISTS `rzcj_database_type_dict`;
CREATE TABLE `rzcj_database_type_dict`  (
  `id` int NOT NULL,
  `database_type_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '数据库类型名称',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '数据库类型列表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of rzcj_database_type_dict
-- ----------------------------
INSERT INTO `rzcj_database_type_dict` VALUES (3, 'Oracle');
INSERT INTO `rzcj_database_type_dict` VALUES (4, 'MySQL');

-- ----------------------------
-- Table structure for rzcj_standard_strategy
-- ----------------------------
DROP TABLE IF EXISTS `rzcj_standard_strategy`;
CREATE TABLE `rzcj_standard_strategy`  (
  `id` varchar(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'ID',
  `standard_strategy_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '标准化策略名称',
  `applicable_system` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '适用系统',
  `is_built_in` int NULL DEFAULT NULL COMMENT '是否内置 1：是、0：自定义',
  `remark` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '描述',
  `file_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '策略对应文件名称',
  `update_time` datetime NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '最后修改时间',
  `update_by` varchar(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '采集策略表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of rzcj_standard_strategy
-- ----------------------------
INSERT INTO `rzcj_standard_strategy` VALUES ('1001', '泓盈防火墙', '', 1, '', 'hy_fw', '2024-11-13 17:41:03', 'admin');
INSERT INTO `rzcj_standard_strategy` VALUES ('1002', '泓盈IDS', '', 1, '', 'hy_ids', '2024-11-13 17:41:03', 'admin');
INSERT INTO `rzcj_standard_strategy` VALUES ('1003', 'Linux', '', 1, '', 'linux', '2024-11-13 17:41:03', 'admin');
INSERT INTO `rzcj_standard_strategy` VALUES ('1004', 'Windows', '', 1, '', 'windows', '2024-11-13 17:41:03', 'admin');
INSERT INTO `rzcj_standard_strategy` VALUES ('1005', '1084-AV', '', 1, '', '1084-AV', '2024-11-13 17:41:03', 'admin');
INSERT INTO `rzcj_standard_strategy` VALUES ('1006', '1084-BID', '', 1, '', '1084-BID', '2024-11-13 17:41:03', 'admin');
INSERT INTO `rzcj_standard_strategy` VALUES ('1007', '1084-DB', '', 1, '', '1084-DB', '2024-11-13 17:41:03', 'admin');
INSERT INTO `rzcj_standard_strategy` VALUES ('1008', '1084-DCD-1', '', 1, '', '1084-DCD-1', '2024-11-13 17:41:03', 'admin');
INSERT INTO `rzcj_standard_strategy` VALUES ('1009', '1084-DCD-2', '', 1, '', '1084-DCD-2', '2024-11-13 17:41:03', 'admin');
INSERT INTO `rzcj_standard_strategy` VALUES ('1010', '1084-FID', '', 1, '', '1084-FID', '2024-11-13 17:41:03', 'admin');
INSERT INTO `rzcj_standard_strategy` VALUES ('1011', '1084-FW', '', 1, '', '1084-FW', '2024-11-13 17:41:03', 'admin');
INSERT INTO `rzcj_standard_strategy` VALUES ('1012', '1084-IDS', '', 1, '', '1084-IDS', '2024-11-13 17:41:03', 'admin');
INSERT INTO `rzcj_standard_strategy` VALUES ('1013', '1084-SVR-1', '', 1, '', '1084-SVR-1', '2024-11-13 17:41:03', 'admin');
INSERT INTO `rzcj_standard_strategy` VALUES ('1014', '1084-SVR-2', '', 1, '', '1084-SVR-2', '2024-11-13 17:41:03', 'admin');
INSERT INTO `rzcj_standard_strategy` VALUES ('1015', '1084-VEAD', '', 1, '', '1084-VEAD', '2024-11-13 17:41:03', 'admin');
INSERT INTO `rzcj_standard_strategy` VALUES ('1016', 'Centos-Linux', '', 1, '', 'Centos-Linux', '2024-11-13 17:41:03', 'admin');
INSERT INTO `rzcj_standard_strategy` VALUES ('1017', 'Debian-Linux', '', 1, '', 'Debian-Linux', '2024-11-13 17:41:03', 'admin');
INSERT INTO `rzcj_standard_strategy` VALUES ('1018', 'Fedora-Linux', '', 1, '', 'Fedora-Linux', '2024-11-13 17:41:03', 'admin');
INSERT INTO `rzcj_standard_strategy` VALUES ('1019', 'solaris 10-Unix', '', 1, '', 'solaris 10-Unix', '2024-11-13 17:41:03', 'admin');
INSERT INTO `rzcj_standard_strategy` VALUES ('1020', 'SunOS-Linux', '', 1, '', 'SunOS-Linux', '2024-11-13 17:41:03', 'admin');
INSERT INTO `rzcj_standard_strategy` VALUES ('1021', 'Suse-Linux', '', 1, '', 'Suse-Linux', '2024-11-13 17:41:03', 'admin');
INSERT INTO `rzcj_standard_strategy` VALUES ('1022', 'Ubuntu-Linux', '', 1, '', 'Ubuntu-Linux', '2024-11-13 17:41:03', 'admin');
INSERT INTO `rzcj_standard_strategy` VALUES ('1023', '宝讯-BX5024S', '', 1, '', '宝讯-BX5024S', '2024-11-13 17:41:03', 'admin');
INSERT INTO `rzcj_standard_strategy` VALUES ('1024', '博维亚讯-IAS-3028', '', 1, '', '博维亚讯-IAS-3028', '2024-11-13 17:41:03', 'admin');
INSERT INTO `rzcj_standard_strategy` VALUES ('1025', '迪普-FW1000-GC-N', '', 1, '', '迪普-FW1000-GC-N', '2024-11-13 17:41:03', 'admin');
INSERT INTO `rzcj_standard_strategy` VALUES ('1026', '东软-5120', '', 1, '', '东软-5120', '2024-11-13 17:41:03', 'admin');
INSERT INTO `rzcj_standard_strategy` VALUES ('1027', '东软-5200', '', 1, '', '东软-5200', '2024-11-13 17:41:03', 'admin');
INSERT INTO `rzcj_standard_strategy` VALUES ('1028', '东软-FW5200-GD', '', 1, '', '东软-FW5200-GD', '2024-11-13 17:41:03', 'admin');
INSERT INTO `rzcj_standard_strategy` VALUES ('1029', '东土-SICOM2024M', '', 1, '', '东土-SICOM2024M', '2024-11-13 17:41:03', 'admin');
INSERT INTO `rzcj_standard_strategy` VALUES ('1030', '东土-SICOM3024', '', 1, '', '东土-SICOM3024', '2024-11-13 17:41:03', 'admin');
INSERT INTO `rzcj_standard_strategy` VALUES ('1031', '广州兆和-safe', '', 1, '', '广州兆和-safe', '2024-11-13 17:41:03', 'admin');
INSERT INTO `rzcj_standard_strategy` VALUES ('1032', '国电南思-NSM800', '', 1, '', '国电南思-NSM800', '2024-11-13 17:41:03', 'admin');
INSERT INTO `rzcj_standard_strategy` VALUES ('1033', '赫斯曼-MACH4001', '', 1, '', '赫斯曼-MACH4001', '2024-11-13 17:41:03', 'admin');
INSERT INTO `rzcj_standard_strategy` VALUES ('1034', '红帽-Linux', '', 1, '', '红帽-Linux', '2024-11-13 17:41:03', 'admin');
INSERT INTO `rzcj_standard_strategy` VALUES ('1035', '华三-3610', '', 1, '', '华三-3610', '2024-11-13 17:41:03', 'admin');
INSERT INTO `rzcj_standard_strategy` VALUES ('1036', '华三-CSA1800A', '', 1, '', '华三-CSA1800A', '2024-11-13 17:41:03', 'admin');
INSERT INTO `rzcj_standard_strategy` VALUES ('1037', '华三-CSA1800B', '', 1, '', '华三-CSA1800B', '2024-11-13 17:41:03', 'admin');
INSERT INTO `rzcj_standard_strategy` VALUES ('1038', '华三-CSA1800C', '', 1, '', '华三-CSA1800C', '2024-11-13 17:41:03', 'admin');
INSERT INTO `rzcj_standard_strategy` VALUES ('1039', '华三-F1020', '', 1, '', '华三-F1020', '2024-11-13 17:41:03', 'admin');
INSERT INTO `rzcj_standard_strategy` VALUES ('1040', '华三-LS-5130S-28S-HI', '', 1, '', '华三-LS-5130S-28S-HI', '2024-11-13 17:41:03', 'admin');
INSERT INTO `rzcj_standard_strategy` VALUES ('1041', '华三-LS-5500-28C-EI', '', 1, '', '华三-LS-5500-28C-EI', '2024-11-13 17:41:03', 'admin');
INSERT INTO `rzcj_standard_strategy` VALUES ('1042', '华三-LS-7506E-NonPoE', '', 1, '', '华三-LS-7506E-NonPoE', '2024-11-13 17:41:03', 'admin');
INSERT INTO `rzcj_standard_strategy` VALUES ('1043', '华三-S10508', '', 1, '', '华三-S10508', '2024-11-13 17:41:03', 'admin');
INSERT INTO `rzcj_standard_strategy` VALUES ('1044', '华三-S10510', '', 1, '', '华三-S10510', '2024-11-13 17:41:03', 'admin');
INSERT INTO `rzcj_standard_strategy` VALUES ('1045', '华三-S1824G', '', 1, '', '华三-S1824G', '2024-11-13 17:41:03', 'admin');
INSERT INTO `rzcj_standard_strategy` VALUES ('1046', '华三-S3100', '', 1, '', '华三-S3100', '2024-11-13 17:41:03', 'admin');
INSERT INTO `rzcj_standard_strategy` VALUES ('1047', '华三-S3600V2', '', 1, '', '华三-S3600V2', '2024-11-13 17:41:03', 'admin');
INSERT INTO `rzcj_standard_strategy` VALUES ('1048', '华三-S5100', '', 1, '', '华三-S5100', '2024-11-13 17:41:03', 'admin');
INSERT INTO `rzcj_standard_strategy` VALUES ('1049', '华三-S5120-28C-EI', '', 1, '', '华三-S5120-28C-EI', '2024-11-13 17:41:03', 'admin');
INSERT INTO `rzcj_standard_strategy` VALUES ('1050', '华三-S5120-28P-SI', '', 1, '', '华三-S5120-28P-SI', '2024-11-13 17:41:03', 'admin');
INSERT INTO `rzcj_standard_strategy` VALUES ('1051', '华三-S5130', '', 1, '', '华三-S5130', '2024-11-13 17:41:03', 'admin');
INSERT INTO `rzcj_standard_strategy` VALUES ('1052', '华三-S5130-30C-HI', '', 1, '', '华三-S5130-30C-HI', '2024-11-13 17:41:03', 'admin');
INSERT INTO `rzcj_standard_strategy` VALUES ('1053', '华三-S5130-54C-HI', '', 1, '', '华三-S5130-54C-HI', '2024-11-13 17:41:03', 'admin');
INSERT INTO `rzcj_standard_strategy` VALUES ('1054', '华三-S5500', '', 1, '', '华三-S5500', '2024-11-13 17:41:03', 'admin');
INSERT INTO `rzcj_standard_strategy` VALUES ('1055', '华三-S5500 SI', '', 1, '', '华三-S5500 SI', '2024-11-13 17:41:03', 'admin');
INSERT INTO `rzcj_standard_strategy` VALUES ('1056', '华三-S5500-52C-EI', '', 1, '', '华三-S5500-52C-EI', '2024-11-13 17:41:03', 'admin');
INSERT INTO `rzcj_standard_strategy` VALUES ('1057', '华三-S5500-52C-SI', '', 1, '', '华三-S5500-52C-SI', '2024-11-13 17:41:03', 'admin');
INSERT INTO `rzcj_standard_strategy` VALUES ('1058', '华三-S5560', '', 1, '', '华三-S5560', '2024-11-13 17:41:03', 'admin');
INSERT INTO `rzcj_standard_strategy` VALUES ('1059', '华三-S5720-56C-EI-AC', '', 1, '', '华三-S5720-56C-EI-AC', '2024-11-13 17:41:03', 'admin');
INSERT INTO `rzcj_standard_strategy` VALUES ('1060', '华三-S5800', '', 1, '', '华三-S5800', '2024-11-13 17:41:03', 'admin');
INSERT INTO `rzcj_standard_strategy` VALUES ('1061', '华三-S5830-52SC', '', 1, '', '华三-S5830-52SC', '2024-11-13 17:41:03', 'admin');
INSERT INTO `rzcj_standard_strategy` VALUES ('1062', '华三-S7502E', '', 1, '', '华三-S7502E', '2024-11-13 17:41:03', 'admin');
INSERT INTO `rzcj_standard_strategy` VALUES ('1063', '华三-S7503E-S', '', 1, '', '华三-S7503E-S', '2024-11-13 17:41:03', 'admin');
INSERT INTO `rzcj_standard_strategy` VALUES ('1064', '华三-S7506E', '', 1, '', '华三-S7506E', '2024-11-13 17:41:03', 'admin');
INSERT INTO `rzcj_standard_strategy` VALUES ('1065', '华三-S9512E-IRF', '', 1, '', '华三-S9512E-IRF', '2024-11-13 17:41:03', 'admin');
INSERT INTO `rzcj_standard_strategy` VALUES ('1066', '华三-SecPath F100-A', '', 1, '', '华三-SecPath F100-A', '2024-11-13 17:41:03', 'admin');
INSERT INTO `rzcj_standard_strategy` VALUES ('1067', '华为-AR2200', '', 1, '', '华为-AR2200', '2024-11-13 17:41:03', 'admin');
INSERT INTO `rzcj_standard_strategy` VALUES ('1068', '华为-AR2240E', '', 1, '', '华为-AR2240E', '2024-11-13 17:41:03', 'admin');
INSERT INTO `rzcj_standard_strategy` VALUES ('1069', '华为-AR2500', '', 1, '', '华为-AR2500', '2024-11-13 17:41:03', 'admin');
INSERT INTO `rzcj_standard_strategy` VALUES ('1070', '华为-CE5855-48T4S2Q-EI', '', 1, '', '华为-CE5855-48T4S2Q-EI', '2024-11-13 17:41:03', 'admin');
INSERT INTO `rzcj_standard_strategy` VALUES ('1071', '华为-EUDEMON 100E', '', 1, '', '华为-EUDEMON 100E', '2024-11-13 17:41:03', 'admin');
INSERT INTO `rzcj_standard_strategy` VALUES ('1072', '华为-Quidway E100E', '', 1, '', '华为-Quidway E100E', '2024-11-13 17:41:03', 'admin');
INSERT INTO `rzcj_standard_strategy` VALUES ('1073', '华为-S2700', '', 1, '', '华为-S2700', '2024-11-13 17:41:03', 'admin');
INSERT INTO `rzcj_standard_strategy` VALUES ('1074', '华为-S2750', '', 1, '', '华为-S2750', '2024-11-13 17:41:03', 'admin');
INSERT INTO `rzcj_standard_strategy` VALUES ('1075', '华为-S3050', '', 1, '', '华为-S3050', '2024-11-13 17:41:03', 'admin');
INSERT INTO `rzcj_standard_strategy` VALUES ('1076', '华为-S3300', '', 1, '', '华为-S3300', '2024-11-13 17:41:03', 'admin');
INSERT INTO `rzcj_standard_strategy` VALUES ('1077', '华为-S3700', '', 1, '', '华为-S3700', '2024-11-13 17:41:03', 'admin');
INSERT INTO `rzcj_standard_strategy` VALUES ('1078', '华为-S5300', '', 1, '', '华为-S5300', '2024-11-13 17:41:03', 'admin');
INSERT INTO `rzcj_standard_strategy` VALUES ('1079', '华为-S5328', '', 1, '', '华为-S5328', '2024-11-13 17:41:03', 'admin');
INSERT INTO `rzcj_standard_strategy` VALUES ('1080', '华为-S5328C', '', 1, '', '华为-S5328C', '2024-11-13 17:41:03', 'admin');
INSERT INTO `rzcj_standard_strategy` VALUES ('1081', '华为-S5700', '', 1, '', '华为-S5700', '2024-11-13 17:41:03', 'admin');
INSERT INTO `rzcj_standard_strategy` VALUES ('1082', '华为-S5720', '', 1, '', '华为-S5720', '2024-11-13 17:41:03', 'admin');
INSERT INTO `rzcj_standard_strategy` VALUES ('1083', '华为-S5720-28X-SI', '', 1, '', '华为-S5720-28X-SI', '2024-11-13 17:41:03', 'admin');
INSERT INTO `rzcj_standard_strategy` VALUES ('1084', '华为-S7706', '', 1, '', '华为-S7706', '2024-11-13 17:41:03', 'admin');
INSERT INTO `rzcj_standard_strategy` VALUES ('1085', '华为-USG2000', '', 1, '', '华为-USG2000', '2024-11-13 17:41:03', 'admin');
INSERT INTO `rzcj_standard_strategy` VALUES ('1086', '华为-USG2160', '', 1, '', '华为-USG2160', '2024-11-13 17:41:03', 'admin');
INSERT INTO `rzcj_standard_strategy` VALUES ('1087', '华为-USG2210', '', 1, '', '华为-USG2210', '2024-11-13 17:41:03', 'admin');
INSERT INTO `rzcj_standard_strategy` VALUES ('1088', '华为-USG5150', '', 1, '', '华为-USG5150', '2024-11-13 17:41:03', 'admin');
INSERT INTO `rzcj_standard_strategy` VALUES ('1089', '华为-USG5150HSR', '', 1, '', '华为-USG5150HSR', '2024-11-13 17:41:03', 'admin');
INSERT INTO `rzcj_standard_strategy` VALUES ('1090', '华为-USG550', '', 1, '', '华为-USG550', '2024-11-13 17:41:03', 'admin');
INSERT INTO `rzcj_standard_strategy` VALUES ('1091', '华为-USG6350', '', 1, '', '华为-USG6350', '2024-11-13 17:41:03', 'admin');
INSERT INTO `rzcj_standard_strategy` VALUES ('1092', '华为-USG6550', '', 1, '', '华为-USG6550', '2024-11-13 17:41:03', 'admin');
INSERT INTO `rzcj_standard_strategy` VALUES ('1093', '华为-USG9000', '', 1, '', '华为-USG9000', '2024-11-13 17:41:03', 'admin');
INSERT INTO `rzcj_standard_strategy` VALUES ('1094', '华为-USG9520', '', 1, '', '华为-USG9520', '2024-11-13 17:41:03', 'admin');
INSERT INTO `rzcj_standard_strategy` VALUES ('1095', '华为-USG9560', '', 1, '', '华为-USG9560', '2024-11-13 17:41:03', 'admin');
INSERT INTO `rzcj_standard_strategy` VALUES ('1096', '惠普-Unix', '', 1, '', '惠普-Unix', '2024-11-13 17:41:03', 'admin');
INSERT INTO `rzcj_standard_strategy` VALUES ('1097', '科东-PSTunnel-2000', '', 1, '', '科东-PSTunnel-2000', '2024-11-13 17:41:03', 'admin');
INSERT INTO `rzcj_standard_strategy` VALUES ('1098', '罗杰康-RSG2100', '', 1, '', '罗杰康-RSG2100', '2024-11-13 17:41:03', 'admin');
INSERT INTO `rzcj_standard_strategy` VALUES ('1099', '罗杰康-RSG2300', '', 1, '', '罗杰康-RSG2300', '2024-11-13 17:41:03', 'admin');
INSERT INTO `rzcj_standard_strategy` VALUES ('1100', '罗杰康-RSG2300NC', '', 1, '', '罗杰康-RSG2300NC', '2024-11-13 17:41:03', 'admin');
INSERT INTO `rzcj_standard_strategy` VALUES ('1101', '罗杰康-其他', '', 1, '', '罗杰康-其他', '2024-11-13 17:41:03', 'admin');
INSERT INTO `rzcj_standard_strategy` VALUES ('1102', '绿盟-NF-NX-SERVERS', '', 1, '', '绿盟-NF-NX-SERVERS', '2024-11-13 17:41:03', 'admin');
INSERT INTO `rzcj_standard_strategy` VALUES ('1103', '摩莎-MOXA PT-7728', '', 1, '', '摩莎-MOXA PT-7728', '2024-11-13 17:41:03', 'admin');
INSERT INTO `rzcj_standard_strategy` VALUES ('1104', '摩莎-PT-7528', '', 1, '', '摩莎-PT-7528', '2024-11-13 17:41:03', 'admin');
INSERT INTO `rzcj_standard_strategy` VALUES ('1105', '默安-幻阵', '', 1, '', '默安-幻阵', '2024-11-13 17:41:03', 'admin');
INSERT INTO `rzcj_standard_strategy` VALUES ('1106', '南瑞-EPS7100', '', 1, '', '南瑞-EPS7100', '2024-11-13 17:41:03', 'admin');
INSERT INTO `rzcj_standard_strategy` VALUES ('1107', '南瑞-EPS7100A1', '', 1, '', '南瑞-EPS7100A1', '2024-11-13 17:41:03', 'admin');
INSERT INTO `rzcj_standard_strategy` VALUES ('1108', '南瑞-PCS9882', '', 1, '', '南瑞-PCS9882', '2024-11-13 17:41:03', 'admin');
INSERT INTO `rzcj_standard_strategy` VALUES ('1109', '南瑞继保-PCS-9882', '', 1, '', '南瑞继保-PCS-9882', '2024-11-13 17:41:03', 'admin');
INSERT INTO `rzcj_standard_strategy` VALUES ('1110', '南瑞信通-NetKeeper-2000', '', 1, '', '南瑞信通-NetKeeper-2000', '2024-11-13 17:41:03', 'admin');
INSERT INTO `rzcj_standard_strategy` VALUES ('1111', '南瑞信通-NetKeeper-2000SJY97', '', 1, '', '南瑞信通-NetKeeper-2000SJY97', '2024-11-13 17:41:03', 'admin');
INSERT INTO `rzcj_standard_strategy` VALUES ('1112', '南瑞信通-syskeeper-2000', '', 1, '', '南瑞信通-syskeeper-2000', '2024-11-13 17:41:03', 'admin');
INSERT INTO `rzcj_standard_strategy` VALUES ('1113', '南瑞信通-其他', '', 1, '', '南瑞信通-其他', '2024-11-13 17:41:03', 'admin');
INSERT INTO `rzcj_standard_strategy` VALUES ('1114', '凝思-LINX6.0', '', 1, '', '凝思-LINX6.0', '2024-11-13 17:41:03', 'admin');
INSERT INTO `rzcj_standard_strategy` VALUES ('1115', '麒麟-Linux', '', 1, '', '麒麟-Linux', '2024-11-13 17:41:03', 'admin');
INSERT INTO `rzcj_standard_strategy` VALUES ('1116', '启明星辰-USG2000', '', 1, '', '启明星辰-USG2000', '2024-11-13 17:41:03', 'admin');
INSERT INTO `rzcj_standard_strategy` VALUES ('1117', '启明星辰-USG200B', '', 1, '', '启明星辰-USG200B', '2024-11-13 17:41:03', 'admin');
INSERT INTO `rzcj_standard_strategy` VALUES ('1118', '启明星辰-USG-310', '', 1, '', '启明星辰-USG-310', '2024-11-13 17:41:03', 'admin');
INSERT INTO `rzcj_standard_strategy` VALUES ('1119', '启明星辰-USG-3600C', '', 1, '', '启明星辰-USG-3600C', '2024-11-13 17:41:03', 'admin');
INSERT INTO `rzcj_standard_strategy` VALUES ('1120', '启明星辰-USG-820C', '', 1, '', '启明星辰-USG-820C', '2024-11-13 17:41:03', 'admin');
INSERT INTO `rzcj_standard_strategy` VALUES ('1121', '启明星辰-USG-FW-12600GP', '', 1, '', '启明星辰-USG-FW-12600GP', '2024-11-13 17:41:03', 'admin');
INSERT INTO `rzcj_standard_strategy` VALUES ('1122', '启明星辰-USG-FW-310B', '', 1, '', '启明星辰-USG-FW-310B', '2024-11-13 17:41:03', 'admin');
INSERT INTO `rzcj_standard_strategy` VALUES ('1123', '启明星辰-USG-FW-320C', '', 1, '', '启明星辰-USG-FW-320C', '2024-11-13 17:41:03', 'admin');
INSERT INTO `rzcj_standard_strategy` VALUES ('1124', '启明星辰-USG-FW-8020E', '', 1, '', '启明星辰-USG-FW-8020E', '2024-11-13 17:41:03', 'admin');
INSERT INTO `rzcj_standard_strategy` VALUES ('1125', '启明星辰-WAF V3075', '', 1, '', '启明星辰-WAF V3075', '2024-11-13 17:41:03', 'admin');
INSERT INTO `rzcj_standard_strategy` VALUES ('1126', '瑞斯康达-S3028i', '', 1, '', '瑞斯康达-S3028i', '2024-11-13 17:41:03', 'admin');
INSERT INTO `rzcj_standard_strategy` VALUES ('1127', '上海贝尔-MES2452-L Series', '', 1, '', '上海贝尔-MES2452-L Series', '2024-11-13 17:41:03', 'admin');
INSERT INTO `rzcj_standard_strategy` VALUES ('1128', '上海博达-BDCOM-IES3328M', '', 1, '', '上海博达-BDCOM-IES3328M', '2024-11-13 17:41:03', 'admin');
INSERT INTO `rzcj_standard_strategy` VALUES ('1129', '深信服-AF-1000', '', 1, '', '深信服-AF-1000', '2024-11-13 17:41:03', 'admin');
INSERT INTO `rzcj_standard_strategy` VALUES ('1130', '深信服-AF3120', '', 1, '', '深信服-AF3120', '2024-11-13 17:41:03', 'admin');
INSERT INTO `rzcj_standard_strategy` VALUES ('1131', '深信服-AF6020', '', 1, '', '深信服-AF6020', '2024-11-13 17:41:03', 'admin');
INSERT INTO `rzcj_standard_strategy` VALUES ('1132', '深信服-AF6062', '', 1, '', '深信服-AF6062', '2024-11-13 17:41:03', 'admin');
INSERT INTO `rzcj_standard_strategy` VALUES ('1133', '思科-2811', '', 1, '', '思科-2811', '2024-11-13 17:41:03', 'admin');
INSERT INTO `rzcj_standard_strategy` VALUES ('1134', '思科-2960', '', 1, '', '思科-2960', '2024-11-13 17:41:03', 'admin');
INSERT INTO `rzcj_standard_strategy` VALUES ('1135', '思科-3560', '', 1, '', '思科-3560', '2024-11-13 17:41:03', 'admin');
INSERT INTO `rzcj_standard_strategy` VALUES ('1136', '思科-3750', '', 1, '', '思科-3750', '2024-11-13 17:41:03', 'admin');
INSERT INTO `rzcj_standard_strategy` VALUES ('1137', '思科-4500 L3', '', 1, '', '思科-4500 L3', '2024-11-13 17:41:03', 'admin');
INSERT INTO `rzcj_standard_strategy` VALUES ('1138', '思科-4948', '', 1, '', '思科-4948', '2024-11-13 17:41:03', 'admin');
INSERT INTO `rzcj_standard_strategy` VALUES ('1139', '思科-6500', '', 1, '', '思科-6500', '2024-11-13 17:41:03', 'admin');
INSERT INTO `rzcj_standard_strategy` VALUES ('1140', '思科-6501', '', 1, '', '思科-6501', '2024-11-13 17:41:03', 'admin');
INSERT INTO `rzcj_standard_strategy` VALUES ('1141', '思科-ASA5550', '', 1, '', '思科-ASA5550', '2024-11-13 17:41:03', 'admin');
INSERT INTO `rzcj_standard_strategy` VALUES ('1142', '思科-C2600', '', 1, '', '思科-C2600', '2024-11-13 17:41:03', 'admin');
INSERT INTO `rzcj_standard_strategy` VALUES ('1143', '思科-C3550', '', 1, '', '思科-C3550', '2024-11-13 17:41:03', 'admin');
INSERT INTO `rzcj_standard_strategy` VALUES ('1144', '思科-S6509', '', 1, '', '思科-S6509', '2024-11-13 17:41:03', 'admin');
INSERT INTO `rzcj_standard_strategy` VALUES ('1145', '天融信-NGFW4000', '', 1, '', '天融信-NGFW4000', '2024-11-13 17:41:03', 'admin');
INSERT INTO `rzcj_standard_strategy` VALUES ('1146', '天融信-NGFW4000-4208D', '', 1, '', '天融信-NGFW4000-4208D', '2024-11-13 17:41:03', 'admin');
INSERT INTO `rzcj_standard_strategy` VALUES ('1147', '天融信-NGFW4000-UF', '', 1, '', '天融信-NGFW4000-UF', '2024-11-13 17:41:03', 'admin');
INSERT INTO `rzcj_standard_strategy` VALUES ('1148', '通用-SnmpTrap', '', 1, '', '通用-SnmpTrap', '2024-11-13 17:41:03', 'admin');
INSERT INTO `rzcj_standard_strategy` VALUES ('1149', '通用-Syslog', '', 1, '', '通用-Syslog', '2024-11-13 17:41:03', 'admin');
INSERT INTO `rzcj_standard_strategy` VALUES ('1150', '微软-Windows', '', 1, '', '微软-Windows', '2024-11-13 17:41:03', 'admin');
INSERT INTO `rzcj_standard_strategy` VALUES ('1151', '卫士通-SJJ1632', '', 1, '', '卫士通-SJJ1632', '2024-11-13 17:41:03', 'admin');
INSERT INTO `rzcj_standard_strategy` VALUES ('1152', '卫士通-SJW77', '', 1, '', '卫士通-SJW77', '2024-11-13 17:41:03', 'admin');
INSERT INTO `rzcj_standard_strategy` VALUES ('1153', '兴唐-SJW07-A', '', 1, '', '兴唐-SJW07-A', '2024-11-13 17:41:03', 'admin');
INSERT INTO `rzcj_standard_strategy` VALUES ('1154', '许继电气-ZYJ-700', '', 1, '', '许继电气-ZYJ-700', '2024-11-13 17:41:03', 'admin');
INSERT INTO `rzcj_standard_strategy` VALUES ('1155', '银河麒麟-Linux', '', 1, '', '银河麒麟-Linux', '2024-11-13 17:41:03', 'admin');
INSERT INTO `rzcj_standard_strategy` VALUES ('1156', '银河麒麟-银河麒麟4.0.2', '', 1, '', '银河麒麟-银河麒麟4.0.2', '2024-11-13 17:41:03', 'admin');
INSERT INTO `rzcj_standard_strategy` VALUES ('1157', '优麒麟-Linux', '', 1, '', '优麒麟-Linux', '2024-11-13 17:41:03', 'admin');
INSERT INTO `rzcj_standard_strategy` VALUES ('1158', '中标麒麟-linux4.8', '', 1, '', '中标麒麟-linux4.8', '2024-11-13 17:41:03', 'admin');
INSERT INTO `rzcj_standard_strategy` VALUES ('1159', '珠海鸿瑞-HRWALL-85MII', '', 1, '', '珠海鸿瑞-HRWALL-85MII', '2024-11-13 17:41:03', 'admin');
INSERT INTO `rzcj_standard_strategy` VALUES ('1160', '左岸-ZVANCOM 44-N', '', 1, '', '左岸-ZVANCOM 44-N', '2024-11-13 17:41:03', 'admin');

-- ----------------------------
-- Table structure for rzcj_standard_strategy_field
-- ----------------------------
DROP TABLE IF EXISTS `rzcj_standard_strategy_field`;
CREATE TABLE `rzcj_standard_strategy_field`  (
  `id` int NOT NULL,
  `public_field` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '公共字段',
  `field_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '对应字段名称',
  `is_mandatory` int NULL DEFAULT NULL COMMENT '是否必选 1：是 0：否',
  `level` int NULL DEFAULT NULL COMMENT '级别',
  `parent_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '父级ID',
  `group_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '分组名称 1:常用 2：预置变量 3：处理方法 4：其它',
  `field_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '显示框类型 1:文本框 2：数字框 3：下一级下拉列表',
  `default_value` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '默认值',
  `prompt` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '提示语',
  `is_end` int NULL DEFAULT 0 COMMENT '是否最后节点 0：否 1：是',
  `three_is_group` int NULL DEFAULT 0 COMMENT '第三级是否存在group分组,1：是 0：否',
  `is_regular_field` int NULL DEFAULT 0 COMMENT '是否正则下的字段 0：否  1：是',
  `value_range` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '取值范围',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '采集编辑器' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of rzcj_standard_strategy_field
-- ----------------------------
INSERT INTO `rzcj_standard_strategy_field` VALUES (1, '事件名称', 'event_name', 1, 1, '0', NULL, '1', '', '', 0, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (2, '事件类型', 'event_type', 1, 1, '0', NULL, '1', '', '', 0, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (3, '严重级别', 'severity_level', 1, 1, '0', NULL, '2', '', '', 0, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (4, '原始级别', 'original_level', 1, 1, '0', NULL, '1', '', '', 0, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (5, '详细信息', 'message', 0, 1, '0', NULL, '1', '', '', 0, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (6, '设备类型', 'device_type', 1, 1, '0', NULL, '1', '', '', 0, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (7, '设备地址', 'device_address', 1, 1, '0', NULL, '1', '', '', 0, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (8, '设备名称', 'device_name', 0, 1, '0', NULL, '1', '', '', 0, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (9, '产品名称', 'product_name', 0, 1, '0', NULL, '1', '', '', 0, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (10, '产品版本', 'product_version', 0, 1, '0', NULL, '1', '', '', 0, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (11, '原始时间', '@timestamp', 1, 1, '0', NULL, '1', '', '', 0, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (12, '可信度', 'credibility', 1, 1, '0', NULL, '2', '', '', 0, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (13, '源地址', 'source_address', 0, 1, '0', NULL, '1', '', '', 0, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (14, '源主机名', 'source_hostname', 0, 1, '0', NULL, '1', '', '', 0, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (15, '源端口', 'source_port', 0, 1, '0', NULL, '2', '', '', 0, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (16, '源地址掩码', 'source_mask', 0, 1, '0', NULL, '1', '', '', 0, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (17, '源用户', 'source_user', 0, 1, '0', NULL, '1', '', '', 0, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (18, '源MAC', 'source_mac', 0, 1, '0', NULL, '1', '', '', 0, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (19, '源区域', 'source_zone', 0, 1, '0', NULL, '1', '', '', 0, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (20, '源操作系统', 'source_os', 0, 1, '0', NULL, '1', '', '', 0, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (21, '源浏览器', 'source_browser', 0, 1, '0', NULL, '1', '', '', 0, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (22, '源运营商', 'source_isp', 0, 1, '0', NULL, '1', '', '', 0, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (23, '目的地址', 'dest_address', 0, 1, '0', NULL, '1', '', '', 0, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (24, '目的主机名', 'dest_hostname', 0, 1, '0', NULL, '1', '', '', 0, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (25, '目的端口', 'dest_port', 0, 1, '0', NULL, '2', '', '', 0, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (26, '目的用户', 'dest_user', 0, 1, '0', NULL, '1', '', '', 0, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (27, '目的地址掩码', 'dest_mask', 0, 1, '0', NULL, '1', '', '', 0, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (28, '目的MAC', 'dest_mac', 0, 1, '0', NULL, '1', '', '', 0, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (29, '目的区域', 'dest_zone', 0, 1, '0', NULL, '1', '', '', 0, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (30, '目的运营商', 'dest_isp', 0, 1, '0', NULL, '1', '', '', 0, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (31, '发送流量', 'sent_traffic', 0, 1, '0', NULL, '2', '', '', 0, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (32, '发送流量单位', 'sent_traffic_unit', 0, 1, '0', NULL, '1', '', '', 0, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (33, '接收流量', 'received_traffic', 0, 1, '0', NULL, '2', '', '', 0, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (34, '接受流量单位', 'received_traffic_unit', 0, 1, '0', NULL, '1', '', '', 0, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (35, '总流量', 'total_traffic', 0, 1, '0', NULL, '2', '', '', 0, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (36, '总流量单位', 'total_traffic_unit', 0, 1, '0', NULL, '1', '', '', 0, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (37, '行为执行者分类', 'related_object_category', 0, 1, '0', NULL, '1', '', '', 0, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (38, '行为执行者类', 'related_object_subcategory', 0, 1, '0', NULL, '1', '', '', 0, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (39, '行为执行者名称', 'related_object_name', 0, 1, '0', NULL, '1', '', '', 0, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (40, '行为执行者地址', 'related_object_address', 0, 1, '0', NULL, '1', '', '', 0, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (41, '行为执行者帐号', 'related_object_account', 0, 1, '0', NULL, '1', '', '', 0, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (42, '行为来源帐号', 'action_account', 0, 1, '0', NULL, '1', '', '', 0, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (43, '行为来源地址', 'action_address', 0, 1, '0', NULL, '1', '', '', 0, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (44, '行为对象类型', 'action_object_type', 0, 1, '0', NULL, '1', '', '', 0, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (45, '行为对象名称', 'action_object_name', 0, 1, '0', NULL, '1', '', '', 0, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (46, '行为对象地址', 'action_object_address', 0, 1, '0', NULL, '1', '', '', 0, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (47, '行为详细信息', 'action_details', 0, 1, '0', NULL, '1', '', '', 0, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (48, '行为持续时间', 'action_duration', 0, 1, '0', NULL, '1', '', '', 0, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (49, '域名', 'domain', 0, 1, '0', NULL, '1', '', '', 0, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (50, '组名', 'group_name', 0, 1, '0', NULL, '1', '', '', 0, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (51, '网络服务名', 'network_service', 0, 1, '0', NULL, '1', '', '', 0, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (52, 'NAT IP', 'nat_ip', 0, 1, '0', NULL, '1', '', '', 0, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (53, '其它端口', 'other_port', 0, 1, '0', NULL, '2', '', '', 0, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (54, '协议', 'protocol', 0, 1, '0', NULL, '1', '', '', 0, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (55, '行为动作', 'action', 0, 1, '0', NULL, '1', '', '', 0, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (56, '行为动作结果', 'action_result', 0, 1, '0', NULL, '1', '', '', 0, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (57, '相关文件名', 'related_filename', 0, 1, '0', NULL, '1', '', '', 0, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (58, '会话编号', 'session_id', 0, 1, '0', NULL, '1', '', '', 0, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (60, '结果码', 'result_code', 0, 1, '0', NULL, '1', '', '', 0, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (61, 'URL', 'url', 0, 1, '0', NULL, '1', '', '', 0, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (62, '引用地址', 'referrer', 0, 1, '0', NULL, '1', '', '', 0, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (63, '引用域名', 'referrer_domain', 0, 1, '0', NULL, '1', '', '', 0, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (64, '应用协议', 'application_protocol', 0, 1, '0', NULL, '1', '', '', 0, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (65, '事件子类', 'event_sub_type', 0, 1, '0', NULL, '1', NULL, NULL, 0, 0, 0, NULL);
INSERT INTO `rzcj_standard_strategy_field` VALUES (1001, '文本', NULL, NULL, 2, '1,4,5,7,8,9,10,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64', '常用', '1', '', '', 1, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1002, '内置变量', NULL, NULL, 2, '1,4,5,7,8,9,10,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,60,61,62,63,64', '预置变量', '3', '', '', 0, 1, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1003, '设备类型', NULL, NULL, 2, '6', '预置变量', '3', '', '', 0, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1004, '事件类型', NULL, NULL, 2, '2', '预置变量', '3', '', '', 0, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1005, '严重级别', NULL, NULL, 2, '3', '预置变量', '2', '', '严重级别:0-4', 1, 0, 0, '0-4');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1006, '网络协议', NULL, NULL, 2, '1,4,5,7,8,9,10,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,60,61,62,63,64', '预置变量', '3', '', '', 0, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1007, '动作结果', NULL, NULL, 2, '1,4,5,7,8,9,10,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,60,61,62,63,64', '预置变量', '3', '', '', 0, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1008, '字节单位', NULL, NULL, 2, '1,4,5,7,8,9,10,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,60,61,62,63,64', '预置变量', '3', '', '', 0, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1009, '可信度', NULL, NULL, 2, '12', '预置变量', '2', '', '可信度:0-50', 1, 0, 0, '0-50');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1010, '正则提取', NULL, NULL, 2, '1,4,5,7,8,9,10,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,60,61,62,63,64', '常用', '2', '', '请输入下标', 1, 0, 1, '1-99999999');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1011, '时间解析', NULL, NULL, 2, '1,4,5,7,8,9,10,11,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,60,61,62,63,64', '处理方法', '2', '', '请输入正则提取下标', 0, 0, 1, '1-99999999');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1012, 'UTC时间转换', NULL, NULL, 2, '1,4,5,7,8,9,10,11,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,60,61,62,63,64', '处理方法', '2', '', '请输入下标', 1, 0, 1, '1-99999999');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1013, '当前日期', NULL, NULL, 2, '1,4,5,7,8,9,10,11,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,60,61,62,63,64', '处理方法', '3', '', '请输入下标', 0, 0, 0, '1-99999999');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1014, '标准日志格式解析', NULL, NULL, 2, '11', '处理方法', '3', '', '请输入下标', 0, 0, 0, '1-99999999');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000001, '事件源地址', NULL, NULL, 3, '1002', '事件源地址', '3', '', '', 1, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000002, '采集器ID', NULL, NULL, 3, '1002', '事件源地址', '3', '', '', 1, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000003, '原始事件内容', NULL, NULL, 3, '1002', '事件源地址', '3', '', '', 1, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000004, '优先级', NULL, NULL, 3, '1002', 'syslog', '3', '', '', 1, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000005, '机构', NULL, NULL, 3, '1002', 'syslog', '3', '', '', 1, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000006, '级别', NULL, NULL, 3, '1002', 'syslog', '3', '', '', 1, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000007, '日期时间', NULL, NULL, 3, '1002', 'syslog', '3', '', '', 1, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000008, 'SNMP版本', NULL, NULL, 3, '1002', 'SNMP Trap', '3', '', '', 1, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000009, '社区串', NULL, NULL, 3, '1002', 'SNMP Trap', '3', '', '', 1, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000010, '时间戳', NULL, NULL, 3, '1002', 'SNMP Trap', '3', '', '', 1, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000011, 'SNMP标识头', NULL, NULL, 3, '1002', 'SNMP Trap', '3', '', '', 1, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000012, '社区串值', NULL, NULL, 3, '1002', 'SNMP Trap', '3', '', '', 1, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000013, '恶意程序', NULL, NULL, 3, '1004', NULL, '3', '', '', 0, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000014, '网络攻击', NULL, NULL, 3, '1004', NULL, '3', '', '', 0, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000015, '数据安全', NULL, NULL, 3, '1004', NULL, '3', '', '', 0, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000016, '信息内容安全', NULL, NULL, 3, '1004', NULL, '3', '', '', 0, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000017, '设备设施故障', NULL, NULL, 3, '1004', NULL, '3', '', '', 0, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000018, '违规操作', NULL, NULL, 3, '1004', NULL, '3', '', '', 0, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000019, '安全隐患', NULL, NULL, 3, '1004', NULL, '3', '', '', 0, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000020, '异常行为', NULL, NULL, 3, '1004', NULL, '3', '', '', 0, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000021, '不可抗力', NULL, NULL, 3, '1004', NULL, '3', '', '', 0, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000022, '暴力破解', NULL, NULL, 3, '1004', NULL, '3', '', '', 0, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000023, 'Linux主机', NULL, NULL, 3, '1004', NULL, '3', '', '', 0, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000024, 'Windows主机', NULL, NULL, 3, '1004', NULL, '3', '', '', 0, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000025, '运维监控', NULL, NULL, 3, '1004', NULL, '3', '', '', 0, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000026, '配置状态', NULL, NULL, 3, '1004', NULL, '3', '', '', 0, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000027, '连接', NULL, NULL, 3, '1004', NULL, '3', '', '', 0, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000028, '访问控制', NULL, NULL, 3, '1004', NULL, '3', '', '', 0, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000029, '账户管理', NULL, NULL, 3, '1004', NULL, '3', '', '', 0, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000030, '数据文件', NULL, NULL, 3, '1004', NULL, '3', '', '', 0, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000031, '其他事件类型', NULL, NULL, 3, '1004', NULL, '3', '', '', 0, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000032, 'Unix/Linux主机', NULL, NULL, 3, '1003', NULL, '3', '', '', 1, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000033, 'Windows主机', NULL, NULL, 3, '1003', NULL, '3', '', '', 1, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000034, '网络设备', NULL, NULL, 3, '1003', NULL, '3', '', '', 1, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000035, '防火墙', NULL, NULL, 3, '1003', NULL, '3', '', '', 1, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000036, '统一威胁管理', NULL, NULL, 3, '1003', NULL, '3', '', '', 1, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000037, '入侵检测系统', NULL, NULL, 3, '1003', NULL, '3', '', '', 1, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000038, '入侵防御系统', NULL, NULL, 3, '1003', NULL, '3', '', '', 1, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000039, '扫描器', NULL, NULL, 3, '1003', NULL, '3', '', '', 1, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000040, 'VPN', NULL, NULL, 3, '1003', NULL, '3', '', '', 1, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000041, '防病毒', NULL, NULL, 3, '1003', NULL, '3', '', '', 1, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000042, '数据库', NULL, NULL, 3, '1003', NULL, '3', '', '', 1, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000043, 'Web中间件', NULL, NULL, 3, '1003', NULL, '3', '', '', 1, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000044, '堡垒机', NULL, NULL, 3, '1003', NULL, '3', '', '', 1, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000045, '应用系统', NULL, NULL, 3, '1003', NULL, '3', '', '', 1, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000046, '其它', NULL, NULL, 3, '1003', NULL, '3', '', '', 1, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000047, 'TCP', NULL, NULL, 3, '1006', NULL, '3', '', '', 1, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000048, 'UDP', NULL, NULL, 3, '1006', NULL, '3', '', '', 1, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000049, 'ANY', NULL, NULL, 3, '1006', NULL, '3', '', '', 1, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000050, 'ICMP', NULL, NULL, 3, '1006', NULL, '3', '', '', 1, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000051, '成功', NULL, NULL, 3, '1007', NULL, '3', '', '', 1, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000052, '失败', NULL, NULL, 3, '1007', NULL, '3', '', '', 1, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000053, '未知', NULL, NULL, 3, '1007', NULL, '3', '', '', 1, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000054, 'B', NULL, NULL, 3, '1008', NULL, '3', '', '', 1, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000055, 'KB', NULL, NULL, 3, '1008', NULL, '3', '', '', 1, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000056, 'MB', NULL, NULL, 3, '1008', NULL, '3', '', '', 1, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000057, 'GB', NULL, NULL, 3, '1008', NULL, '3', '', '', 1, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000058, 'ISO8601', NULL, NULL, 3, '1013', NULL, '3', '', '', 1, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000059, '策略管理', NULL, NULL, 3, '1004', NULL, '3', NULL, NULL, 0, 0, 0, NULL);
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000060, '用户命令', NULL, NULL, 3, '1004', NULL, '3', NULL, NULL, 0, 0, 0, NULL);
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000061, '行为监视', NULL, NULL, 3, '1004', NULL, '3', NULL, NULL, 0, 0, 0, NULL);
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000062, '运行日志', NULL, NULL, 3, '1004', NULL, '3', NULL, NULL, 0, 0, 0, NULL);
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000063, '告警事件', NULL, NULL, 3, '1004', NULL, '3', NULL, NULL, 0, 0, 0, NULL);
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000064, '设备故障事件', NULL, NULL, 3, '1004', NULL, '3', NULL, NULL, 0, 0, 0, NULL);
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000065, '安全事件', NULL, NULL, 3, '1004', NULL, '3', NULL, NULL, 0, 0, 0, NULL);
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000066, '配置信息', NULL, NULL, 3, '1004', NULL, '3', NULL, NULL, 0, 0, 0, NULL);
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000067, '状态信息', NULL, NULL, 3, '1004', NULL, '3', NULL, NULL, 0, 0, 0, NULL);
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000068, '管理事件', NULL, NULL, 3, '1004', NULL, '3', NULL, NULL, 0, 0, 0, NULL);
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000069, '数据库运行信息', NULL, NULL, 3, '1004', NULL, '3', NULL, NULL, 0, 0, 0, NULL);
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000070, '数据库安全日志', NULL, NULL, 3, '1004', NULL, '3', NULL, NULL, 0, 0, 0, NULL);
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000071, '系统日志', NULL, NULL, 3, '1004', NULL, '3', NULL, NULL, 0, 0, 0, NULL);
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000072, '病毒日志', NULL, NULL, 3, '1004', NULL, '3', NULL, NULL, 0, 0, 0, NULL);
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000073, '入侵保护事件', NULL, NULL, 3, '1004', NULL, '3', NULL, NULL, 0, 0, 0, NULL);
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000074, '登录操作信息', NULL, NULL, 3, '1004', NULL, '3', NULL, NULL, 0, 0, 0, NULL);
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000075, '基线核查信息', NULL, NULL, 3, '1004', NULL, '3', NULL, NULL, 0, 0, 0, NULL);
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000076, '横向正向隔离装置', NULL, NULL, 3, '1003', NULL, '3', NULL, NULL, 1, 0, 0, NULL);
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000077, '横向反向隔离装置', NULL, NULL, 3, '1003', NULL, '3', NULL, NULL, 1, 0, 0, NULL);
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000078, '服务器', NULL, NULL, 3, '1003', NULL, '3', NULL, NULL, 1, 0, 0, NULL);
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000079, '交换机', NULL, NULL, 3, '1003', NULL, '3', NULL, NULL, 1, 0, 0, NULL);
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000080, '纵向加密装置', NULL, NULL, 3, '1003', NULL, '3', NULL, NULL, 1, 0, 0, NULL);
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000081, '网络安全监测装置', NULL, NULL, 3, '1003', NULL, '3', NULL, NULL, 1, 0, 0, NULL);
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000082, 'Syslog', NULL, NULL, 3, '1014', NULL, '3', '', '', 1, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000083, 'SnmpTrap', NULL, NULL, 3, '1014', NULL, '3', '', '', 1, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000001, '计算机病毒', NULL, NULL, 4, '1000013', NULL, '3', '', '', 1, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000002, '网络蠕虫', NULL, NULL, 4, '1000013', NULL, '3', '', '', 1, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000003, '特洛伊木马', NULL, NULL, 4, '1000013', NULL, '3', '', '', 1, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000004, '恶意代码内嵌网页', NULL, NULL, 4, '1000013', NULL, '3', '', '', 1, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000005, '恶意代码宿主站点', NULL, NULL, 4, '1000013', NULL, '3', '', '', 1, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000006, '僵尸网络', NULL, NULL, 4, '1000013', NULL, '3', '', '', 1, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000007, '勒索软件', NULL, NULL, 4, '1000013', NULL, '3', '', '', 1, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000008, '挖矿病毒', NULL, NULL, 4, '1000013', NULL, '3', '', '', 1, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000009, '混合攻击程序', NULL, NULL, 4, '1000013', NULL, '3', '', '', 1, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000010, '其他恶意程序', NULL, NULL, 4, '1000013', NULL, '3', '', '', 1, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000011, '网络扫描探测', NULL, NULL, 4, '1000014', NULL, '3', '', '', 1, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000012, '网络钓鱼', NULL, NULL, 4, '1000014', NULL, '3', '', '', 1, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000013, '漏洞利用', NULL, NULL, 4, '1000014', NULL, '3', '', '', 1, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000014, '后门利用', NULL, NULL, 4, '1000014', NULL, '3', '', '', 1, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000015, '后门植入', NULL, NULL, 4, '1000014', NULL, '3', '', '', 1, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000016, '凭据攻击', NULL, NULL, 4, '1000014', NULL, '3', '', '', 1, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000017, '信号干扰', NULL, NULL, 4, '1000014', NULL, '3', '', '', 1, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000018, '拒绝服务', NULL, NULL, 4, '1000014', NULL, '3', '', '', 1, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000019, '网页篡改', NULL, NULL, 4, '1000014', NULL, '3', '', '', 1, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000020, '暗链植入', NULL, NULL, 4, '1000014', NULL, '3', '', '', 1, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000021, '域名劫持', NULL, NULL, 4, '1000014', NULL, '3', '', '', 1, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000022, '域名转嫁', NULL, NULL, 4, '1000014', NULL, '3', '', '', 1, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000023, 'DNS污染', NULL, NULL, 4, '1000014', NULL, '3', '', '', 1, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000024, 'WLAN劫持', NULL, NULL, 4, '1000014', NULL, '3', '', '', 1, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000025, '流量劫持', NULL, NULL, 4, '1000014', NULL, '3', '', '', 1, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000026, 'BGP劫持攻击', NULL, NULL, 4, '1000014', NULL, '3', '', '', 1, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000027, '广播欺诈', NULL, NULL, 4, '1000014', NULL, '3', '', '', 1, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000028, '失陷主机', NULL, NULL, 4, '1000014', NULL, '3', '', '', 1, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000029, '供应链攻击', NULL, NULL, 4, '1000014', NULL, '3', '', '', 1, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000030, 'APT', NULL, NULL, 4, '1000014', NULL, '3', '', '', 1, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000031, '其他网络攻击', NULL, NULL, 4, '1000014', NULL, '3', '', '', 1, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000032, '数据篡改', NULL, NULL, 4, '1000015', NULL, '3', '', '', 1, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000033, '数据假冒', NULL, NULL, 4, '1000015', NULL, '3', '', '', 1, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000034, '数据泄露', NULL, NULL, 4, '1000015', NULL, '3', '', '', 1, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000035, '社会工程', NULL, NULL, 4, '1000015', NULL, '3', '', '', 1, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000036, '数据窃取', NULL, NULL, 4, '1000015', NULL, '3', '', '', 1, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000037, '数据拦截', NULL, NULL, 4, '1000015', NULL, '3', '', '', 1, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000038, '位置检测', NULL, NULL, 4, '1000015', NULL, '3', '', '', 1, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000039, '数据投毒', NULL, NULL, 4, '1000015', NULL, '3', '', '', 1, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000040, '数据滥用', NULL, NULL, 4, '1000015', NULL, '3', '', '', 1, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000041, '隐私侵犯', NULL, NULL, 4, '1000015', NULL, '3', '', '', 1, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000042, '数据损失', NULL, NULL, 4, '1000015', NULL, '3', '', '', 1, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000043, '其他数据安全', NULL, NULL, 4, '1000015', NULL, '3', '', '', 1, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000044, '反动宣传', NULL, NULL, 4, '1000016', NULL, '3', '', '', 1, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000045, '暴恐宣扬', NULL, NULL, 4, '1000016', NULL, '3', '', '', 1, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000046, '色情传播', NULL, NULL, 4, '1000016', NULL, '3', '', '', 1, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000047, '虚假信息传播', NULL, NULL, 4, '1000016', NULL, '3', '', '', 1, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000048, '权益侵害', NULL, NULL, 4, '1000016', NULL, '3', '', '', 1, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000049, '信息滥发', NULL, NULL, 4, '1000016', NULL, '3', '', '', 1, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000050, '网络欺诈', NULL, NULL, 4, '1000016', NULL, '3', '', '', 1, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000051, '其他信息内容安全', NULL, NULL, 4, '1000016', NULL, '3', '', '', 1, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000052, '技术故障', NULL, NULL, 4, '1000017', NULL, '3', '', '', 1, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000053, '配套设施故障', NULL, NULL, 4, '1000017', NULL, '3', '', '', 1, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000054, '物理损害', NULL, NULL, 4, '1000017', NULL, '3', '', '', 1, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000055, '辐射干扰', NULL, NULL, 4, '1000017', NULL, '3', '', '', 1, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000056, '其他设备设施故障', NULL, NULL, 4, '1000017', NULL, '3', '', '', 1, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000057, '权限滥用', NULL, NULL, 4, '1000018', NULL, '3', '', '', 1, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000058, '权限伪造', NULL, NULL, 4, '1000018', NULL, '3', '', '', 1, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000059, '行为抵赖', NULL, NULL, 4, '1000018', NULL, '3', '', '', 1, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000060, '故意违规操作', NULL, NULL, 4, '1000018', NULL, '3', '', '', 1, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000061, '误操作', NULL, NULL, 4, '1000018', NULL, '3', '', '', 1, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000062, '人员可用性破坏', NULL, NULL, 4, '1000018', NULL, '3', '', '', 1, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000063, '资源未授权使用', NULL, NULL, 4, '1000018', NULL, '3', '', '', 1, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000064, '版权违反', NULL, NULL, 4, '1000018', NULL, '3', '', '', 1, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000065, '其他违规操作', NULL, NULL, 4, '1000018', NULL, '3', '', '', 1, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000066, '网络漏洞', NULL, NULL, 4, '1000019', NULL, '3', '', '', 1, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000067, '网络配置合规缺陷', NULL, NULL, 4, '1000019', NULL, '3', '', '', 1, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000068, '其他安全隐患', NULL, NULL, 4, '1000019', NULL, '3', '', '', 1, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000069, '访问异常', NULL, NULL, 4, '1000020', NULL, '3', '', '', 1, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000070, '流量异常', NULL, NULL, 4, '1000020', NULL, '3', '', '', 1, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000071, '其他异常行为', NULL, NULL, 4, '1000020', NULL, '3', '', '', 1, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000072, '自然灾害', NULL, NULL, 4, '1000021', NULL, '3', '', '', 1, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000073, '事故灾难', NULL, NULL, 4, '1000021', NULL, '3', '', '', 1, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000074, '公共卫生', NULL, NULL, 4, '1000021', NULL, '3', '', '', 1, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000075, '社会安全', NULL, NULL, 4, '1000021', NULL, '3', '', '', 1, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000076, '其他不可抗力', NULL, NULL, 4, '1000021', NULL, '3', '', '', 1, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000077, 'Windows爆破', NULL, NULL, 4, '1000022', NULL, '3', '', '', 1, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000078, 'Linux爆破', NULL, NULL, 4, '1000022', NULL, '3', '', '', 1, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000079, '应用爆破', NULL, NULL, 4, '1000022', NULL, '3', '', '', 1, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000080, '其他暴力破解', NULL, NULL, 4, '1000022', NULL, '3', '', '', 1, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000081, '系统日志', NULL, NULL, 4, '1000023', NULL, '3', '', '', 1, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000082, '安全日志', NULL, NULL, 4, '1000023', NULL, '3', '', '', 1, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000083, '应用程序日志', NULL, NULL, 4, '1000023', NULL, '3', '', '', 1, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000084, '其他日志', NULL, NULL, 4, '1000023', NULL, '3', '', '', 1, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000085, '系统日志', NULL, NULL, 4, '1000024', NULL, '3', '', '', 1, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000086, '安全日志', NULL, NULL, 4, '1000024', NULL, '3', '', '', 1, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000087, '应用程序日志', NULL, NULL, 4, '1000024', NULL, '3', '', '', 1, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000088, '其他日志', NULL, NULL, 4, '1000024', NULL, '3', '', '', 1, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000089, '性能监控', NULL, NULL, 4, '1000025', NULL, '3', '', '', 1, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000090, '故障监控', NULL, NULL, 4, '1000025', NULL, '3', '', '', 1, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000091, '操作审计', NULL, NULL, 4, '1000025', NULL, '3', '', '', 1, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000092, '其他监控', NULL, NULL, 4, '1000025', NULL, '3', '', '', 1, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000093, '配置信息', NULL, NULL, 4, '1000026', NULL, '3', '', '', 1, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000094, '配置变更', NULL, NULL, 4, '1000026', NULL, '3', '', '', 1, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000095, '状态变更', NULL, NULL, 4, '1000026', NULL, '3', '', '', 1, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000096, '状态跟踪', NULL, NULL, 4, '1000026', NULL, '3', '', '', 1, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000097, '资源调用', NULL, NULL, 4, '1000026', NULL, '3', '', '', 1, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000098, '设备重启', NULL, NULL, 4, '1000026', NULL, '3', '', '', 1, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000099, '启动', NULL, NULL, 4, '1000026', NULL, '3', '', '', 1, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000100, '停止关闭', NULL, NULL, 4, '1000026', NULL, '3', '', '', 1, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000101, '安装维护', NULL, NULL, 4, '1000026', NULL, '3', '', '', 1, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000102, '其他配置状态', NULL, NULL, 4, '1000026', NULL, '3', '', '', 1, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000103, '连接接受', NULL, NULL, 4, '1000027', NULL, '3', '', '', 1, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000104, '连接拒绝', NULL, NULL, 4, '1000027', NULL, '3', '', '', 1, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000105, '主动连接', NULL, NULL, 4, '1000027', NULL, '3', '', '', 1, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000106, '连接断开', NULL, NULL, 4, '1000027', NULL, '3', '', '', 1, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000107, '连接中断', NULL, NULL, 4, '1000027', NULL, '3', '', '', 1, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000108, '连接超时', NULL, NULL, 4, '1000027', NULL, '3', '', '', 1, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000109, '地址转换', NULL, NULL, 4, '1000027', NULL, '3', '', '', 1, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000110, '连接跟踪', NULL, NULL, 4, '1000027', NULL, '3', '', '', 1, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000111, '其他连接', NULL, NULL, 4, '1000027', NULL, '3', '', '', 1, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000112, '用户登录', NULL, NULL, 4, '1000028', NULL, '3', '', '', 1, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000113, '用户注销', NULL, NULL, 4, '1000028', NULL, '3', '', '', 1, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000114, '用户切换', NULL, NULL, 4, '1000028', NULL, '3', '', '', 1, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000115, '其他访问控制', NULL, NULL, 4, '1000028', NULL, '3', '', '', 1, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000116, '账户新建', NULL, NULL, 4, '1000029', NULL, '3', '', '', 1, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000117, '账户删除', NULL, NULL, 4, '1000029', NULL, '3', '', '', 1, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000118, '账户失效', NULL, NULL, 4, '1000029', NULL, '3', '', '', 1, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000119, '账户变更', NULL, NULL, 4, '1000029', NULL, '3', '', '', 1, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000120, '口令变更', NULL, NULL, 4, '1000029', NULL, '3', '', '', 1, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000121, '权限变更', NULL, NULL, 4, '1000029', NULL, '3', '', '', 1, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000122, '其他账户管理', NULL, NULL, 4, '1000029', NULL, '3', '', '', 1, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000123, '文件新增', NULL, NULL, 4, '1000030', NULL, '3', '', '', 1, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000124, '文件删除', NULL, NULL, 4, '1000030', NULL, '3', '', '', 1, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000125, '文件修改', NULL, NULL, 4, '1000030', NULL, '3', '', '', 1, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000126, '数据新增', NULL, NULL, 4, '1000030', NULL, '3', '', '', 1, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000127, '数据删除', NULL, NULL, 4, '1000030', NULL, '3', '', '', 1, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000128, '数据修改', NULL, NULL, 4, '1000030', NULL, '3', '', '', 1, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000129, '数据备份/还原', NULL, NULL, 4, '1000030', NULL, '3', '', '', 1, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000130, '其他数据变化', NULL, NULL, 4, '1000030', NULL, '3', '', '', 1, 0, 0, '');
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000131, '策略新建', NULL, NULL, 4, '1000059', NULL, '3', NULL, NULL, 1, 0, 0, NULL);
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000132, '策略删除', NULL, NULL, 4, '1000059', NULL, '3', NULL, NULL, 1, 0, 0, NULL);
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000133, '策略失效', NULL, NULL, 4, '1000059', NULL, '3', NULL, NULL, 1, 0, 0, NULL);
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000134, '策略修改', NULL, NULL, 4, '1000059', NULL, '3', NULL, NULL, 1, 0, 0, NULL);
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000135, '策略应用', NULL, NULL, 4, '1000059', NULL, '3', NULL, NULL, 1, 0, 0, NULL);
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000136, '其他策略管理', NULL, NULL, 4, '1000059', NULL, '3', NULL, NULL, 1, 0, 0, NULL);
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000137, '用户命令', NULL, NULL, 4, '1000060', NULL, '3', NULL, NULL, 1, 0, 0, NULL);
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000138, '用户登录成功', NULL, NULL, 4, '1000061', NULL, '3', NULL, NULL, 1, 0, 0, NULL);
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000139, '用户操作信息', NULL, NULL, 4, '1000061', NULL, '3', NULL, NULL, 1, 0, 0, NULL);
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000140, '用户登录失败', NULL, NULL, 4, '1000061', NULL, '3', NULL, NULL, 1, 0, 0, NULL);
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000141, '用户退出登录', NULL, NULL, 4, '1000061', NULL, '3', NULL, NULL, 1, 0, 0, NULL);
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000142, '运行时长', NULL, NULL, 4, '1000062', NULL, '3', NULL, NULL, 1, 0, 0, NULL);
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000143, 'CPU 利用率', NULL, NULL, 4, '1000062', NULL, '3', NULL, NULL, 1, 0, 0, NULL);
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000144, '内存利用率', NULL, NULL, 4, '1000062', NULL, '3', NULL, NULL, 1, 0, 0, NULL);
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000145, '网口流量均值', NULL, NULL, 4, '1000062', NULL, '3', NULL, NULL, 1, 0, 0, NULL);
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000146, '网口状态', NULL, NULL, 4, '1000062', NULL, '3', NULL, NULL, 1, 0, 0, NULL);
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000147, '接入设备 MAC 地址的合法性识别', NULL, NULL, 4, '1000062', NULL, '3', NULL, NULL, 1, 0, 0, NULL);
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000148, 'IP 连接信息', NULL, NULL, 4, '1000063', NULL, '3', NULL, NULL, 1, 0, 0, NULL);
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000149, '接入设备 IP 地址冲突', NULL, NULL, 4, '1000063', NULL, '3', NULL, NULL, 1, 0, 0, NULL);
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000150, '端口 UP', NULL, NULL, 4, '1000063', NULL, '3', NULL, NULL, 1, 0, 0, NULL);
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000151, '端口 DOWN', NULL, NULL, 4, '1000063', NULL, '3', NULL, NULL, 1, 0, 0, NULL);
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000152, '风扇故障', NULL, NULL, 4, '1000064', NULL, '3', NULL, NULL, 1, 0, 0, NULL);
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000153, '电源故障', NULL, NULL, 4, '1000064', NULL, '3', NULL, NULL, 1, 0, 0, NULL);
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000154, '温度异常', NULL, NULL, 4, '1000064', NULL, '3', NULL, NULL, 1, 0, 0, NULL);
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000155, '端口故障', NULL, NULL, 4, '1000064', NULL, '3', NULL, NULL, 1, 0, 0, NULL);
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000156, '电路板故障', NULL, NULL, 4, '1000064', NULL, '3', NULL, NULL, 1, 0, 0, NULL);
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000157, 'CPU 配置信息', NULL, NULL, 4, '1000066', NULL, '3', NULL, NULL, 1, 0, 0, NULL);
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000158, '内存配置信息', NULL, NULL, 4, '1000066', NULL, '3', NULL, NULL, 1, 0, 0, NULL);
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000159, '硬盘容量信息', NULL, NULL, 4, '1000066', NULL, '3', NULL, NULL, 1, 0, 0, NULL);
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000160, '硬盘配置信息', NULL, NULL, 4, '1000066', NULL, '3', NULL, NULL, 1, 0, 0, NULL);
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000161, '移动介质数量', NULL, NULL, 4, '1000066', NULL, '3', NULL, NULL, 1, 0, 0, NULL);
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000162, '串口数量信息', NULL, NULL, 4, '1000066', NULL, '3', NULL, NULL, 1, 0, 0, NULL);
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000163, '并口数量信息', NULL, NULL, 4, '1000066', NULL, '3', NULL, NULL, 1, 0, 0, NULL);
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000164, '网卡配置信息', NULL, NULL, 4, '1000066', NULL, '3', NULL, NULL, 1, 0, 0, NULL);
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000165, '操作系统版本信息', NULL, NULL, 4, '1000066', NULL, '3', NULL, NULL, 1, 0, 0, NULL);
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000166, 'CPU 平均负载', NULL, NULL, 4, '1000067', NULL, '3', NULL, NULL, 1, 0, 0, NULL);
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000167, '内存使用情况', NULL, NULL, 4, '1000067', NULL, '3', NULL, NULL, 1, 0, 0, NULL);
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000168, '硬盘使用情况', NULL, NULL, 4, '1000067', NULL, '3', NULL, NULL, 1, 0, 0, NULL);
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000169, '网卡使用情况', NULL, NULL, 4, '1000067', NULL, '3', NULL, NULL, 1, 0, 0, NULL);
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000170, 'Modem 使用情况', NULL, NULL, 4, '1000067', NULL, '3', NULL, NULL, 1, 0, 0, NULL);
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000171, '僵尸进程数量', NULL, NULL, 4, '1000067', NULL, '3', NULL, NULL, 1, 0, 0, NULL);
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000172, 'TCP 链接 CLOSE_WAIT 数量', NULL, NULL, 4, '1000067', NULL, '3', NULL, NULL, 1, 0, 0, NULL);
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000173, '网络端口监听情况', NULL, NULL, 4, '1000067', NULL, '3', NULL, NULL, 1, 0, 0, NULL);
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000174, '主板温度状态', NULL, NULL, 4, '1000067', NULL, '3', NULL, NULL, 1, 0, 0, NULL);
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000175, '风扇转数状态', NULL, NULL, 4, '1000067', NULL, '3', NULL, NULL, 1, 0, 0, NULL);
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000176, 'USB 使用情况', NULL, NULL, 4, '1000067', NULL, '3', NULL, NULL, 1, 0, 0, NULL);
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000177, '光驱使用情况', NULL, NULL, 4, '1000067', NULL, '3', NULL, NULL, 1, 0, 0, NULL);
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000178, 'USB 接入', NULL, NULL, 4, '1000063', NULL, '3', NULL, NULL, 1, 0, 0, NULL);
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000179, 'USB 拔出', NULL, NULL, 4, '1000063', NULL, '3', NULL, NULL, 1, 0, 0, NULL);
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000180, '串口接入', NULL, NULL, 4, '1000063', NULL, '3', NULL, NULL, 1, 0, 0, NULL);
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000181, '串口拔出', NULL, NULL, 4, '1000063', NULL, '3', NULL, NULL, 1, 0, 0, NULL);
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000182, '并口插入', NULL, NULL, 4, '1000063', NULL, '3', NULL, NULL, 1, 0, 0, NULL);
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000183, '并口拔出', NULL, NULL, 4, '1000063', NULL, '3', NULL, NULL, 1, 0, 0, NULL);
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000184, '光驱加载光盘', NULL, NULL, 4, '1000063', NULL, '3', NULL, NULL, 1, 0, 0, NULL);
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000185, '光驱托盘弹出', NULL, NULL, 4, '1000063', NULL, '3', NULL, NULL, 1, 0, 0, NULL);
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000186, '非法外联', NULL, NULL, 4, '1000063', NULL, '3', NULL, NULL, 1, 0, 0, NULL);
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000187, '网口状态异常', NULL, NULL, 4, '1000063', NULL, '3', NULL, NULL, 1, 0, 0, NULL);
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000188, '网口状态恢复', NULL, NULL, 4, '1000063', NULL, '3', NULL, NULL, 1, 0, 0, NULL);
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000189, '主板温度过高', NULL, NULL, 4, '1000063', NULL, '3', NULL, NULL, 1, 0, 0, NULL);
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000190, '风扇故障', NULL, NULL, 4, '1000063', NULL, '3', NULL, NULL, 1, 0, 0, NULL);
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000191, '电源故障', NULL, NULL, 4, '1000063', NULL, '3', NULL, NULL, 1, 0, 0, NULL);
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000192, '非法登录尝试', NULL, NULL, 4, '1000063', NULL, '3', NULL, NULL, 1, 0, 0, NULL);
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000193, '关键文件/目录变更', NULL, NULL, 4, '1000063', NULL, '3', NULL, NULL, 1, 0, 0, NULL);
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000194, '用户权限变更', NULL, NULL, 4, '1000063', NULL, '3', NULL, NULL, 1, 0, 0, NULL);
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000195, 'USB 存储功能开启', NULL, NULL, 4, '1000063', NULL, '3', NULL, NULL, 1, 0, 0, NULL);
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000196, '主机系统命令调用', NULL, NULL, 4, '1000063', NULL, '3', NULL, NULL, 1, 0, 0, NULL);
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000197, '主机网络服务开启', NULL, NULL, 4, '1000063', NULL, '3', NULL, NULL, 1, 0, 0, NULL);
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000198, '登录成功', NULL, NULL, 4, '1000068', NULL, '3', NULL, NULL, 1, 0, 0, NULL);
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000199, '操作输入信息', NULL, NULL, 4, '1000068', NULL, '3', NULL, NULL, 1, 0, 0, NULL);
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000200, '操作回显信息', NULL, NULL, 4, '1000068', NULL, '3', NULL, NULL, 1, 0, 0, NULL);
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000201, '退出登录', NULL, NULL, 4, '1000068', NULL, '3', NULL, NULL, 1, 0, 0, NULL);
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000202, '本机登录失败', NULL, NULL, 4, '1000068', NULL, '3', NULL, NULL, 1, 0, 0, NULL);
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000203, 'USB 接入', NULL, NULL, 4, '1000065', NULL, '3', NULL, NULL, 1, 0, 0, NULL);
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000204, 'USB 拔出', NULL, NULL, 4, '1000065', NULL, '3', NULL, NULL, 1, 0, 0, NULL);
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000205, '串口占用', NULL, NULL, 4, '1000065', NULL, '3', NULL, NULL, 1, 0, 0, NULL);
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000206, '串口释放', NULL, NULL, 4, '1000065', NULL, '3', NULL, NULL, 1, 0, 0, NULL);
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000207, '并口占用', NULL, NULL, 4, '1000065', NULL, '3', NULL, NULL, 1, 0, 0, NULL);
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000208, '并口释放', NULL, NULL, 4, '1000065', NULL, '3', NULL, NULL, 1, 0, 0, NULL);
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000209, '光驱加载', NULL, NULL, 4, '1000065', NULL, '3', NULL, NULL, 1, 0, 0, NULL);
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000210, '光驱卸载', NULL, NULL, 4, '1000065', NULL, '3', NULL, NULL, 1, 0, 0, NULL);
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000211, '非法外联', NULL, NULL, 4, '1000065', NULL, '3', NULL, NULL, 1, 0, 0, NULL);
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000212, '存在光驱设备', NULL, NULL, 4, '1000065', NULL, '3', NULL, NULL, 1, 0, 0, NULL);
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000213, '开放非法端口', NULL, NULL, 4, '1000065', NULL, '3', NULL, NULL, 1, 0, 0, NULL);
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000214, '网口 up', NULL, NULL, 4, '1000065', NULL, '3', NULL, NULL, 1, 0, 0, NULL);
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000215, '网口 down', NULL, NULL, 4, '1000065', NULL, '3', NULL, NULL, 1, 0, 0, NULL);
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000216, '关键文件/目录变更', NULL, NULL, 4, '1000065', NULL, '3', NULL, NULL, 1, 0, 0, NULL);
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000217, '用户权限变更', NULL, NULL, 4, '1000065', NULL, '3', NULL, NULL, 1, 0, 0, NULL);
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000218, '数据库CPU利用率', NULL, NULL, 4, '1000069', NULL, '3', NULL, NULL, 1, 0, 0, NULL);
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000219, '数据库内存利用率', NULL, NULL, 4, '1000069', NULL, '3', NULL, NULL, 1, 0, 0, NULL);
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000220, '数据库磁盘信息-数据文件', NULL, NULL, 4, '1000069', NULL, '3', NULL, NULL, 1, 0, 0, NULL);
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000221, '数据库磁盘信息-归档文件', NULL, NULL, 4, '1000069', NULL, '3', NULL, NULL, 1, 0, 0, NULL);
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000222, '数据库磁盘信息-备份文件', NULL, NULL, 4, '1000069', NULL, '3', NULL, NULL, 1, 0, 0, NULL);
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000223, '数据库表空间使用情况', NULL, NULL, 4, '1000069', NULL, '3', NULL, NULL, 1, 0, 0, NULL);
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000224, '数据库连接使用情况', NULL, NULL, 4, '1000069', NULL, '3', NULL, NULL, 1, 0, 0, NULL);
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000225, '数据库运行时长', NULL, NULL, 4, '1000069', NULL, '3', NULL, NULL, 1, 0, 0, NULL);
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000226, '数据库状态', NULL, NULL, 4, '1000069', NULL, '3', NULL, NULL, 1, 0, 0, NULL);
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000227, '数据库操作记录', NULL, NULL, 4, '1000069', NULL, '3', NULL, NULL, 1, 0, 0, NULL);
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000228, '数据库用户信息变更', NULL, NULL, 4, '1000069', NULL, '3', NULL, NULL, 1, 0, 0, NULL);
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000229, '数据库用户登录失败', NULL, NULL, 4, '1000070', NULL, '3', NULL, NULL, 1, 0, 0, NULL);
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000230, '锁表-sql语句长时间未提交', NULL, NULL, 4, '1000070', NULL, '3', NULL, NULL, 1, 0, 0, NULL);
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000231, '数据计划任务执行失败', NULL, NULL, 4, '1000070', NULL, '3', NULL, NULL, 1, 0, 0, NULL);
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000232, '锁表-sql语句执行时间异常', NULL, NULL, 4, '1000070', NULL, '3', NULL, NULL, 1, 0, 0, NULL);
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000233, '锁表-数据库脏页面占比情况', NULL, NULL, 4, '1000070', NULL, '3', NULL, NULL, 1, 0, 0, NULL);
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000234, '修改策略', NULL, NULL, 4, '1000068', NULL, '3', NULL, NULL, 1, 0, 0, NULL);
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000235, 'CPU 利用率', NULL, NULL, 4, '1000071', NULL, '3', NULL, NULL, 1, 0, 0, NULL);
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000236, '内存使用率', NULL, NULL, 4, '1000071', NULL, '3', NULL, NULL, 1, 0, 0, NULL);
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000237, '电源故障', NULL, NULL, 4, '1000071', NULL, '3', NULL, NULL, 1, 0, 0, NULL);
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000238, '风扇故障', NULL, NULL, 4, '1000071', NULL, '3', NULL, NULL, 1, 0, 0, NULL);
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000239, '温度异常', NULL, NULL, 4, '1000071', NULL, '3', NULL, NULL, 1, 0, 0, NULL);
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000240, '网口状态异常', NULL, NULL, 4, '1000071', NULL, '3', NULL, NULL, 1, 0, 0, NULL);
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000241, '不符合安全策略访问', NULL, NULL, 4, '1000065', NULL, '3', NULL, NULL, 1, 0, 0, NULL);
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000242, '攻击告警', NULL, NULL, 4, '1000065', NULL, '3', NULL, NULL, 1, 0, 0, NULL);
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000243, '系统登录', NULL, NULL, 4, '1000071', NULL, '3', NULL, NULL, 1, 0, 0, NULL);
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000244, '修改设备配置', NULL, NULL, 4, '1000071', NULL, '3', NULL, NULL, 1, 0, 0, NULL);
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000245, '修改配置', NULL, NULL, 4, '1000068', NULL, '3', NULL, NULL, 1, 0, 0, NULL);
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000246, '网口状态恢复', NULL, NULL, 4, '1000071', NULL, '3', NULL, NULL, 1, 0, 0, NULL);
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000247, '隧道建立错误', NULL, NULL, 4, '1000065', NULL, '3', NULL, NULL, 1, 0, 0, NULL);
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000248, '病毒检测', NULL, NULL, 4, '1000072', NULL, '3', NULL, NULL, 1, 0, 0, NULL);
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000249, '入侵保护事件', NULL, NULL, 4, '1000073', NULL, '3', NULL, NULL, 1, 0, 0, NULL);
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000250, 'USB设备拔出', NULL, NULL, 4, '1000061', NULL, '3', NULL, NULL, 1, 0, 0, NULL);
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000251, '本地管理界面登录成功', NULL, NULL, 4, '1000061', NULL, '3', NULL, NULL, 1, 0, 0, NULL);
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000252, '本地管理界面退出登录', NULL, NULL, 4, '1000061', NULL, '3', NULL, NULL, 1, 0, 0, NULL);
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000253, '配置变更', NULL, NULL, 4, '1000061', NULL, '3', NULL, NULL, 1, 0, 0, NULL);
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000254, '外联事件', NULL, NULL, 4, '1000065', NULL, '3', NULL, NULL, 1, 0, 0, NULL);
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000255, '系统登录失败超过阈值', NULL, NULL, 4, '1000065', NULL, '3', NULL, NULL, 1, 0, 0, NULL);
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000256, '危险操作', NULL, NULL, 4, '1000065', NULL, '3', NULL, NULL, 1, 0, 0, NULL);
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000257, 'CPU利用率超过阈值', NULL, NULL, 4, '1000065', NULL, '3', NULL, NULL, 1, 0, 0, NULL);
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000258, '内存使用率超过阈值', NULL, NULL, 4, '1000065', NULL, '3', NULL, NULL, 1, 0, 0, NULL);
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000259, '磁盘空间使用率超过阈值', NULL, NULL, 4, '1000065', NULL, '3', NULL, NULL, 1, 0, 0, NULL);
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000260, '本地管理界面登录失败被锁定', NULL, NULL, 4, '1000065', NULL, '3', NULL, NULL, 1, 0, 0, NULL);
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000261, '装置异常告警', NULL, NULL, 4, '1000065', NULL, '3', NULL, NULL, 1, 0, 0, NULL);
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000262, '对时异常', NULL, NULL, 4, '1000065', NULL, '3', NULL, NULL, 1, 0, 0, NULL);
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000263, '验签错误', NULL, NULL, 4, '1000065', NULL, '3', NULL, NULL, 1, 0, 0, NULL);
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000264, 'MAC 地址绑定关系变更', NULL, NULL, 4, '1000061', NULL, '3', NULL, NULL, 1, 0, 0, NULL);
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000265, '交换机上线', NULL, NULL, 4, '1000061', NULL, '3', NULL, NULL, 1, 0, 0, NULL);
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000266, '修改用户密码', NULL, NULL, 4, '1000061', NULL, '3', NULL, NULL, 1, 0, 0, NULL);
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000267, '网口流量超过阈值', NULL, NULL, 4, '1000065', NULL, '3', NULL, NULL, 1, 0, 0, NULL);
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000268, '交换机离线', NULL, NULL, 4, '1000065', NULL, '3', NULL, NULL, 1, 0, 0, NULL);
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000269, '端口未绑定 MAC 地址', NULL, NULL, 4, '1000065', NULL, '3', NULL, NULL, 1, 0, 0, NULL);
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000270, '修改策略', NULL, NULL, 4, '1000061', NULL, '3', NULL, NULL, 1, 0, 0, NULL);
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000271, '防火墙上线', NULL, NULL, 4, '1000061', NULL, '3', NULL, NULL, 1, 0, 0, NULL);
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000272, '防火墙离线', NULL, NULL, 4, '1000065', NULL, '3', NULL, NULL, 1, 0, 0, NULL);
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000273, '隔离装置上线', NULL, NULL, 4, '1000061', NULL, '3', NULL, NULL, 1, 0, 0, NULL);
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000275, '系统登录', NULL, NULL, 4, '1000061', NULL, '3', NULL, NULL, 1, 0, 0, NULL);
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000276, '修改设备配置', NULL, NULL, 4, '1000061', NULL, '3', NULL, NULL, 1, 0, 0, NULL);
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000277, '隔离装置离线', NULL, NULL, 4, '1000065', NULL, '3', NULL, NULL, 1, 0, 0, NULL);
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000281, '设备上线', NULL, NULL, 4, '1000061', NULL, '3', NULL, NULL, 1, 0, 0, NULL);
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000282, '关键文件变更', NULL, NULL, 4, '1000065', NULL, '3', NULL, NULL, 1, 0, 0, NULL);
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000283, '设备离线', NULL, NULL, 4, '1000065', NULL, '3', NULL, NULL, 1, 0, 0, NULL);
INSERT INTO `rzcj_standard_strategy_field` VALUES (1000000284, '存在光驱告警', NULL, NULL, 4, '1000065', NULL, '3', NULL, NULL, 1, 0, 0, NULL);

-- ----------------------------
-- Table structure for rzcj_standard_strategy_regular
-- ----------------------------
DROP TABLE IF EXISTS `rzcj_standard_strategy_regular`;
CREATE TABLE `rzcj_standard_strategy_regular`  (
  `id` varchar(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `sample_log` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '日志样例',
  `regular` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '正则表达式',
  `standard_strategy_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '采集ID',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '日志采集策略--正则表达式存储' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of rzcj_standard_strategy_regular
-- ----------------------------

-- ----------------------------
-- Table structure for rzcj_standard_strategy_value
-- ----------------------------
DROP TABLE IF EXISTS `rzcj_standard_strategy_value`;
CREATE TABLE `rzcj_standard_strategy_value`  (
  `id` varchar(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `public_field_one_id` varchar(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '一级字段ID',
  `public_field_two_id` varchar(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '二级字段ID',
  `public_field_two_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `public_field_three_id` varchar(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '三级字段ID',
  `public_field_three_value` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '一级字段值',
  `public_field_four_id` varchar(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '四级字段ID',
  `create_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `standard_strategy_id` varchar(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '日志采集器ID',
  `regular_id` varchar(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '正则表达式ID',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '采集编辑器公共字段对应值' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of rzcj_standard_strategy_value
-- ----------------------------

-- ----------------------------
-- Table structure for rzcj_storage_config
-- ----------------------------
DROP TABLE IF EXISTS `rzcj_storage_config`;
CREATE TABLE `rzcj_storage_config`  (
  `id` varchar(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `retention_period` int NULL DEFAULT NULL COMMENT '日志保留期限 (天)',
  `delete_threshold_day` int NULL DEFAULT NULL COMMENT '磁盘使用率删除阈值（时间） (天)',
  `delete_threshold_size` int NULL DEFAULT NULL COMMENT '磁盘使用率删除阈值（大小） (%)',
  `usage_reminder_threshold_size` int NULL DEFAULT NULL COMMENT '磁盘使用率提醒阈值（大小） (%)',
  `storage_fields` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '存储字段',
  `create_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人',
  `create_time` datetime NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '日志采集存储配置' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of rzcj_storage_config
-- ----------------------------
INSERT INTO `rzcj_storage_config` VALUES ('1', 180, 180, 85, 70, '', 'admin', '2024-12-17 17:41:01');

-- ----------------------------
-- Table structure for strategy_manage
-- ----------------------------
DROP TABLE IF EXISTS `strategy_manage`;
CREATE TABLE `strategy_manage`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT 'id',
  `strategy_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '策略名称',
  `event_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '事件类型',
  `event_sub_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '事件子类型',
  `combine_condition` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '合并条件',
  `seconds_range` int NULL DEFAULT NULL COMMENT '秒数范围',
  `amount` int NULL DEFAULT NULL COMMENT '数量',
  `continue_flag` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '1' COMMENT '命中后继续（0-否1-是）',
  `audit_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '审计名称',
  `audit_type` int NULL DEFAULT NULL COMMENT '审计类型',
  `audit_grade` int NULL DEFAULT NULL COMMENT '审计级别(1-一般2-警告3-严重4-极度严重)',
  `description` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '描述',
  `sys_default` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '是否内置（0-否1-是）',
  `status` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '1' COMMENT '状态（0-停用1-启用）',
  `priority` int NULL DEFAULT NULL COMMENT '处理顺序',
  `create_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建事件',
  `update_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人',
  `update_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 151 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '审计策略' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of strategy_manage
-- ----------------------------
INSERT INTO `strategy_manage` VALUES (63, '登录成功审计', '1000028', '1000000112', '', NULL, NULL, '1', '登录成功审计', 1, 0, '', '1', '0', 1, 'zqj', '2024-10-28 11:36:40', 'xdl', '2024-12-13 10:07:54');
INSERT INTO `strategy_manage` VALUES (64, '登录失败审计', '1000028', '1000000112', '', NULL, NULL, '1', '登录失败审计', 1, 0, '', '1', '0', 2, 'zqj', '2024-10-28 11:42:53', 'xdl', '2024-12-13 10:08:04');
INSERT INTO `strategy_manage` VALUES (65, '启动进程审计', '1000026', '1000000099', '', NULL, NULL, '1', '启动进程审计', 2, 0, '', '1', '0', 4, 'zqj', '2024-10-28 11:44:07', 'xdl', '2024-12-13 10:08:20');
INSERT INTO `strategy_manage` VALUES (66, 'SU失败动作审计', '1000028', '1000000114', '', NULL, NULL, '1', 'SU失败动作审计', 1, 0, '', '1', '0', 3, 'zqj', '2024-10-28 12:06:37', 'xdl', '2024-12-13 10:08:12');
INSERT INTO `strategy_manage` VALUES (68, '拒绝可疑连接行为审计', '1000027', '1000000104', '', NULL, NULL, '1', '拒绝可疑连接行为审计', 3, 1, '', '1', '0', 5, 'zqj', '2024-10-28 15:18:29', 'xdl', '2024-12-13 10:08:22');
INSERT INTO `strategy_manage` VALUES (69, 'su_root动作审计', '1000028', '1000000114', '', NULL, NULL, '1', 'su_root动作审计', 1, 0, '', '1', '0', 6, 'zqj', '2024-10-28 15:20:34', 'xdl', '2024-12-13 10:08:25');
INSERT INTO `strategy_manage` VALUES (70, 'SU成功动作审计', '1000028', '1000000114', '', NULL, NULL, '1', 'SU成功动作审计', 1, 0, '', '1', '0', 7, 'zqj', '2024-10-28 15:21:41', 'xdl', '2024-12-13 10:08:29');
INSERT INTO `strategy_manage` VALUES (71, '普通病毒感染告警审计', '1000013', '1000000001', '', NULL, NULL, '1', '普通病毒感染告警审计', 4, 0, '', '1', '0', 8, 'zqj', '2024-10-28 15:24:24', 'xdl', '2024-12-13 10:08:35');
INSERT INTO `strategy_manage` VALUES (72, '高风险病毒感染告警审计', '1000013', '1000000001', '', NULL, NULL, '1', '高风险病毒感染告警审计', 4, 2, '', '1', '0', 9, 'zqj', '2024-10-28 15:25:11', 'xdl', '2024-12-13 10:08:38');
INSERT INTO `strategy_manage` VALUES (73, '用户注销审计', '1000028', '1000000113', '', NULL, NULL, '1', '用户注销审计', 1, 1, '', '1', '0', 10, 'zqj', '2024-10-28 15:26:06', 'xdl', '2024-12-13 10:08:41');
INSERT INTO `strategy_manage` VALUES (74, '帐户变更审计', '1000029', '1000000119', '', NULL, NULL, '1', '帐户变更审计', 5, 1, '', '1', '0', 11, 'zqj', '2024-10-28 15:26:50', 'xdl', '2024-12-13 09:40:43');
INSERT INTO `strategy_manage` VALUES (75, '口令变更审计', '1000029', '1000000120', '', NULL, NULL, '1', '口令变更审计', 5, 1, '', '1', '0', 12, 'zqj', '2024-10-28 15:37:24', 'xdl', '2024-12-13 09:40:51');
INSERT INTO `strategy_manage` VALUES (76, '帐户权限变更审计', '1000029', '1000000121', '', NULL, NULL, '1', '帐户权限变更审计', 5, 1, '', '1', '0', 13, 'zqj', '2024-10-28 16:10:23', 'xdl', '2024-12-13 09:40:57');
INSERT INTO `strategy_manage` VALUES (77, '可执行文件安装审计', '1000026', '1000000101', '', NULL, NULL, '1', '可执行文件安装审计', 7, 1, '', '1', '0', 14, 'zqj', '2024-10-28 16:13:15', 'xdl', '2024-12-13 09:41:02');
INSERT INTO `strategy_manage` VALUES (79, '系统重启审计', '1000026', '1000000098', '', NULL, NULL, '1', '系统重启审计', 2, 1, '', '1', '0', 15, 'zqj', '2024-10-28 16:29:06', 'xdl', '2024-12-13 09:41:06');
INSERT INTO `strategy_manage` VALUES (80, '网络蠕虫行为审计', '1000013', '1000000002', '', NULL, NULL, '1', '网络蠕虫行为审计', 4, 1, '', '1', '0', 16, 'zqj', '2024-10-28 16:47:57', 'xdl', '2024-12-13 09:41:14');
INSERT INTO `strategy_manage` VALUES (81, '网络探测威胁行为审计', '1000014', '1000000011', '', NULL, NULL, '1', '网络探测威胁行为审计', 8, 1, '', '1', '0', 17, 'zqj', '2024-10-28 16:48:40', 'xdl', '2024-12-13 09:41:19');
INSERT INTO `strategy_manage` VALUES (83, '网络拒绝服务行为审计', '1000014', '1000000018', '', NULL, NULL, '1', '网络拒绝服务行为审计', 8, 1, '', '1', '0', 18, 'zqj', '2024-10-28 17:03:58', 'xdl', '2024-12-13 09:41:23');
INSERT INTO `strategy_manage` VALUES (84, '数据库用户登录审计', '1000028', '1000000112', '', NULL, NULL, '1', '数据库用户登录审计', 5, 0, '', '1', '0', 19, 'zqj', '2024-10-28 17:05:29', 'xdl', '2024-12-13 09:41:28');
INSERT INTO `strategy_manage` VALUES (85, '数据库帐户口令变更审计', '1000029', '1000000120', '', NULL, NULL, '1', '数据库帐户口令变更审计', 5, 0, '', '1', '0', 20, 'zqj', '2024-10-28 17:06:26', 'xdl', '2024-12-13 09:41:33');
INSERT INTO `strategy_manage` VALUES (86, '帐户管理变更审计', '1000029', NULL, '', NULL, NULL, '1', '帐户管理变更审计', 5, 0, '', '1', '0', 21, 'zqj', '2024-10-28 17:07:19', 'xdl', '2024-12-13 09:44:01');
INSERT INTO `strategy_manage` VALUES (87, '审计策略变更审计', '1000059', '1000000134', '', NULL, NULL, '1', '审计策略变更审计', 5, 0, '', '1', '0', 22, 'zqj', '2024-10-28 17:09:34', 'xdl', '2024-12-13 09:45:34');
INSERT INTO `strategy_manage` VALUES (88, '对象访问动作审计', NULL, NULL, '', NULL, NULL, '1', '对象访问动作审计', 7, 0, '', '1', '0', 23, 'zqj', '2024-10-28 17:11:44', 'xdl', '2024-12-13 09:46:05');
INSERT INTO `strategy_manage` VALUES (89, '数据库高危操作审计', '1000060', NULL, '', NULL, NULL, '1', '数据库高危操作审计', 10, 1, '', '1', '0', 24, 'zqj', '2024-10-28 17:13:09', 'xdl', '2024-12-13 09:46:10');
INSERT INTO `strategy_manage` VALUES (90, '数据库Insert动作审计', '1000060', NULL, '', NULL, NULL, '1', '数据库Insert动作审计', 10, 1, '', '1', '0', 25, 'zqj', '2024-10-28 17:14:08', 'xdl', '2024-12-13 09:46:15');
INSERT INTO `strategy_manage` VALUES (94, '数据库Delete动作审计', '1000060', NULL, '', NULL, NULL, '1', '数据库Delete动作审计', 10, 1, '', '1', '0', 26, 'zqj', '2024-10-28 17:18:01', 'xdl', '2024-12-13 09:46:21');
INSERT INTO `strategy_manage` VALUES (95, '数据库Create动作审计', '1000060', NULL, '', NULL, NULL, '1', '数据库Create动作审计', 10, 1, '', '1', '0', 27, 'zqj', '2024-10-28 17:18:39', 'xdl', '2024-12-13 09:46:26');
INSERT INTO `strategy_manage` VALUES (96, '数据库Drop动作审计', '1000060', NULL, '', NULL, NULL, '1', '数据库Drop动作审计', 10, 1, '', '1', '0', 28, 'zqj', '2024-10-28 17:19:49', 'xdl', '2024-12-13 09:46:30');
INSERT INTO `strategy_manage` VALUES (97, '数据库Update动作审计', '1000060', NULL, '', NULL, NULL, '1', '数据库Update动作审计', 10, 1, '', '1', '0', 29, 'zqj', '2024-10-28 17:21:12', 'xdl', '2024-12-13 09:46:35');
INSERT INTO `strategy_manage` VALUES (123, '策略修改审计', '1000059', '1000000134', NULL, NULL, NULL, '1', '策略修改审计', 6, 1, '', '1', '0', 30, 'zqj', '2024-11-13 14:03:38', 'xdl', '2024-12-13 09:46:40');

-- ----------------------------
-- Table structure for strategy_manage_action
-- ----------------------------
DROP TABLE IF EXISTS `strategy_manage_action`;
CREATE TABLE `strategy_manage_action`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT 'id',
  `strategy_id` int NULL DEFAULT NULL COMMENT '模板Id',
  `belong_to` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '属于（0-属于1-不属于）',
  `action` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '行为动作',
  `action_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '动作类型（0-不考虑结果1-成功2-失败）',
  `create_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人',
  `update_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 90 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '策略行为' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of strategy_manage_action
-- ----------------------------
INSERT INTO `strategy_manage_action` VALUES (40, 63, '0', 'login', '1', 'xdl', '2024-12-13 10:07:54', NULL, NULL);
INSERT INTO `strategy_manage_action` VALUES (41, 64, '0', 'login', '2', 'xdl', '2024-12-13 10:08:04', NULL, NULL);
INSERT INTO `strategy_manage_action` VALUES (42, 65, '0', 'create process', '0', 'xdl', '2024-12-13 10:08:20', NULL, NULL);
INSERT INTO `strategy_manage_action` VALUES (43, 66, '0', 'su', '2', 'xdl', '2024-12-13 10:08:12', NULL, NULL);
INSERT INTO `strategy_manage_action` VALUES (44, 68, '0', 'deny', '0', 'xdl', '2024-12-13 10:08:22', NULL, NULL);
INSERT INTO `strategy_manage_action` VALUES (45, 69, '0', 'su', '0', 'xdl', '2024-12-13 10:08:25', NULL, NULL);
INSERT INTO `strategy_manage_action` VALUES (46, 70, '0', 'su', '1', 'xdl', '2024-12-13 10:08:29', NULL, NULL);
INSERT INTO `strategy_manage_action` VALUES (47, 88, '0', 'write', '0', 'xdl', '2024-12-13 09:46:05', NULL, NULL);
INSERT INTO `strategy_manage_action` VALUES (48, 89, '0', 'update', '0', 'xdl', '2024-12-13 09:46:10', NULL, NULL);
INSERT INTO `strategy_manage_action` VALUES (73, 88, '0', 'read', '0', 'xdl', '2024-12-13 09:46:05', NULL, NULL);
INSERT INTO `strategy_manage_action` VALUES (74, 88, '0', 'delete', '0', 'xdl', '2024-12-13 09:46:05', NULL, NULL);
INSERT INTO `strategy_manage_action` VALUES (75, 89, '0', 'grant', '0', 'xdl', '2024-12-13 09:46:10', NULL, NULL);
INSERT INTO `strategy_manage_action` VALUES (76, 89, '0', 'insert', '0', 'xdl', '2024-12-13 09:46:10', NULL, NULL);
INSERT INTO `strategy_manage_action` VALUES (77, 89, '0', 'revoke', '0', 'xdl', '2024-12-13 09:46:10', NULL, NULL);
INSERT INTO `strategy_manage_action` VALUES (78, 89, '0', 'drop', '0', 'xdl', '2024-12-13 09:46:10', NULL, NULL);
INSERT INTO `strategy_manage_action` VALUES (79, 89, '0', 'delete', '0', 'xdl', '2024-12-13 09:46:10', NULL, NULL);
INSERT INTO `strategy_manage_action` VALUES (80, 89, '0', 'cursor', '0', 'xdl', '2024-12-13 09:46:10', NULL, NULL);
INSERT INTO `strategy_manage_action` VALUES (81, 89, '0', 'create', '0', 'xdl', '2024-12-13 09:46:10', NULL, NULL);
INSERT INTO `strategy_manage_action` VALUES (83, 94, '0', 'delete', '0', 'xdl', '2024-12-13 09:46:21', NULL, NULL);
INSERT INTO `strategy_manage_action` VALUES (84, 90, '0', 'insert', '0', 'xdl', '2024-12-13 09:46:15', NULL, NULL);
INSERT INTO `strategy_manage_action` VALUES (85, 95, '0', 'create', '0', 'xdl', '2024-12-13 09:46:26', NULL, NULL);
INSERT INTO `strategy_manage_action` VALUES (86, 96, '0', 'drop', '0', 'xdl', '2024-12-13 09:46:30', NULL, NULL);
INSERT INTO `strategy_manage_action` VALUES (87, 97, '0', 'update', '0', 'xdl', '2024-12-13 09:46:35', NULL, NULL);

-- ----------------------------
-- Table structure for strategy_manage_action_executor
-- ----------------------------
DROP TABLE IF EXISTS `strategy_manage_action_executor`;
CREATE TABLE `strategy_manage_action_executor`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT 'id',
  `strategy_id` int NULL DEFAULT NULL COMMENT '策略Id',
  `executor_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '执行类别（0-人员名称1-账号）',
  `belong_to` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '属于（0-属于1-不属于）',
  `auditor_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '审计人员',
  `device_address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '设备地址',
  `account_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '账号名称',
  `create_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '更新人',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 76 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '审计行为执行者' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of strategy_manage_action_executor
-- ----------------------------

-- ----------------------------
-- Table structure for strategy_manage_action_source
-- ----------------------------
DROP TABLE IF EXISTS `strategy_manage_action_source`;
CREATE TABLE `strategy_manage_action_source`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT 'id',
  `strategy_id` int NULL DEFAULT NULL COMMENT '策略Id',
  `belong_to` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '属于（0-属于1-不属于）',
  `ip_address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT 'IP地址',
  `create_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` datetime NULL DEFAULT NULL COMMENT '更新人',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 38 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '审计行为来源' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of strategy_manage_action_source
-- ----------------------------

-- ----------------------------
-- Table structure for strategy_manage_condition
-- ----------------------------
DROP TABLE IF EXISTS `strategy_manage_condition`;
CREATE TABLE `strategy_manage_condition`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT 'id',
  `strategy_id` int NULL DEFAULT NULL COMMENT '模板Id',
  `condition` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '条件',
  `operator` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '运算符',
  `condition_value` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '条件值',
  `condition_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '条件类型',
  `create_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人',
  `update_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 128 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '策略条件' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of strategy_manage_condition
-- ----------------------------
INSERT INTO `strategy_manage_condition` VALUES (75, 69, 'dest_user', '=', 'root', '1', 'xdl', '2024-12-13 10:08:25', NULL, NULL);
INSERT INTO `strategy_manage_condition` VALUES (81, 84, 'action_object_type', '=', '数据库', '1', 'xdl', '2024-12-13 09:41:28', NULL, NULL);
INSERT INTO `strategy_manage_condition` VALUES (82, 85, 'action_object_type', '=', '数据库', '1', 'xdl', '2024-12-13 09:41:33', NULL, NULL);
INSERT INTO `strategy_manage_condition` VALUES (83, 89, 'action_object_type', '=', '数据库', '1', 'xdl', '2024-12-13 09:46:10', NULL, NULL);
INSERT INTO `strategy_manage_condition` VALUES (84, 90, 'action_object_type', '=', '数据库', '1', 'xdl', '2024-12-13 09:46:16', NULL, NULL);
INSERT INTO `strategy_manage_condition` VALUES (85, 94, 'action_object_type', '=', '数据库', '1', 'xdl', '2024-12-13 09:46:21', NULL, NULL);
INSERT INTO `strategy_manage_condition` VALUES (86, 95, 'action_object_type', '=', '数据库', '1', 'xdl', '2024-12-13 09:46:26', NULL, NULL);
INSERT INTO `strategy_manage_condition` VALUES (87, 96, 'action_object_type', '=', '数据库', '1', 'xdl', '2024-12-13 09:46:30', NULL, NULL);
INSERT INTO `strategy_manage_condition` VALUES (88, 97, 'action_object_type', '=', '数据库', '1', 'xdl', '2024-12-13 09:46:35', NULL, NULL);
INSERT INTO `strategy_manage_condition` VALUES (100, 71, 'severity_level', '<=', '2', '2', 'xdl', '2024-12-13 10:08:35', NULL, NULL);
INSERT INTO `strategy_manage_condition` VALUES (101, 72, 'severity_level', '>', '2', '2', 'xdl', '2024-12-13 10:08:38', NULL, NULL);

-- ----------------------------
-- Table structure for strategy_manage_period
-- ----------------------------
DROP TABLE IF EXISTS `strategy_manage_period`;
CREATE TABLE `strategy_manage_period`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT 'id',
  `strategy_id` int NULL DEFAULT NULL COMMENT '模板Id',
  `period_type` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '类型（0-每日1-日期）',
  `belong_to` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '属于（0-属于1-不属于）',
  `time_begin` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '时间开始',
  `time_end` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '时间结束',
  `week_begin` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '星期开始',
  `week_end` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '星期结束',
  `create_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人',
  `update_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 56 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '策略有效时间段' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of strategy_manage_period
-- ----------------------------

-- ----------------------------
-- Table structure for strategy_manage_target
-- ----------------------------
DROP TABLE IF EXISTS `strategy_manage_target`;
CREATE TABLE `strategy_manage_target`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '审计目标id',
  `strategy_id` int NULL DEFAULT NULL COMMENT '审计策略Id',
  `target_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '审计目标类别（0-IP地址1-主机设备2-资产）',
  `ip_address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT 'IP地址',
  `belong_to` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '属于（0-属于1-不属于）',
  `device_type` int NULL DEFAULT NULL COMMENT '设备类型',
  `asset_id` int NULL DEFAULT NULL COMMENT '资产',
  `create_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '更新人',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 94 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '审计行为目标' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of strategy_manage_target
-- ----------------------------

-- ----------------------------
-- Table structure for strategy_template
-- ----------------------------
DROP TABLE IF EXISTS `strategy_template`;
CREATE TABLE `strategy_template`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT 'id',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '策略模板名称',
  `event_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '事件类型',
  `event_sub_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '事件子类型',
  `combine_condition` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '合并条件',
  `seconds_range` int NULL DEFAULT NULL COMMENT '秒数范围',
  `amount` int NULL DEFAULT NULL COMMENT '数量',
  `description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '描述',
  `sys_default` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '是否内置（0-否1-是）',
  `create_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建事件',
  `update_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人',
  `update_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 50 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '审计策略模板' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of strategy_template
-- ----------------------------
INSERT INTO `strategy_template` VALUES (17, '登录成功策略', '1000028', '1000000112', '', NULL, NULL, NULL, '1', 'admin', '2024-11-18 17:12:20', 'admin', '2024-11-18 17:12:20');
INSERT INTO `strategy_template` VALUES (18, '登录失败策略', '1000028', '1000000112', '', NULL, NULL, NULL, '1', 'admin', '2024-11-18 17:14:47', 'admin', '2024-11-18 17:14:47');
INSERT INTO `strategy_template` VALUES (19, '启动进程审计', '1000026', '1000000099', '', NULL, NULL, NULL, '1', 'admin', '2024-11-18 17:17:13', 'admin', '2024-11-18 17:17:13');
INSERT INTO `strategy_template` VALUES (20, '用户注销审计', '1000028', '1000000113', '', NULL, NULL, NULL, '1', 'admin', '2024-11-18 17:18:53', 'admin', '2024-11-18 17:18:53');
INSERT INTO `strategy_template` VALUES (21, '账户变更审计', '1000029', '1000000119', '', NULL, NULL, NULL, '1', 'admin', '2024-11-18 17:20:07', 'admin', '2024-11-18 17:20:07');
INSERT INTO `strategy_template` VALUES (22, '口令变更审计', '1000029', '1000000120', '', NULL, NULL, NULL, '1', 'admin', '2024-11-18 17:20:40', 'admin', '2024-11-18 17:20:40');
INSERT INTO `strategy_template` VALUES (23, '账户权限变更审计', '1000029', '1000000121', '', NULL, NULL, NULL, '1', 'admin', '2024-11-18 17:21:16', 'admin', '2024-11-18 17:21:16');
INSERT INTO `strategy_template` VALUES (24, '策略修改审计', '1000059', '1000000134', '', NULL, NULL, NULL, '1', 'admin', '2024-11-18 17:22:52', 'admin', '2024-11-18 17:22:52');
INSERT INTO `strategy_template` VALUES (25, '系统文件删除审计', '1000030', '1000000124', '', NULL, NULL, NULL, '1', 'admin', '2024-11-18 17:24:35', 'admin', '2024-11-18 17:24:35');
INSERT INTO `strategy_template` VALUES (26, '可执行文件安装审计', '1000026', '1000000101', '', NULL, NULL, NULL, '1', 'admin', '2024-11-18 17:27:53', 'admin', '2024-11-18 17:27:53');
INSERT INTO `strategy_template` VALUES (27, '系统重启审计', '1000026', '1000000098', '', NULL, NULL, NULL, '1', 'admin', '2024-11-18 17:28:47', 'admin', '2024-11-18 17:28:47');
INSERT INTO `strategy_template` VALUES (28, '拒绝可疑连接行为审计', '1000027', '1000000104', '', NULL, NULL, NULL, '1', 'admin', '2024-11-18 17:30:04', 'admin', '2024-11-18 17:30:04');
INSERT INTO `strategy_template` VALUES (29, '网络蠕虫行为审计', '1000013', '1000000002', '', NULL, NULL, NULL, '1', 'admin', '2024-11-18 17:31:02', 'admin', '2024-11-18 17:31:02');
INSERT INTO `strategy_template` VALUES (30, 'SU成功动作审计', '1000028', '1000000114', '', NULL, NULL, NULL, '1', 'admin', '2024-11-18 17:31:56', 'admin', '2024-11-18 17:31:56');
INSERT INTO `strategy_template` VALUES (31, 'SU动作失败审计', '1000028', '1000000114', '', NULL, NULL, NULL, '1', 'admin', '2024-11-18 17:32:38', 'admin', '2024-11-18 17:32:38');
INSERT INTO `strategy_template` VALUES (32, 'su root动作审计', '1000028', '1000000114', '', NULL, NULL, NULL, '1', 'admin', '2024-11-18 17:34:11', 'admin', '2024-11-18 17:34:11');
INSERT INTO `strategy_template` VALUES (33, '网络探测威胁行为审计', '1000014', '1000000011', '', NULL, NULL, NULL, '1', 'admin', '2024-11-18 17:35:26', 'admin', '2024-11-18 17:35:26');
INSERT INTO `strategy_template` VALUES (34, '网络拒绝服务行为审计', '1000014', '1000000018', '', NULL, NULL, NULL, '1', 'admin', '2024-11-18 17:36:23', 'admin', '2024-11-18 17:36:23');
INSERT INTO `strategy_template` VALUES (35, '普通病毒感染告警审计', '1000013', '1000000001', '', NULL, NULL, NULL, '1', 'admin', '2024-11-18 17:38:44', 'admin', '2024-11-18 17:38:44');
INSERT INTO `strategy_template` VALUES (36, '高风险病毒感染告警审计', '1000013', '1000000001', '', NULL, NULL, NULL, '1', 'admin', '2024-11-18 17:39:40', 'admin', '2024-11-18 17:39:40');
INSERT INTO `strategy_template` VALUES (38, '数据库高危操作审计', '1000060', '1000000137', '', NULL, NULL, NULL, '1', 'admin', '2024-11-18 17:50:18', 'admin', '2024-11-18 17:50:18');
INSERT INTO `strategy_template` VALUES (39, '数据库Insert动作审计', '1000060', '1000000137', '', NULL, NULL, NULL, '1', 'admin', '2024-11-18 17:52:07', 'admin', '2024-11-18 17:52:07');
INSERT INTO `strategy_template` VALUES (40, '数据库Delete动作审计', '1000060', NULL, '', NULL, NULL, NULL, '1', 'admin', '2024-11-18 17:56:17', 'admin', '2024-11-18 17:56:17');
INSERT INTO `strategy_template` VALUES (41, '数据库Create动作审计', '1000060', '', '', NULL, NULL, NULL, '1', 'admin', '2024-11-18 18:03:23', 'admin', '2024-11-18 18:03:23');
INSERT INTO `strategy_template` VALUES (42, '数据库Drop动作审计', '1000060', '', '', NULL, NULL, NULL, '1', 'admin', '2024-11-18 18:04:16', 'admin', '2024-11-18 18:04:16');
INSERT INTO `strategy_template` VALUES (43, '数据库Update动作审计', '1000060', '', '', NULL, NULL, NULL, '1', 'admin', '2024-11-18 18:05:05', 'admin', '2024-11-18 18:05:05');
INSERT INTO `strategy_template` VALUES (44, '数据库Select动作审计', '1000060', '', '', NULL, NULL, NULL, '1', 'admin', '2024-11-18 18:06:02', 'admin', '2024-11-18 18:06:02');
INSERT INTO `strategy_template` VALUES (45, '数据库用户登录审计', '1000028', '1000000112', '', NULL, NULL, NULL, '1', 'admin', '2024-11-18 18:07:27', 'admin', '2024-11-18 18:07:27');
INSERT INTO `strategy_template` VALUES (46, '数据库账户口令变更审计', '1000029', '1000000120', '', NULL, NULL, NULL, '1', 'admin', '2024-11-18 18:09:07', 'admin', '2024-11-18 18:09:07');
INSERT INTO `strategy_template` VALUES (47, '账户管理变更审计', '1000029', '', '', NULL, NULL, NULL, '1', 'admin', '2024-11-18 18:10:00', 'admin', '2024-11-18 18:10:00');
INSERT INTO `strategy_template` VALUES (48, '对象访问动作审计', NULL, NULL, '', NULL, NULL, NULL, '1', 'admin', '2024-11-18 18:11:18', 'admin', '2024-11-18 18:11:18');
INSERT INTO `strategy_template` VALUES (49, '审计策略变更审计', '1000059', '', '', NULL, NULL, NULL, '1', 'admin', '2024-11-18 18:13:05', 'admin', '2024-11-18 18:13:05');

-- ----------------------------
-- Table structure for strategy_template_action
-- ----------------------------
DROP TABLE IF EXISTS `strategy_template_action`;
CREATE TABLE `strategy_template_action`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT 'id',
  `template_id` int NULL DEFAULT NULL COMMENT '模板Id',
  `belong` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '属于（0-属于1-不属于）',
  `action` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '行为动作',
  `action_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '动作类型（0-不考虑结果1-成功2失败）',
  `create_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人',
  `update_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 38 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '策略模板行为' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of strategy_template_action
-- ----------------------------
INSERT INTO `strategy_template_action` VALUES (15, 17, '0', 'login', '1', 'admin', '2024-11-18 17:12:20', NULL, NULL);
INSERT INTO `strategy_template_action` VALUES (16, 18, '0', 'login', '2', 'admin', '2024-11-18 17:14:47', NULL, NULL);
INSERT INTO `strategy_template_action` VALUES (17, 19, '0', 'create process', '0', 'admin', '2024-11-18 17:17:13', NULL, NULL);
INSERT INTO `strategy_template_action` VALUES (18, 28, '0', 'deny', '0', 'admin', '2024-11-18 17:30:04', NULL, NULL);
INSERT INTO `strategy_template_action` VALUES (19, 30, '0', 'su', '1', 'admin', '2024-11-18 17:31:56', NULL, NULL);
INSERT INTO `strategy_template_action` VALUES (20, 31, '0', 'su', '2', 'admin', '2024-11-18 17:32:38', NULL, NULL);
INSERT INTO `strategy_template_action` VALUES (21, 38, '0', 'update', '0', 'admin', '2024-11-18 17:50:18', NULL, NULL);
INSERT INTO `strategy_template_action` VALUES (22, 38, '0', 'revoke', '0', 'admin', '2024-11-18 17:50:18', NULL, NULL);
INSERT INTO `strategy_template_action` VALUES (23, 38, '0', 'grant', '0', 'admin', '2024-11-18 17:50:18', NULL, NULL);
INSERT INTO `strategy_template_action` VALUES (24, 38, '0', 'insert', '0', 'admin', '2024-11-18 17:50:18', NULL, NULL);
INSERT INTO `strategy_template_action` VALUES (25, 38, '0', 'drop', '0', 'admin', '2024-11-18 17:50:18', NULL, NULL);
INSERT INTO `strategy_template_action` VALUES (26, 38, '0', 'delete', '0', 'admin', '2024-11-18 17:50:18', NULL, NULL);
INSERT INTO `strategy_template_action` VALUES (27, 38, '0', 'cursor', '0', 'admin', '2024-11-18 17:50:18', NULL, NULL);
INSERT INTO `strategy_template_action` VALUES (28, 38, '0', 'create', '0', 'admin', '2024-11-18 17:50:18', NULL, NULL);
INSERT INTO `strategy_template_action` VALUES (29, 39, '0', 'insert', '0', 'admin', '2024-11-18 17:52:07', NULL, NULL);
INSERT INTO `strategy_template_action` VALUES (30, 40, '0', 'delete', '0', 'admin', '2024-11-18 17:56:17', NULL, NULL);
INSERT INTO `strategy_template_action` VALUES (31, 41, '0', 'create', '0', 'admin', '2024-11-18 18:03:23', NULL, NULL);
INSERT INTO `strategy_template_action` VALUES (32, 42, '0', 'drop', '0', 'admin', '2024-11-18 18:04:16', NULL, NULL);
INSERT INTO `strategy_template_action` VALUES (33, 43, '0', 'update', '0', 'admin', '2024-11-18 18:05:05', NULL, NULL);
INSERT INTO `strategy_template_action` VALUES (34, 44, '0', 'select', '0', 'admin', '2024-11-18 18:06:02', NULL, NULL);
INSERT INTO `strategy_template_action` VALUES (35, 48, '0', 'write', '0', 'admin', '2024-11-18 18:11:18', NULL, NULL);
INSERT INTO `strategy_template_action` VALUES (36, 48, '0', 'read', '0', 'admin', '2024-11-18 18:11:18', NULL, NULL);
INSERT INTO `strategy_template_action` VALUES (37, 48, '0', 'delete', '0', 'admin', '2024-11-18 18:11:18', NULL, NULL);

-- ----------------------------
-- Table structure for strategy_template_condition
-- ----------------------------
DROP TABLE IF EXISTS `strategy_template_condition`;
CREATE TABLE `strategy_template_condition`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT 'id',
  `template_id` int NULL DEFAULT NULL COMMENT '模板Id',
  `condition` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '条件',
  `operator` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '运算符',
  `condition_value` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '条件值',
  `condition_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '条件类型',
  `create_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人',
  `update_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 30 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '策略模板条件' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of strategy_template_condition
-- ----------------------------
INSERT INTO `strategy_template_condition` VALUES (16, 25, 'related_filename', 'in', 'filename1,filename2', '1', 'admin', '2024-11-18 17:24:35', NULL, NULL);
INSERT INTO `strategy_template_condition` VALUES (17, 32, 'dest_user', '=', 'root', '1', 'admin', '2024-11-18 17:34:11', NULL, NULL);
INSERT INTO `strategy_template_condition` VALUES (18, 35, 'severity_level', '<=', '2', '2', 'admin', '2024-11-18 17:38:44', NULL, NULL);
INSERT INTO `strategy_template_condition` VALUES (19, 36, 'severity_level', '>', '2', '2', 'admin', '2024-11-18 17:39:40', NULL, NULL);
INSERT INTO `strategy_template_condition` VALUES (21, 40, 'action_object_type', '=', '数据库', '1', 'admin', '2024-11-18 17:56:17', NULL, NULL);
INSERT INTO `strategy_template_condition` VALUES (22, 39, 'action_object_type', '=', '数据库', '1', 'admin', '2024-11-18 18:02:03', NULL, NULL);
INSERT INTO `strategy_template_condition` VALUES (23, 38, 'action_object_type', '=', '数据库', '1', 'admin', '2024-11-18 18:02:03', NULL, NULL);
INSERT INTO `strategy_template_condition` VALUES (24, 41, 'action_object_type', '=', '数据库', NULL, 'admin', '2024-11-18 18:03:23', NULL, NULL);
INSERT INTO `strategy_template_condition` VALUES (25, 42, 'action_object_type', '=', '数据库', NULL, 'admin', '2024-11-18 18:04:16', NULL, NULL);
INSERT INTO `strategy_template_condition` VALUES (26, 43, 'action_object_type', '=', '数据库', NULL, 'admin', '2024-11-18 18:05:05', NULL, NULL);
INSERT INTO `strategy_template_condition` VALUES (27, 44, 'action_object_type', '=', '数据库', NULL, 'admin', '2024-11-18 18:06:02', NULL, NULL);
INSERT INTO `strategy_template_condition` VALUES (28, 45, 'action_object_type', '=', '数据库', NULL, 'admin', '2024-11-18 18:07:27', NULL, NULL);
INSERT INTO `strategy_template_condition` VALUES (29, 46, 'action_object_type', '=', '数据库', NULL, 'admin', '2024-11-18 18:09:07', NULL, NULL);

-- ----------------------------
-- Table structure for strategy_template_group
-- ----------------------------
DROP TABLE IF EXISTS `strategy_template_group`;
CREATE TABLE `strategy_template_group`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT 'id',
  `template_group_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '策略模板组名称',
  `sys_default` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '是否内置（0-否1-是）',
  `create_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人',
  `update_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 16 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '策略模板组' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of strategy_template_group
-- ----------------------------
INSERT INTO `strategy_template_group` VALUES (1, 'Windows主机', '1', 'admin', '2024-10-08 17:17:38', NULL, NULL);
INSERT INTO `strategy_template_group` VALUES (2, 'Linux/Unix主机', '1', 'admin', '2024-10-08 17:17:38', NULL, NULL);
INSERT INTO `strategy_template_group` VALUES (3, '防火墙', '1', 'admin', '2024-10-08 17:17:38', NULL, NULL);
INSERT INTO `strategy_template_group` VALUES (4, '扫描器', '1', 'admin', '2024-10-08 17:17:38', NULL, NULL);
INSERT INTO `strategy_template_group` VALUES (5, 'IDS/IPS', '1', 'admin', '2024-10-08 17:17:38', NULL, NULL);
INSERT INTO `strategy_template_group` VALUES (6, '防病毒', '1', 'admin', '2024-10-08 17:17:38', NULL, NULL);
INSERT INTO `strategy_template_group` VALUES (7, '数据库', '1', 'admin', '2024-10-08 17:17:38', NULL, NULL);
INSERT INTO `strategy_template_group` VALUES (8, '萨巴斯', '1', 'admin', '2024-10-08 17:17:38', NULL, NULL);
INSERT INTO `strategy_template_group` VALUES (9, '等级保护', '1', 'admin', '2024-10-08 17:17:38', NULL, NULL);

-- ----------------------------
-- Table structure for strategy_template_group_relation
-- ----------------------------
DROP TABLE IF EXISTS `strategy_template_group_relation`;
CREATE TABLE `strategy_template_group_relation`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `group_id` int NULL DEFAULT NULL COMMENT '模板组ID',
  `template_id` int NULL DEFAULT NULL COMMENT '模板ID',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 105 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of strategy_template_group_relation
-- ----------------------------
INSERT INTO `strategy_template_group_relation` VALUES (32, 1, 17);
INSERT INTO `strategy_template_group_relation` VALUES (33, 1, 18);
INSERT INTO `strategy_template_group_relation` VALUES (34, 1, 19);
INSERT INTO `strategy_template_group_relation` VALUES (35, 1, 20);
INSERT INTO `strategy_template_group_relation` VALUES (36, 1, 21);
INSERT INTO `strategy_template_group_relation` VALUES (37, 1, 22);
INSERT INTO `strategy_template_group_relation` VALUES (38, 1, 23);
INSERT INTO `strategy_template_group_relation` VALUES (39, 1, 24);
INSERT INTO `strategy_template_group_relation` VALUES (40, 1, 25);
INSERT INTO `strategy_template_group_relation` VALUES (41, 1, 26);
INSERT INTO `strategy_template_group_relation` VALUES (42, 1, 27);
INSERT INTO `strategy_template_group_relation` VALUES (43, 1, 28);
INSERT INTO `strategy_template_group_relation` VALUES (44, 1, 29);
INSERT INTO `strategy_template_group_relation` VALUES (65, 2, 17);
INSERT INTO `strategy_template_group_relation` VALUES (66, 2, 18);
INSERT INTO `strategy_template_group_relation` VALUES (67, 2, 20);
INSERT INTO `strategy_template_group_relation` VALUES (68, 2, 21);
INSERT INTO `strategy_template_group_relation` VALUES (69, 2, 22);
INSERT INTO `strategy_template_group_relation` VALUES (70, 2, 23);
INSERT INTO `strategy_template_group_relation` VALUES (71, 2, 24);
INSERT INTO `strategy_template_group_relation` VALUES (72, 2, 25);
INSERT INTO `strategy_template_group_relation` VALUES (73, 2, 26);
INSERT INTO `strategy_template_group_relation` VALUES (74, 2, 27);
INSERT INTO `strategy_template_group_relation` VALUES (75, 2, 30);
INSERT INTO `strategy_template_group_relation` VALUES (76, 2, 31);
INSERT INTO `strategy_template_group_relation` VALUES (77, 2, 32);
INSERT INTO `strategy_template_group_relation` VALUES (78, 3, 17);
INSERT INTO `strategy_template_group_relation` VALUES (79, 3, 28);
INSERT INTO `strategy_template_group_relation` VALUES (80, 4, 33);
INSERT INTO `strategy_template_group_relation` VALUES (81, 5, 29);
INSERT INTO `strategy_template_group_relation` VALUES (82, 5, 34);
INSERT INTO `strategy_template_group_relation` VALUES (83, 6, 35);
INSERT INTO `strategy_template_group_relation` VALUES (84, 6, 36);
INSERT INTO `strategy_template_group_relation` VALUES (85, 7, 38);
INSERT INTO `strategy_template_group_relation` VALUES (86, 7, 39);
INSERT INTO `strategy_template_group_relation` VALUES (87, 7, 40);
INSERT INTO `strategy_template_group_relation` VALUES (88, 7, 41);
INSERT INTO `strategy_template_group_relation` VALUES (89, 7, 42);
INSERT INTO `strategy_template_group_relation` VALUES (90, 7, 43);
INSERT INTO `strategy_template_group_relation` VALUES (91, 7, 44);
INSERT INTO `strategy_template_group_relation` VALUES (92, 7, 45);
INSERT INTO `strategy_template_group_relation` VALUES (93, 7, 46);
INSERT INTO `strategy_template_group_relation` VALUES (94, 8, 17);
INSERT INTO `strategy_template_group_relation` VALUES (95, 8, 18);
INSERT INTO `strategy_template_group_relation` VALUES (96, 8, 20);
INSERT INTO `strategy_template_group_relation` VALUES (97, 8, 45);
INSERT INTO `strategy_template_group_relation` VALUES (98, 8, 47);
INSERT INTO `strategy_template_group_relation` VALUES (99, 8, 48);
INSERT INTO `strategy_template_group_relation` VALUES (102, 1, 49);
INSERT INTO `strategy_template_group_relation` VALUES (103, 8, 49);
INSERT INTO `strategy_template_group_relation` VALUES (104, 9, 49);

-- ----------------------------
-- Table structure for strategy_template_period
-- ----------------------------
DROP TABLE IF EXISTS `strategy_template_period`;
CREATE TABLE `strategy_template_period`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT 'id',
  `template_id` int NULL DEFAULT NULL COMMENT '模板Id',
  `period_type` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '类型（0-每日1-星期2-日期）',
  `belong_to` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '属于（0-属于1-不属于）',
  `time_begin` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '时间开始',
  `time_end` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '时间结束',
  `week_begin` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '星期开始',
  `week_end` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '星期结束',
  `create_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人',
  `update_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 15 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '策略模板有效时间段' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of strategy_template_period
-- ----------------------------

-- ----------------------------
-- Table structure for strategy_warn_config
-- ----------------------------
DROP TABLE IF EXISTS `strategy_warn_config`;
CREATE TABLE `strategy_warn_config`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT 'id',
  `strategy_id` int NULL DEFAULT NULL COMMENT '策略Id',
  `generate_type` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '生成方式（0-自动1-自定义）',
  `warn_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '告警名称',
  `grade` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '级别（0-一般1警告2严重3极度严重）',
  `warn_type` int NULL DEFAULT NULL COMMENT '告警大类',
  `warn_sub_type` int NULL DEFAULT NULL COMMENT '告警子类',
  `create_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '更新人',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 80 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '策略告警' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of strategy_warn_config
-- ----------------------------

-- ----------------------------
-- Table structure for sys_admin_setting
-- ----------------------------
DROP TABLE IF EXISTS `sys_admin_setting`;
CREATE TABLE `sys_admin_setting`  (
  `set_id` bigint NOT NULL COMMENT 'id',
  `http_active` varchar(1) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'http管理端口（0-未启用1-启用）',
  `http_port` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '80' COMMENT 'http端口',
  `https_active` varchar(1) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'https管理端口（0-未启用1-启用）',
  `https_port` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '8443' COMMENT 'https端口',
  `host_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '主机名称',
  `three_power_active` varchar(1) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '三权分立模式（0-未启用1-启用）',
  `login_code_active` varchar(1) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '登录验证码（0-未启用1-启用）',
  `timeout` int NULL DEFAULT NULL COMMENT '页面超时时间（5-480分钟）',
  `online_admin_count` int NULL DEFAULT NULL COMMENT '在线管理员数量（1-20）',
  `fail_retry_count` int NULL DEFAULT NULL COMMENT '失败重试次数（1-60）',
  `fail_obstruct_time` int NULL DEFAULT NULL COMMENT '失败阻隔时间（1-3600秒）',
  `password_min_length` int NULL DEFAULT NULL COMMENT '密码最小长度（8-31）',
  `create_by` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建人',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '修改人',
  `update_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`set_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '系统管理设定' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_admin_setting
-- ----------------------------
INSERT INTO `sys_admin_setting` VALUES (1, '1', '80', '1', '8443', 'host', '1', '0', 30, 1, 5, 60, 8, 'admin', '2024-04-18 10:23:42', 'admin', '2024-09-09 11:02:58');

-- ----------------------------
-- Table structure for sys_config
-- ----------------------------
DROP TABLE IF EXISTS `sys_config`;
CREATE TABLE `sys_config`  (
  `config_id` int NOT NULL AUTO_INCREMENT COMMENT '参数主键',
  `config_name` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '参数名称',
  `config_key` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '参数键名',
  `config_value` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '参数键值',
  `config_type` char(1) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT 'N' COMMENT '系统内置（Y是 N否）',
  `create_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`config_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 7 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '参数配置表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_config
-- ----------------------------
INSERT INTO `sys_config` VALUES (1, '主框架页-默认皮肤样式名称', 'sys.index.skinName', 'skin-blue', 'Y', 'admin', '2024-03-19 10:23:37', '', NULL, '蓝色 skin-blue、绿色 skin-green、紫色 skin-purple、红色 skin-red、黄色 skin-yellow');
INSERT INTO `sys_config` VALUES (2, '用户管理-账号初始密码', 'sys.user.initPassword', '123456', 'Y', 'admin', '2024-03-19 10:23:37', '', NULL, '初始化密码 123456');
INSERT INTO `sys_config` VALUES (3, '主框架页-侧边栏主题', 'sys.index.sideTheme', 'theme-dark', 'Y', 'admin', '2024-03-19 10:23:37', '', NULL, '深色主题theme-dark，浅色主题theme-light');
INSERT INTO `sys_config` VALUES (4, '账号自助-验证码开关', 'sys.account.captchaEnabled', 'true', 'Y', 'admin', '2024-03-19 10:23:37', '', NULL, '是否开启验证码功能（true开启，false关闭）');
INSERT INTO `sys_config` VALUES (5, '账号自助-是否开启用户注册功能', 'sys.account.registerUser', 'false', 'Y', 'admin', '2024-03-19 10:23:37', '', NULL, '是否开启注册用户功能（true开启，false关闭）');
INSERT INTO `sys_config` VALUES (6, '用户登录-黑名单列表', 'sys.login.blackIPList', '', 'Y', 'admin', '2024-03-19 10:23:37', '', NULL, '设置登录IP黑名单限制，多个匹配项以;分隔，支持匹配（*通配、网段）');

-- ----------------------------
-- Table structure for sys_dept
-- ----------------------------
DROP TABLE IF EXISTS `sys_dept`;
CREATE TABLE `sys_dept`  (
  `dept_id` bigint NOT NULL AUTO_INCREMENT COMMENT '部门id',
  `parent_id` bigint NULL DEFAULT 0 COMMENT '父部门id',
  `ancestors` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '祖级列表',
  `dept_name` varchar(30) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '部门名称',
  `order_num` int NULL DEFAULT 0 COMMENT '显示顺序',
  `leader` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '负责人',
  `phone` varchar(11) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '联系电话',
  `email` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '邮箱',
  `status` char(1) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '0' COMMENT '部门状态（0正常 1停用）',
  `del_flag` char(1) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `create_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`dept_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '部门表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_dept
-- ----------------------------

-- ----------------------------
-- Table structure for sys_dict
-- ----------------------------
DROP TABLE IF EXISTS `sys_dict`;
CREATE TABLE `sys_dict`  (
  `id` int NOT NULL COMMENT 'ID',
  `value` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `text` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '值',
  `dict_type` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '类型',
  `log_type` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '日志类别：4:系统 2:安全 3:操作 1:入侵',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_dict
-- ----------------------------
INSERT INTO `sys_dict` VALUES (1, '1', '告警', 'sig_level', NULL);
INSERT INTO `sys_dict` VALUES (2, '2', '警示', 'sig_level', NULL);
INSERT INTO `sys_dict` VALUES (3, '3', '通知', 'sig_level', NULL);
INSERT INTO `sys_dict` VALUES (4, '4', '信息', 'sig_level', NULL);
INSERT INTO `sys_dict` VALUES (5, '1', '系统事件', 'type', '4');
INSERT INTO `sys_dict` VALUES (6, '2', '接口信息', 'type', '4');
INSERT INTO `sys_dict` VALUES (7, '3', '告警事件', 'type', '4');
INSERT INTO `sys_dict` VALUES (8, '1', '配置审计', 'oper_log_type', '3');
INSERT INTO `sys_dict` VALUES (9, '2', '审计事件', 'oper_log_type', '3');
INSERT INTO `sys_dict` VALUES (10, '3', '系统事件', 'oper_log_type', '3');
INSERT INTO `sys_dict` VALUES (11, '0', '成功', 'oper_log_status', NULL);
INSERT INTO `sys_dict` VALUES (12, '1', '失败', 'oper_log_status', NULL);
INSERT INTO `sys_dict` VALUES (13, '1', '入侵检测', 'rzsj_log_type', '1');

-- ----------------------------
-- Table structure for sys_dict_data
-- ----------------------------
DROP TABLE IF EXISTS `sys_dict_data`;
CREATE TABLE `sys_dict_data`  (
  `dict_code` bigint NOT NULL AUTO_INCREMENT COMMENT '字典编码',
  `dict_sort` int NULL DEFAULT 0 COMMENT '字典排序',
  `dict_label` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '字典标签',
  `dict_value` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '字典键值',
  `dict_type` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '字典类型',
  `css_class` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '样式属性（其他样式扩展）',
  `list_class` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '表格回显样式',
  `is_default` char(1) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT 'N' COMMENT '是否默认（Y是 N否）',
  `status` char(1) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `create_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`dict_code`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 100 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '字典数据表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_dict_data
-- ----------------------------
INSERT INTO `sys_dict_data` VALUES (1, 1, '男', '0', 'sys_user_sex', '', '', 'Y', '0', 'admin', '2024-03-19 10:23:37', '', NULL, '性别男');
INSERT INTO `sys_dict_data` VALUES (2, 2, '女', '1', 'sys_user_sex', '', '', 'N', '0', 'admin', '2024-03-19 10:23:37', '', NULL, '性别女');
INSERT INTO `sys_dict_data` VALUES (3, 3, '未知', '2', 'sys_user_sex', '', '', 'N', '0', 'admin', '2024-03-19 10:23:37', '', NULL, '性别未知');
INSERT INTO `sys_dict_data` VALUES (4, 1, '显示', '0', 'sys_show_hide', '', 'primary', 'Y', '0', 'admin', '2024-03-19 10:23:37', '', NULL, '显示菜单');
INSERT INTO `sys_dict_data` VALUES (5, 2, '隐藏', '1', 'sys_show_hide', '', 'danger', 'N', '0', 'admin', '2024-03-19 10:23:37', '', NULL, '隐藏菜单');
INSERT INTO `sys_dict_data` VALUES (6, 1, '正常', '0', 'sys_normal_disable', '', 'primary', 'Y', '0', 'admin', '2024-03-19 10:23:37', '', NULL, '正常状态');
INSERT INTO `sys_dict_data` VALUES (7, 2, '停用', '1', 'sys_normal_disable', '', 'danger', 'N', '0', 'admin', '2024-03-19 10:23:37', '', NULL, '停用状态');
INSERT INTO `sys_dict_data` VALUES (8, 1, '正常', '0', 'sys_job_status', '', 'primary', 'Y', '0', 'admin', '2024-03-19 10:23:37', '', NULL, '正常状态');
INSERT INTO `sys_dict_data` VALUES (9, 2, '暂停', '1', 'sys_job_status', '', 'danger', 'N', '0', 'admin', '2024-03-19 10:23:37', '', NULL, '停用状态');
INSERT INTO `sys_dict_data` VALUES (10, 1, '默认', 'DEFAULT', 'sys_job_group', '', '', 'Y', '0', 'admin', '2024-03-19 10:23:37', '', NULL, '默认分组');
INSERT INTO `sys_dict_data` VALUES (11, 2, '系统', 'SYSTEM', 'sys_job_group', '', '', 'N', '0', 'admin', '2024-03-19 10:23:37', '', NULL, '系统分组');
INSERT INTO `sys_dict_data` VALUES (12, 1, '是', 'Y', 'sys_yes_no', '', 'primary', 'Y', '0', 'admin', '2024-03-19 10:23:37', '', NULL, '系统默认是');
INSERT INTO `sys_dict_data` VALUES (13, 2, '否', 'N', 'sys_yes_no', '', 'danger', 'N', '0', 'admin', '2024-03-19 10:23:37', '', NULL, '系统默认否');
INSERT INTO `sys_dict_data` VALUES (14, 1, '通知', '1', 'sys_notice_type', '', 'warning', 'Y', '0', 'admin', '2024-03-19 10:23:37', '', NULL, '通知');
INSERT INTO `sys_dict_data` VALUES (15, 2, '公告', '2', 'sys_notice_type', '', 'success', 'N', '0', 'admin', '2024-03-19 10:23:37', '', NULL, '公告');
INSERT INTO `sys_dict_data` VALUES (16, 1, '正常', '0', 'sys_notice_status', '', 'primary', 'Y', '0', 'admin', '2024-03-19 10:23:37', '', NULL, '正常状态');
INSERT INTO `sys_dict_data` VALUES (17, 2, '关闭', '1', 'sys_notice_status', '', 'danger', 'N', '0', 'admin', '2024-03-19 10:23:37', '', NULL, '关闭状态');
INSERT INTO `sys_dict_data` VALUES (18, 99, '其他', '0', 'sys_oper_type', '', 'info', 'N', '0', 'admin', '2024-03-19 10:23:37', '', NULL, '其他操作');
INSERT INTO `sys_dict_data` VALUES (19, 1, '新增', '1', 'sys_oper_type', '', 'info', 'N', '0', 'admin', '2024-03-19 10:23:37', '', NULL, '新增操作');
INSERT INTO `sys_dict_data` VALUES (20, 2, '修改', '2', 'sys_oper_type', '', 'info', 'N', '0', 'admin', '2024-03-19 10:23:37', '', NULL, '修改操作');
INSERT INTO `sys_dict_data` VALUES (21, 3, '删除', '3', 'sys_oper_type', '', 'danger', 'N', '0', 'admin', '2024-03-19 10:23:37', '', NULL, '删除操作');
INSERT INTO `sys_dict_data` VALUES (22, 4, '授权', '4', 'sys_oper_type', '', 'primary', 'N', '0', 'admin', '2024-03-19 10:23:37', '', NULL, '授权操作');
INSERT INTO `sys_dict_data` VALUES (23, 5, '导出', '5', 'sys_oper_type', '', 'warning', 'N', '0', 'admin', '2024-03-19 10:23:37', '', NULL, '导出操作');
INSERT INTO `sys_dict_data` VALUES (24, 6, '导入', '6', 'sys_oper_type', '', 'warning', 'N', '0', 'admin', '2024-03-19 10:23:37', '', NULL, '导入操作');
INSERT INTO `sys_dict_data` VALUES (25, 7, '强退', '7', 'sys_oper_type', '', 'danger', 'N', '0', 'admin', '2024-03-19 10:23:37', '', NULL, '强退操作');
INSERT INTO `sys_dict_data` VALUES (26, 8, '生成代码', '8', 'sys_oper_type', '', 'warning', 'N', '0', 'admin', '2024-03-19 10:23:37', '', NULL, '生成操作');
INSERT INTO `sys_dict_data` VALUES (27, 9, '清空数据', '9', 'sys_oper_type', '', 'danger', 'N', '0', 'admin', '2024-03-19 10:23:37', '', NULL, '清空操作');
INSERT INTO `sys_dict_data` VALUES (28, 1, '成功', '0', 'sys_common_status', '', 'primary', 'N', '0', 'admin', '2024-03-19 10:23:37', '', NULL, '正常状态');
INSERT INTO `sys_dict_data` VALUES (29, 2, '失败', '1', 'sys_common_status', '', 'danger', 'N', '0', 'admin', '2024-03-19 10:23:37', '', NULL, '停用状态');

-- ----------------------------
-- Table structure for sys_dict_type
-- ----------------------------
DROP TABLE IF EXISTS `sys_dict_type`;
CREATE TABLE `sys_dict_type`  (
  `dict_id` bigint NOT NULL AUTO_INCREMENT COMMENT '字典主键',
  `dict_name` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '字典名称',
  `dict_type` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '字典类型',
  `status` char(1) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `create_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`dict_id`) USING BTREE,
  UNIQUE INDEX `dict_type`(`dict_type` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 11 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '字典类型表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_dict_type
-- ----------------------------
INSERT INTO `sys_dict_type` VALUES (1, '用户性别', 'sys_user_sex', '0', 'admin', '2024-03-19 10:23:37', '', NULL, '用户性别列表');
INSERT INTO `sys_dict_type` VALUES (2, '菜单状态', 'sys_show_hide', '0', 'admin', '2024-03-19 10:23:37', '', NULL, '菜单状态列表');
INSERT INTO `sys_dict_type` VALUES (3, '系统开关', 'sys_normal_disable', '0', 'admin', '2024-03-19 10:23:37', '', NULL, '系统开关列表');
INSERT INTO `sys_dict_type` VALUES (4, '任务状态', 'sys_job_status', '0', 'admin', '2024-03-19 10:23:37', '', NULL, '任务状态列表');
INSERT INTO `sys_dict_type` VALUES (5, '任务分组', 'sys_job_group', '0', 'admin', '2024-03-19 10:23:37', '', NULL, '任务分组列表');
INSERT INTO `sys_dict_type` VALUES (6, '系统是否', 'sys_yes_no', '0', 'admin', '2024-03-19 10:23:37', '', NULL, '系统是否列表');
INSERT INTO `sys_dict_type` VALUES (7, '通知类型', 'sys_notice_type', '0', 'admin', '2024-03-19 10:23:37', '', NULL, '通知类型列表');
INSERT INTO `sys_dict_type` VALUES (8, '通知状态', 'sys_notice_status', '0', 'admin', '2024-03-19 10:23:37', '', NULL, '通知状态列表');
INSERT INTO `sys_dict_type` VALUES (9, '操作类型', 'sys_oper_type', '0', 'admin', '2024-03-19 10:23:37', '', NULL, '操作类型列表');
INSERT INTO `sys_dict_type` VALUES (10, '系统状态', 'sys_common_status', '0', 'admin', '2024-03-19 10:23:37', '', NULL, '登录状态列表');

-- ----------------------------
-- Table structure for sys_job
-- ----------------------------
DROP TABLE IF EXISTS `sys_job`;
CREATE TABLE `sys_job`  (
  `job_id` bigint NOT NULL AUTO_INCREMENT COMMENT '任务ID',
  `job_name` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '任务名称',
  `job_group` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT 'DEFAULT' COMMENT '任务组名',
  `invoke_target` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '调用目标字符串',
  `cron_expression` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT 'cron执行表达式',
  `misfire_policy` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '3' COMMENT '计划执行错误策略（1立即执行 2执行一次 3放弃执行）',
  `concurrent` char(1) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '1' COMMENT '是否并发执行（0允许 1禁止）',
  `status` char(1) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '0' COMMENT '状态（0正常 1暂停）',
  `create_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '备注信息',
  PRIMARY KEY (`job_id`, `job_name`, `job_group`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 104 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '定时任务调度表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_job
-- ----------------------------
INSERT INTO `sys_job` VALUES (100, '磁盘使用率提醒阈值', 'DEFAULT', 'ryTask.checkDiskUsage', '0 0/5 * * * ?', '2', '1', '0', 'admin', '2024-04-30 10:45:00', 'admin', '2024-05-06 15:34:15', '');
INSERT INTO `sys_job` VALUES (101, '系统日志管理策略', 'DEFAULT', 'ryTask.checkSystemLogDay', '0 1 * * * ?', '2', '1', '0', 'admin', '2024-04-30 10:45:00', 'admin', '2024-05-06 15:34:15', '');
INSERT INTO `sys_job` VALUES (102, '监控信息外发', 'DEFAULT', 'ryTask.outInfo', '0 0/1 * * * ?', '2', '1', '1', 'admin', '2024-11-20 16:23:39', 'admin', '2024-12-13 11:49:04', '');

-- ----------------------------
-- Table structure for sys_job_log
-- ----------------------------
DROP TABLE IF EXISTS `sys_job_log`;
CREATE TABLE `sys_job_log`  (
  `job_log_id` bigint NOT NULL AUTO_INCREMENT COMMENT '任务日志ID',
  `job_name` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '任务名称',
  `job_group` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '任务组名',
  `invoke_target` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '调用目标字符串',
  `job_message` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '日志信息',
  `status` char(1) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '0' COMMENT '执行状态（0正常 1失败）',
  `exception_info` varchar(2000) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '异常信息',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`job_log_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 18322 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '定时任务调度日志表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_job_log
-- ----------------------------

-- ----------------------------
-- Table structure for sys_logininfor
-- ----------------------------
DROP TABLE IF EXISTS `sys_logininfor`;
CREATE TABLE `sys_logininfor`  (
  `info_id` bigint NOT NULL AUTO_INCREMENT COMMENT '访问ID',
  `user_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '用户账号',
  `ipaddr` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '登录IP地址',
  `login_location` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '登录地点',
  `browser` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '浏览器类型',
  `os` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '操作系统',
  `status` char(1) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '0' COMMENT '登录状态（0成功 1失败）',
  `msg` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '提示消息',
  `login_time` datetime NULL DEFAULT NULL COMMENT '访问时间',
  PRIMARY KEY (`info_id`) USING BTREE,
  INDEX `idx_sys_logininfor_s`(`status` ASC) USING BTREE,
  INDEX `idx_sys_logininfor_lt`(`login_time` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '系统访问记录' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_logininfor
-- ----------------------------

-- ----------------------------
-- Table structure for sys_menu
-- ----------------------------
DROP TABLE IF EXISTS `sys_menu`;
CREATE TABLE `sys_menu`  (
  `menu_id` bigint NOT NULL AUTO_INCREMENT COMMENT '菜单ID',
  `menu_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '菜单名称',
  `parent_id` bigint NULL DEFAULT 0 COMMENT '父菜单ID',
  `order_num` int NULL DEFAULT 0 COMMENT '显示顺序',
  `path` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '路由地址',
  `component` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '组件路径',
  `query` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '路由参数',
  `is_frame` int NULL DEFAULT 1 COMMENT '是否为外链（0是 1否）',
  `is_cache` int NULL DEFAULT 0 COMMENT '是否缓存（0缓存 1不缓存）',
  `menu_type` char(1) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '菜单类型（M目录 C菜单 F按钮）',
  `visible` char(1) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '0' COMMENT '菜单状态（0显示 1隐藏）',
  `status` char(1) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '0' COMMENT '菜单状态（0正常 1停用）',
  `perms` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '权限标识',
  `icon` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '#' COMMENT '菜单图标',
  `create_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '备注',
  PRIMARY KEY (`menu_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4001 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '菜单权限表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_menu
-- ----------------------------
INSERT INTO `sys_menu` VALUES (3000, '安全概览', 0, 16, 'index', 'index', NULL, 1, 0, 'C', '0', '0', NULL, 'securityOverview', 'admin', '2024-09-09 11:15:01', 'admin', '2024-09-09 11:15:06', '');
INSERT INTO `sys_menu` VALUES (3001, '审计仪表盘', 0, 17, 'auditDashboard', 'auditDashboard/index', NULL, 1, 0, 'C', '0', '0', NULL, 'auditDashboard', 'admin', '2024-09-09 11:15:01', 'admin', '2024-09-09 11:15:01', '');
INSERT INTO `sys_menu` VALUES (3002, '安全监控', 0, 18, 'securityMonitor', NULL, NULL, 1, 0, 'M', '0', '0', NULL, 'securityMonitor', 'admin', '2024-09-09 11:15:01', 'admin', '2024-09-09 11:15:01', '');
INSERT INTO `sys_menu` VALUES (3003, '事件分析', 0, 19, 'eventAnalysis', NULL, NULL, 1, 0, 'M', '0', '0', NULL, 'eventAnalysis', 'admin', '2024-09-09 11:15:01', 'admin', '2024-09-09 11:15:01', '');
INSERT INTO `sys_menu` VALUES (3004, '报表管理', 0, 20, 'reportManage', NULL, NULL, 1, 0, 'M', '0', '0', NULL, 'reportManage', 'admin', NULL, '', NULL, '');
INSERT INTO `sys_menu` VALUES (3005, '策略管理', 0, 21, 'strategyManage', NULL, NULL, 1, 0, 'M', '0', '0', NULL, 'strategyManage', 'admin', NULL, '', NULL, '');
INSERT INTO `sys_menu` VALUES (3006, '资产管理', 0, 22, 'assetManages', NULL, NULL, 1, 0, 'M', '0', '0', NULL, 'assetManage', 'admin', NULL, '', NULL, '');
INSERT INTO `sys_menu` VALUES (3007, '日志采集', 0, 23, 'logCollect', 'logCollect/index', NULL, 1, 0, 'C', '0', '0', NULL, 'logCollect', '', NULL, '', NULL, '');
INSERT INTO `sys_menu` VALUES (3008, '系统管理', 0, 24, 'systemManage', NULL, NULL, 1, 0, 'M', '0', '0', NULL, 'systemManage', '', NULL, '', NULL, '');
INSERT INTO `sys_menu` VALUES (3009, '告警监控', 3002, 1, 'alarmMonitor', 'securityMonitor/alarmMonitor/index', NULL, 1, 0, 'C', '0', '0', NULL, 'alarmMonitor', '', NULL, '', NULL, '');
INSERT INTO `sys_menu` VALUES (3010, '展现模式', 3002, 2, 'index1', 'index1', '', 1, 0, 'C', '0', '0', NULL, '#', '', NULL, '', NULL, '');
INSERT INTO `sys_menu` VALUES (3012, '日志列表', 3003, 1, 'logList', 'eventAnalysis/logList/index', NULL, 1, 0, 'C', '0', '0', NULL, 'logList', '', NULL, '', NULL, '');
INSERT INTO `sys_menu` VALUES (3014, '审计事件', 3003, 3, 'auditEvent', 'eventAnalysis/auditEvent/index', NULL, 1, 0, 'C', '0', '0', NULL, 'auditEvent', '', NULL, '', NULL, '');
INSERT INTO `sys_menu` VALUES (3015, '导出任务管理', 3003, 4, 'exportTaskManage', 'eventAnalysis/exportTaskManage/index', NULL, 1, 0, 'C', '0', '0', NULL, 'exportTaskManage', '', NULL, '', NULL, '');
INSERT INTO `sys_menu` VALUES (3016, '综合报告', 3004, 1, 'generalReport', 'reportManage/generalReport/index', NULL, 1, 0, 'C', '0', '0', NULL, 'generalReport', '', NULL, '', NULL, '');
INSERT INTO `sys_menu` VALUES (3017, '日常报告', 3004, 2, 'dailyReport', 'reportManage/dailyReport/index', NULL, 1, 0, 'C', '0', '0', NULL, 'dailyReport', '', NULL, '', NULL, '');
INSERT INTO `sys_menu` VALUES (3018, '审计策略', 3005, 1, 'auditStrategy', 'strategyManage/auditStrategy/index', NULL, 1, 0, 'C', '0', '0', NULL, 'auditStrategy', '', NULL, '', NULL, '');
INSERT INTO `sys_menu` VALUES (3020, '采集策略', 3005, 3, 'collectStrategy', 'strategyManage/collectStrategy/index', NULL, 1, 0, 'C', '0', '0', NULL, 'collectStrategy', '', NULL, '', NULL, '');
INSERT INTO `sys_menu` VALUES (3022, '资产管理', 3006, 1, 'assetManage', 'assetManage/assetManage/index', NULL, 1, 0, 'C', '0', '0', NULL, 'assetManage', '', NULL, '', NULL, '');
INSERT INTO `sys_menu` VALUES (3023, '用户管理', 3008, 1, 'userManage', 'systemManage/userManage/index', NULL, 1, 0, 'C', '0', '0', NULL, 'userManage', '', NULL, '', NULL, '');
INSERT INTO `sys_menu` VALUES (3024, '日志管理', 3008, 2, 'logManage', 'systemManage/logManage/index', NULL, 1, 0, 'C', '0', '0', NULL, 'logManage', '', NULL, '', NULL, '');
INSERT INTO `sys_menu` VALUES (3026, '升级管理', 3008, 4, 'upgradeManage', 'systemManage/upgradeManage/index', NULL, 1, 0, 'C', '0', '0', NULL, 'upgradeManage', '', NULL, '', NULL, '');
INSERT INTO `sys_menu` VALUES (3027, '许可证管理', 3008, 5, 'licenseManage', 'systemManage/licenseManage/index', NULL, 1, 0, 'C', '0', '0', NULL, 'licenseManage', '', NULL, '', NULL, '');
INSERT INTO `sys_menu` VALUES (3028, '系统配置', 3008, 6, 'systemConfig', NULL, NULL, 1, 0, 'M', '1', '0', NULL, 'systemConfig', '', NULL, '', NULL, '');
INSERT INTO `sys_menu` VALUES (3029, '系统信息', 3008, 1, 'SystemInfo', 'sasSystem/sysConfig/SystemInfo', NULL, 1, 0, 'C', '0', '0', NULL, 'SystemInfo', '', NULL, '', NULL, '');
INSERT INTO `sys_menu` VALUES (3031, '网络管理', 3008, 3, 'NetworkManage', 'sasSystem/sysConfig/NetworkManage', NULL, 1, 0, 'C', '0', '0', NULL, 'NetworkManage', '', NULL, '', NULL, '');
INSERT INTO `sys_menu` VALUES (3032, '系统工具', 3008, 4, 'SystemTool', 'sasSystem/sysConfig/SystemTool', NULL, 1, 0, 'C', '0', '0', NULL, '#', '', NULL, '', NULL, '');
INSERT INTO `sys_menu` VALUES (3033, '日期时间管理', 3008, 5, 'DateManage', 'sasSystem/sysConfig/DateManage', NULL, 1, 0, 'C', '0', '0', NULL, '#', '', NULL, '', NULL, '');
INSERT INTO `sys_menu` VALUES (3035, '日志备份与恢复', 3008, 7, 'IndexBackup', 'sasSystem/sysConfig/IndexBackup', NULL, 1, 0, 'C', '0', '0', NULL, '#', '', NULL, '', NULL, '');
INSERT INTO `sys_menu` VALUES (3038, '系统关机与重启', 3008, 10, 'Shutdown', 'sasSystem/sysConfig/Shutdown', NULL, 1, 0, 'C', '0', '0', NULL, '#', '', NULL, '', NULL, '');
INSERT INTO `sys_menu` VALUES (3039, '系统参数配置', 3008, 11, 'SystemParamManage', 'sasSystem/sysConfig/SystemParamManage', NULL, 1, 0, 'C', '0', '0', NULL, '#', '', NULL, '', NULL, '');
INSERT INTO `sys_menu` VALUES (3040, '配置管理', 3008, 12, 'SystemSettings', 'sasSystem/sysConfig/SystemSettings', NULL, 1, 0, 'C', '0', '0', NULL, '#', '', NULL, '', NULL, '');
INSERT INTO `sys_menu` VALUES (3041, '重置平台初始化口令', 3008, 13, 'ResetAdminPwd', 'sasSystem/sysConfig/ResetAdminPwd', NULL, 1, 0, 'C', '0', '0', NULL, '#', '', NULL, '', NULL, '');

-- ----------------------------
-- Table structure for sys_notice
-- ----------------------------
DROP TABLE IF EXISTS `sys_notice`;
CREATE TABLE `sys_notice`  (
  `notice_id` int NOT NULL AUTO_INCREMENT COMMENT '公告ID',
  `notice_title` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '公告标题',
  `notice_type` char(1) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '公告类型（1:阀值告警 2:告警监控）',
  `notice_content` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '公告内容',
  `status` char(1) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '0' COMMENT '公告状态（0正常 1关闭）',
  `create_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '备注',
  `relation_id` varchar(80) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '关联ID',
  PRIMARY KEY (`notice_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 30451 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '通知公告表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_notice
-- ----------------------------

-- ----------------------------
-- Table structure for sys_oper_log
-- ----------------------------
DROP TABLE IF EXISTS `sys_oper_log`;
CREATE TABLE `sys_oper_log`  (
  `oper_id` bigint NOT NULL AUTO_INCREMENT COMMENT '日志主键',
  `title` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '模块标题',
  `business_type` int NULL DEFAULT 0 COMMENT '业务类型（0其它 1新增 2修改 3删除）',
  `method` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '方法名称',
  `request_method` varchar(10) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '请求方式',
  `operator_type` int NULL DEFAULT 0 COMMENT '操作类别（0其它 1后台用户 2手机端用户）',
  `oper_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '操作人员',
  `dept_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '部门名称',
  `oper_url` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '请求URL',
  `oper_ip` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '主机地址',
  `oper_location` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '操作地点',
  `oper_param` varchar(2000) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '请求参数',
  `json_result` varchar(2000) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '返回参数',
  `status` int NULL DEFAULT 0 COMMENT '操作状态（0正常 1异常）',
  `error_msg` varchar(2000) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '错误消息',
  `oper_time` datetime NULL DEFAULT NULL COMMENT '操作时间',
  `cost_time` bigint NULL DEFAULT 0 COMMENT '消耗时间',
  `level` int NULL DEFAULT NULL COMMENT '级别：1=告警,2=警示,3=通知,4=信息',
  `type` int NULL DEFAULT NULL COMMENT '类型：1、配置审计 2、审计事件 3、系统事件',
  `mode` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '管理方式：暂时全为web',
  PRIMARY KEY (`oper_id`) USING BTREE,
  INDEX `idx_sys_oper_log_bt`(`business_type` ASC) USING BTREE,
  INDEX `idx_sys_oper_log_s`(`status` ASC) USING BTREE,
  INDEX `idx_sys_oper_log_ot`(`oper_time` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 72064 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '操作日志记录' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_oper_log
-- ----------------------------

-- ----------------------------
-- Table structure for sys_post
-- ----------------------------
DROP TABLE IF EXISTS `sys_post`;
CREATE TABLE `sys_post`  (
  `post_id` bigint NOT NULL AUTO_INCREMENT COMMENT '岗位ID',
  `post_code` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '岗位编码',
  `post_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '岗位名称',
  `post_sort` int NOT NULL COMMENT '显示顺序',
  `status` char(1) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '状态（0正常 1停用）',
  `create_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`post_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '岗位信息表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_post
-- ----------------------------

-- ----------------------------
-- Table structure for sys_role
-- ----------------------------
DROP TABLE IF EXISTS `sys_role`;
CREATE TABLE `sys_role`  (
  `role_id` bigint NOT NULL AUTO_INCREMENT COMMENT '角色ID',
  `role_name` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '角色名称',
  `role_key` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '角色权限字符串',
  `role_sort` int NULL DEFAULT NULL COMMENT '显示顺序',
  `data_scope` char(1) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '1' COMMENT '数据范围（1：全部数据权限 2：自定数据权限 3：本部门数据权限 4：本部门及以下数据权限）',
  `menu_check_strictly` tinyint(1) NULL DEFAULT 1 COMMENT '菜单树选择项是否关联显示',
  `dept_check_strictly` tinyint(1) NULL DEFAULT 1 COMMENT '部门树选择项是否关联显示',
  `status` char(1) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '0' COMMENT '角色状态（0正常 1停用）',
  `del_flag` char(1) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `create_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '备注',
  `sys_default` varchar(1) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '0' COMMENT '系统默认（否-0是-1）',
  `description` varchar(2000) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '描述',
  PRIMARY KEY (`role_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 146 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '角色信息表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_role
-- ----------------------------
INSERT INTO `sys_role` VALUES (103, '安全审计员', NULL, NULL, '1', 1, 1, '0', '0', 'admin', NULL, '', NULL, NULL, '1', NULL);
INSERT INTO `sys_role` VALUES (104, '安全管理员', NULL, NULL, '1', 1, 1, '0', '0', '', NULL, '', NULL, NULL, '1', NULL);
INSERT INTO `sys_role` VALUES (105, '系统管理员', NULL, NULL, '1', 0, 1, '0', '0', '', NULL, '', NULL, NULL, '1', NULL);

-- ----------------------------
-- Table structure for sys_role_dept
-- ----------------------------
DROP TABLE IF EXISTS `sys_role_dept`;
CREATE TABLE `sys_role_dept`  (
  `role_id` bigint NOT NULL COMMENT '角色ID',
  `dept_id` bigint NOT NULL COMMENT '部门ID',
  PRIMARY KEY (`role_id`, `dept_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '角色和部门关联表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_role_dept
-- ----------------------------

-- ----------------------------
-- Table structure for sys_role_menu
-- ----------------------------
DROP TABLE IF EXISTS `sys_role_menu`;
CREATE TABLE `sys_role_menu`  (
  `role_id` bigint NOT NULL COMMENT '角色ID',
  `menu_id` bigint NOT NULL COMMENT '菜单ID',
  `authority` varchar(1) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '读写权限（0-读1-读写）'
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '角色和菜单关联表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_role_menu
-- ----------------------------
INSERT INTO `sys_role_menu` VALUES (103, 3008, NULL);
INSERT INTO `sys_role_menu` VALUES (103, 3023, NULL);
INSERT INTO `sys_role_menu` VALUES (103, 3024, NULL);
INSERT INTO `sys_role_menu` VALUES (104, 3000, NULL);
INSERT INTO `sys_role_menu` VALUES (104, 3001, NULL);
INSERT INTO `sys_role_menu` VALUES (104, 3002, NULL);
INSERT INTO `sys_role_menu` VALUES (104, 3003, NULL);
INSERT INTO `sys_role_menu` VALUES (104, 3004, NULL);
INSERT INTO `sys_role_menu` VALUES (104, 3005, NULL);
INSERT INTO `sys_role_menu` VALUES (104, 3006, NULL);
INSERT INTO `sys_role_menu` VALUES (104, 3007, NULL);
INSERT INTO `sys_role_menu` VALUES (104, 3008, NULL);
INSERT INTO `sys_role_menu` VALUES (104, 3009, NULL);
INSERT INTO `sys_role_menu` VALUES (104, 3010, NULL);
INSERT INTO `sys_role_menu` VALUES (104, 3011, NULL);
INSERT INTO `sys_role_menu` VALUES (104, 3012, NULL);
INSERT INTO `sys_role_menu` VALUES (104, 3013, NULL);
INSERT INTO `sys_role_menu` VALUES (104, 3014, NULL);
INSERT INTO `sys_role_menu` VALUES (104, 3015, NULL);
INSERT INTO `sys_role_menu` VALUES (104, 3016, NULL);
INSERT INTO `sys_role_menu` VALUES (104, 3017, NULL);
INSERT INTO `sys_role_menu` VALUES (104, 3018, NULL);
INSERT INTO `sys_role_menu` VALUES (104, 3019, NULL);
INSERT INTO `sys_role_menu` VALUES (104, 3020, NULL);
INSERT INTO `sys_role_menu` VALUES (104, 3021, NULL);
INSERT INTO `sys_role_menu` VALUES (104, 3022, NULL);
INSERT INTO `sys_role_menu` VALUES (104, 3023, NULL);
INSERT INTO `sys_role_menu` VALUES (104, 3024, NULL);
INSERT INTO `sys_role_menu` VALUES (104, 3027, NULL);
INSERT INTO `sys_role_menu` VALUES (104, 3039, NULL);
INSERT INTO `sys_role_menu` VALUES (105, 3008, NULL);
INSERT INTO `sys_role_menu` VALUES (105, 3023, NULL);
INSERT INTO `sys_role_menu` VALUES (105, 3024, NULL);
INSERT INTO `sys_role_menu` VALUES (105, 3025, NULL);
INSERT INTO `sys_role_menu` VALUES (105, 3026, NULL);
INSERT INTO `sys_role_menu` VALUES (105, 3027, NULL);
INSERT INTO `sys_role_menu` VALUES (105, 3028, NULL);
INSERT INTO `sys_role_menu` VALUES (105, 3029, NULL);
INSERT INTO `sys_role_menu` VALUES (105, 3030, NULL);
INSERT INTO `sys_role_menu` VALUES (105, 3031, NULL);
INSERT INTO `sys_role_menu` VALUES (105, 3032, NULL);
INSERT INTO `sys_role_menu` VALUES (105, 3033, NULL);
INSERT INTO `sys_role_menu` VALUES (105, 3035, NULL);
INSERT INTO `sys_role_menu` VALUES (105, 3037, NULL);
INSERT INTO `sys_role_menu` VALUES (105, 3038, NULL);
INSERT INTO `sys_role_menu` VALUES (105, 3039, NULL);
INSERT INTO `sys_role_menu` VALUES (105, 3040, NULL);

-- ----------------------------
-- Table structure for sys_timezone
-- ----------------------------
DROP TABLE IF EXISTS `sys_timezone`;
CREATE TABLE `sys_timezone`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '时区名称',
  `parent_id` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '父级ID',
  `is_current` int NULL DEFAULT 0 COMMENT '是否当前时区：0、否 1、是',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 563 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '日志审计-时区表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_timezone
-- ----------------------------
INSERT INTO `sys_timezone` VALUES (1, 'Abidjan', 'Africa', 0);
INSERT INTO `sys_timezone` VALUES (2, 'Accra', 'Africa', 0);
INSERT INTO `sys_timezone` VALUES (3, 'Addis_Ababa', 'Africa', 0);
INSERT INTO `sys_timezone` VALUES (4, 'Algiers', 'Africa', 0);
INSERT INTO `sys_timezone` VALUES (5, 'Asmara', 'Africa', 0);
INSERT INTO `sys_timezone` VALUES (6, 'Asmera', 'Africa', 0);
INSERT INTO `sys_timezone` VALUES (7, 'Bamako', 'Africa', 0);
INSERT INTO `sys_timezone` VALUES (8, 'Bangui', 'Africa', 0);
INSERT INTO `sys_timezone` VALUES (9, 'Banjul', 'Africa', 0);
INSERT INTO `sys_timezone` VALUES (10, 'Bissau', 'Africa', 0);
INSERT INTO `sys_timezone` VALUES (11, 'Blantyre', 'Africa', 0);
INSERT INTO `sys_timezone` VALUES (12, 'Brazzaville', 'Africa', 0);
INSERT INTO `sys_timezone` VALUES (13, 'Bujumbura', 'Africa', 0);
INSERT INTO `sys_timezone` VALUES (14, 'Cairo', 'Africa', 0);
INSERT INTO `sys_timezone` VALUES (15, 'Casablanca', 'Africa', 0);
INSERT INTO `sys_timezone` VALUES (16, 'Ceuta', 'Africa', 0);
INSERT INTO `sys_timezone` VALUES (17, 'Conakry', 'Africa', 0);
INSERT INTO `sys_timezone` VALUES (18, 'Dakar', 'Africa', 0);
INSERT INTO `sys_timezone` VALUES (19, 'Dar_es_Salaam', 'Africa', 0);
INSERT INTO `sys_timezone` VALUES (20, 'Djibouti', 'Africa', 0);
INSERT INTO `sys_timezone` VALUES (21, 'Douala', 'Africa', 0);
INSERT INTO `sys_timezone` VALUES (22, 'El_Aaiun', 'Africa', 0);
INSERT INTO `sys_timezone` VALUES (23, 'Freetown', 'Africa', 0);
INSERT INTO `sys_timezone` VALUES (24, 'Gaborone', 'Africa', 0);
INSERT INTO `sys_timezone` VALUES (25, 'Harare', 'Africa', 0);
INSERT INTO `sys_timezone` VALUES (26, 'Johannesburg', 'Africa', 0);
INSERT INTO `sys_timezone` VALUES (27, 'Juba', 'Africa', 0);
INSERT INTO `sys_timezone` VALUES (28, 'Kampala', 'Africa', 0);
INSERT INTO `sys_timezone` VALUES (29, 'Khartoum', 'Africa', 0);
INSERT INTO `sys_timezone` VALUES (30, 'Kigali', 'Africa', 0);
INSERT INTO `sys_timezone` VALUES (31, 'Kinshasa', 'Africa', 0);
INSERT INTO `sys_timezone` VALUES (32, 'Lagos', 'Africa', 0);
INSERT INTO `sys_timezone` VALUES (33, 'Libreville', 'Africa', 0);
INSERT INTO `sys_timezone` VALUES (34, 'Lome', 'Africa', 0);
INSERT INTO `sys_timezone` VALUES (35, 'Luanda', 'Africa', 0);
INSERT INTO `sys_timezone` VALUES (36, 'Lubumbashi', 'Africa', 0);
INSERT INTO `sys_timezone` VALUES (37, 'Lusaka', 'Africa', 0);
INSERT INTO `sys_timezone` VALUES (38, 'Malabo', 'Africa', 0);
INSERT INTO `sys_timezone` VALUES (39, 'Maputo', 'Africa', 0);
INSERT INTO `sys_timezone` VALUES (40, 'Maseru', 'Africa', 0);
INSERT INTO `sys_timezone` VALUES (41, 'Mbabane', 'Africa', 0);
INSERT INTO `sys_timezone` VALUES (42, 'Mogadishu', 'Africa', 0);
INSERT INTO `sys_timezone` VALUES (43, 'Monrovia', 'Africa', 0);
INSERT INTO `sys_timezone` VALUES (44, 'Nairobi', 'Africa', 0);
INSERT INTO `sys_timezone` VALUES (45, 'Ndjamena', 'Africa', 0);
INSERT INTO `sys_timezone` VALUES (46, 'Niamey', 'Africa', 0);
INSERT INTO `sys_timezone` VALUES (47, 'Nouakchott', 'Africa', 0);
INSERT INTO `sys_timezone` VALUES (48, 'Ouagadougou', 'Africa', 0);
INSERT INTO `sys_timezone` VALUES (49, 'Porto-Novo', 'Africa', 0);
INSERT INTO `sys_timezone` VALUES (50, 'Sao_Tome', 'Africa', 0);
INSERT INTO `sys_timezone` VALUES (51, 'Timbuktu', 'Africa', 0);
INSERT INTO `sys_timezone` VALUES (52, 'Tripoli', 'Africa', 0);
INSERT INTO `sys_timezone` VALUES (53, 'Tunis', 'Africa', 0);
INSERT INTO `sys_timezone` VALUES (54, 'Windhoek', 'Africa', 0);
INSERT INTO `sys_timezone` VALUES (55, 'Adak', 'America', 0);
INSERT INTO `sys_timezone` VALUES (56, 'Anchorage', 'America', 0);
INSERT INTO `sys_timezone` VALUES (57, 'Anguilla', 'America', 0);
INSERT INTO `sys_timezone` VALUES (58, 'Antigua', 'America', 0);
INSERT INTO `sys_timezone` VALUES (59, 'Araguaina', 'America', 0);
INSERT INTO `sys_timezone` VALUES (60, 'Buenos_Aires', 'America', 0);
INSERT INTO `sys_timezone` VALUES (61, 'Catamarca', 'America', 0);
INSERT INTO `sys_timezone` VALUES (62, 'ComodRivadavia', 'America', 0);
INSERT INTO `sys_timezone` VALUES (63, 'Cordoba', 'America', 0);
INSERT INTO `sys_timezone` VALUES (64, 'Jujuy', 'America', 0);
INSERT INTO `sys_timezone` VALUES (65, 'La_Rioja', 'America', 0);
INSERT INTO `sys_timezone` VALUES (66, 'Mendoza', 'America', 0);
INSERT INTO `sys_timezone` VALUES (67, 'Rio_Gallegos', 'America', 0);
INSERT INTO `sys_timezone` VALUES (68, 'Salta', 'America', 0);
INSERT INTO `sys_timezone` VALUES (69, 'San_Juan', 'America', 0);
INSERT INTO `sys_timezone` VALUES (70, 'San_Luis', 'America', 0);
INSERT INTO `sys_timezone` VALUES (71, 'Tucuman', 'America', 0);
INSERT INTO `sys_timezone` VALUES (72, 'Ushuaia', 'America', 0);
INSERT INTO `sys_timezone` VALUES (73, 'Aruba', 'America', 0);
INSERT INTO `sys_timezone` VALUES (74, 'Asuncion', 'America', 0);
INSERT INTO `sys_timezone` VALUES (75, 'Atikokan', 'America', 0);
INSERT INTO `sys_timezone` VALUES (76, 'Atka', 'America', 0);
INSERT INTO `sys_timezone` VALUES (77, 'Bahia', 'America', 0);
INSERT INTO `sys_timezone` VALUES (78, 'Bahia_Banderas', 'America', 0);
INSERT INTO `sys_timezone` VALUES (79, 'Barbados', 'America', 0);
INSERT INTO `sys_timezone` VALUES (80, 'Belem', 'America', 0);
INSERT INTO `sys_timezone` VALUES (81, 'Belize', 'America', 0);
INSERT INTO `sys_timezone` VALUES (82, 'Blanc-Sablon', 'America', 0);
INSERT INTO `sys_timezone` VALUES (83, 'Boa_Vista', 'America', 0);
INSERT INTO `sys_timezone` VALUES (84, 'Bogota', 'America', 0);
INSERT INTO `sys_timezone` VALUES (85, 'Boise', 'America', 0);
INSERT INTO `sys_timezone` VALUES (86, 'Buenos_Aires', 'America', 0);
INSERT INTO `sys_timezone` VALUES (87, 'Cambridge_Bay', 'America', 0);
INSERT INTO `sys_timezone` VALUES (88, 'Campo_Grande', 'America', 0);
INSERT INTO `sys_timezone` VALUES (89, 'Cancun', 'America', 0);
INSERT INTO `sys_timezone` VALUES (90, 'Caracas', 'America', 0);
INSERT INTO `sys_timezone` VALUES (91, 'Catamarca', 'America', 0);
INSERT INTO `sys_timezone` VALUES (92, 'Cayenne', 'America', 0);
INSERT INTO `sys_timezone` VALUES (93, 'Cayman', 'America', 0);
INSERT INTO `sys_timezone` VALUES (94, 'Chicago', 'America', 0);
INSERT INTO `sys_timezone` VALUES (95, 'Chihuahua', 'America', 0);
INSERT INTO `sys_timezone` VALUES (96, 'Coral_Harbour', 'America', 0);
INSERT INTO `sys_timezone` VALUES (97, 'Cordoba', 'America', 0);
INSERT INTO `sys_timezone` VALUES (98, 'Costa_Rica', 'America', 0);
INSERT INTO `sys_timezone` VALUES (99, 'Creston', 'America', 0);
INSERT INTO `sys_timezone` VALUES (100, 'Cuiaba', 'America', 0);
INSERT INTO `sys_timezone` VALUES (101, 'Curacao', 'America', 0);
INSERT INTO `sys_timezone` VALUES (102, 'Danmarkshavn', 'America', 0);
INSERT INTO `sys_timezone` VALUES (103, 'Dawson', 'America', 0);
INSERT INTO `sys_timezone` VALUES (104, 'Dawson_Creek', 'America', 0);
INSERT INTO `sys_timezone` VALUES (105, 'Denver', 'America', 0);
INSERT INTO `sys_timezone` VALUES (106, 'Detroit', 'America', 0);
INSERT INTO `sys_timezone` VALUES (107, 'Dominica', 'America', 0);
INSERT INTO `sys_timezone` VALUES (108, 'Edmonton', 'America', 0);
INSERT INTO `sys_timezone` VALUES (109, 'Eirunepe', 'America', 0);
INSERT INTO `sys_timezone` VALUES (110, 'El_Salvador', 'America', 0);
INSERT INTO `sys_timezone` VALUES (111, 'Ensenada', 'America', 0);
INSERT INTO `sys_timezone` VALUES (112, 'Fort_Nelson', 'America', 0);
INSERT INTO `sys_timezone` VALUES (113, 'Fort_Wayne', 'America', 0);
INSERT INTO `sys_timezone` VALUES (114, 'Fortaleza', 'America', 0);
INSERT INTO `sys_timezone` VALUES (115, 'Glace_Bay', 'America', 0);
INSERT INTO `sys_timezone` VALUES (116, 'Godthab', 'America', 0);
INSERT INTO `sys_timezone` VALUES (117, 'Goose_Bay', 'America', 0);
INSERT INTO `sys_timezone` VALUES (118, 'Grand_Turk', 'America', 0);
INSERT INTO `sys_timezone` VALUES (119, 'Grenada', 'America', 0);
INSERT INTO `sys_timezone` VALUES (120, 'Guadeloupe', 'America', 0);
INSERT INTO `sys_timezone` VALUES (121, 'Guatemala', 'America', 0);
INSERT INTO `sys_timezone` VALUES (122, 'Guayaquil', 'America', 0);
INSERT INTO `sys_timezone` VALUES (123, 'Guyana', 'America', 0);
INSERT INTO `sys_timezone` VALUES (124, 'Halifax', 'America', 0);
INSERT INTO `sys_timezone` VALUES (125, 'Havana', 'America', 0);
INSERT INTO `sys_timezone` VALUES (126, 'Hermosillo', 'America', 0);
INSERT INTO `sys_timezone` VALUES (127, 'Indianapolis', 'America', 0);
INSERT INTO `sys_timezone` VALUES (128, 'Knox', 'America', 0);
INSERT INTO `sys_timezone` VALUES (129, 'Marengo', 'America', 0);
INSERT INTO `sys_timezone` VALUES (130, 'Petersburg', 'America', 0);
INSERT INTO `sys_timezone` VALUES (131, 'Tell_City', 'America', 0);
INSERT INTO `sys_timezone` VALUES (132, 'Vevay', 'America', 0);
INSERT INTO `sys_timezone` VALUES (133, 'Vincennes', 'America', 0);
INSERT INTO `sys_timezone` VALUES (134, 'Winamac', 'America', 0);
INSERT INTO `sys_timezone` VALUES (135, 'Indianapolis', 'America', 0);
INSERT INTO `sys_timezone` VALUES (136, 'Inuvik', 'America', 0);
INSERT INTO `sys_timezone` VALUES (137, 'Iqaluit', 'America', 0);
INSERT INTO `sys_timezone` VALUES (138, 'Jamaica', 'America', 0);
INSERT INTO `sys_timezone` VALUES (139, 'Jujuy', 'America', 0);
INSERT INTO `sys_timezone` VALUES (140, 'Juneau', 'America', 0);
INSERT INTO `sys_timezone` VALUES (141, 'Louisville', 'America', 0);
INSERT INTO `sys_timezone` VALUES (142, 'Monticello', 'America', 0);
INSERT INTO `sys_timezone` VALUES (143, 'Knox_IN', 'America', 0);
INSERT INTO `sys_timezone` VALUES (144, 'Kralendijk', 'America', 0);
INSERT INTO `sys_timezone` VALUES (145, 'La_Paz', 'America', 0);
INSERT INTO `sys_timezone` VALUES (146, 'Lima', 'America', 0);
INSERT INTO `sys_timezone` VALUES (147, 'Los_Angeles', 'America', 0);
INSERT INTO `sys_timezone` VALUES (148, 'Louisville', 'America', 0);
INSERT INTO `sys_timezone` VALUES (149, 'Lower_Princes', 'America', 0);
INSERT INTO `sys_timezone` VALUES (150, 'Maceio', 'America', 0);
INSERT INTO `sys_timezone` VALUES (151, 'Managua', 'America', 0);
INSERT INTO `sys_timezone` VALUES (152, 'Manaus', 'America', 0);
INSERT INTO `sys_timezone` VALUES (153, 'Marigot', 'America', 0);
INSERT INTO `sys_timezone` VALUES (154, 'Martinique', 'America', 0);
INSERT INTO `sys_timezone` VALUES (155, 'Matamoros', 'America', 0);
INSERT INTO `sys_timezone` VALUES (156, 'Mazatlan', 'America', 0);
INSERT INTO `sys_timezone` VALUES (157, 'Mendoza', 'America', 0);
INSERT INTO `sys_timezone` VALUES (158, 'Menominee', 'America', 0);
INSERT INTO `sys_timezone` VALUES (159, 'Merida', 'America', 0);
INSERT INTO `sys_timezone` VALUES (160, 'Metlakatla', 'America', 0);
INSERT INTO `sys_timezone` VALUES (161, 'Mexico_City', 'America', 0);
INSERT INTO `sys_timezone` VALUES (162, 'Miquelon', 'America', 0);
INSERT INTO `sys_timezone` VALUES (163, 'Moncton', 'America', 0);
INSERT INTO `sys_timezone` VALUES (164, 'Monterrey', 'America', 0);
INSERT INTO `sys_timezone` VALUES (165, 'Montevideo', 'America', 0);
INSERT INTO `sys_timezone` VALUES (166, 'Montreal', 'America', 0);
INSERT INTO `sys_timezone` VALUES (167, 'Montserrat', 'America', 0);
INSERT INTO `sys_timezone` VALUES (168, 'Nassau', 'America', 0);
INSERT INTO `sys_timezone` VALUES (169, 'New_York', 'America', 0);
INSERT INTO `sys_timezone` VALUES (170, 'Nipigon', 'America', 0);
INSERT INTO `sys_timezone` VALUES (171, 'Nome', 'America', 0);
INSERT INTO `sys_timezone` VALUES (172, 'Noronha', 'America', 0);
INSERT INTO `sys_timezone` VALUES (173, 'Beulah', 'America', 0);
INSERT INTO `sys_timezone` VALUES (174, 'Center', 'America', 0);
INSERT INTO `sys_timezone` VALUES (175, 'New_Salem', 'America', 0);
INSERT INTO `sys_timezone` VALUES (176, 'Ojinaga', 'America', 0);
INSERT INTO `sys_timezone` VALUES (177, 'Panama', 'America', 0);
INSERT INTO `sys_timezone` VALUES (178, 'Pangnirtung', 'America', 0);
INSERT INTO `sys_timezone` VALUES (179, 'Paramaribo', 'America', 0);
INSERT INTO `sys_timezone` VALUES (180, 'Phoenix', 'America', 0);
INSERT INTO `sys_timezone` VALUES (181, 'Port-au-Prince', 'America', 0);
INSERT INTO `sys_timezone` VALUES (182, 'Port_of_Spain', 'America', 0);
INSERT INTO `sys_timezone` VALUES (183, 'Porto_Acre', 'America', 0);
INSERT INTO `sys_timezone` VALUES (184, 'Porto_Velho', 'America', 0);
INSERT INTO `sys_timezone` VALUES (185, 'Puerto_Rico', 'America', 0);
INSERT INTO `sys_timezone` VALUES (186, 'Punta_Arenas', 'America', 0);
INSERT INTO `sys_timezone` VALUES (187, 'Rainy_River', 'America', 0);
INSERT INTO `sys_timezone` VALUES (188, 'Rankin_Inlet', 'America', 0);
INSERT INTO `sys_timezone` VALUES (189, 'Recife', 'America', 0);
INSERT INTO `sys_timezone` VALUES (190, 'Regina', 'America', 0);
INSERT INTO `sys_timezone` VALUES (191, 'Resolute', 'America', 0);
INSERT INTO `sys_timezone` VALUES (192, 'Rio_Branco', 'America', 0);
INSERT INTO `sys_timezone` VALUES (193, 'Rosario', 'America', 0);
INSERT INTO `sys_timezone` VALUES (194, 'Santa_Isabel', 'America', 0);
INSERT INTO `sys_timezone` VALUES (195, 'Santarem', 'America', 0);
INSERT INTO `sys_timezone` VALUES (196, 'Santiago', 'America', 0);
INSERT INTO `sys_timezone` VALUES (197, 'Santo_Domingo', 'America', 0);
INSERT INTO `sys_timezone` VALUES (198, 'Sao_Paulo', 'America', 0);
INSERT INTO `sys_timezone` VALUES (199, 'Scoresbysund', 'America', 0);
INSERT INTO `sys_timezone` VALUES (200, 'Shiprock', 'America', 0);
INSERT INTO `sys_timezone` VALUES (201, 'Sitka', 'America', 0);
INSERT INTO `sys_timezone` VALUES (202, 'St_Barthelemy', 'America', 0);
INSERT INTO `sys_timezone` VALUES (203, 'St_Johns', 'America', 0);
INSERT INTO `sys_timezone` VALUES (204, 'St_Kitts', 'America', 0);
INSERT INTO `sys_timezone` VALUES (205, 'St_Lucia', 'America', 0);
INSERT INTO `sys_timezone` VALUES (206, 'St_Thomas', 'America', 0);
INSERT INTO `sys_timezone` VALUES (207, 'St_Vincent', 'America', 0);
INSERT INTO `sys_timezone` VALUES (208, 'Swift_Current', 'America', 0);
INSERT INTO `sys_timezone` VALUES (209, 'Tegucigalpa', 'America', 0);
INSERT INTO `sys_timezone` VALUES (210, 'Thule', 'America', 0);
INSERT INTO `sys_timezone` VALUES (211, 'Thunder_Bay', 'America', 0);
INSERT INTO `sys_timezone` VALUES (212, 'Tijuana', 'America', 0);
INSERT INTO `sys_timezone` VALUES (213, 'Toronto', 'America', 0);
INSERT INTO `sys_timezone` VALUES (214, 'Tortola', 'America', 0);
INSERT INTO `sys_timezone` VALUES (215, 'Vancouver', 'America', 0);
INSERT INTO `sys_timezone` VALUES (216, 'Virgin', 'America', 0);
INSERT INTO `sys_timezone` VALUES (217, 'Whitehorse', 'America', 0);
INSERT INTO `sys_timezone` VALUES (218, 'Winnipeg', 'America', 0);
INSERT INTO `sys_timezone` VALUES (219, 'Yakutat', 'America', 0);
INSERT INTO `sys_timezone` VALUES (220, 'Yellowknife', 'America', 0);
INSERT INTO `sys_timezone` VALUES (221, 'Casey', 'Antarctica', 0);
INSERT INTO `sys_timezone` VALUES (222, 'Davis', 'Antarctica', 0);
INSERT INTO `sys_timezone` VALUES (223, 'DumontDUrville', 'Antarctica', 0);
INSERT INTO `sys_timezone` VALUES (224, 'Macquarie', 'Antarctica', 0);
INSERT INTO `sys_timezone` VALUES (225, 'Mawson', 'Antarctica', 0);
INSERT INTO `sys_timezone` VALUES (226, 'McMurdo', 'Antarctica', 0);
INSERT INTO `sys_timezone` VALUES (227, 'Palmer', 'Antarctica', 0);
INSERT INTO `sys_timezone` VALUES (228, 'Rothera', 'Antarctica', 0);
INSERT INTO `sys_timezone` VALUES (229, 'South_Pole', 'Antarctica', 0);
INSERT INTO `sys_timezone` VALUES (230, 'Syowa', 'Antarctica', 0);
INSERT INTO `sys_timezone` VALUES (231, 'Troll', 'Antarctica', 0);
INSERT INTO `sys_timezone` VALUES (232, 'Vostok', 'Antarctica', 0);
INSERT INTO `sys_timezone` VALUES (233, 'Longyearbyen', 'Arctic', 0);
INSERT INTO `sys_timezone` VALUES (234, 'Aden', 'Asia', 0);
INSERT INTO `sys_timezone` VALUES (235, 'Almaty', 'Asia', 0);
INSERT INTO `sys_timezone` VALUES (236, 'Amman', 'Asia', 0);
INSERT INTO `sys_timezone` VALUES (237, 'Anadyr', 'Asia', 0);
INSERT INTO `sys_timezone` VALUES (238, 'Aqtau', 'Asia', 0);
INSERT INTO `sys_timezone` VALUES (239, 'Aqtobe', 'Asia', 0);
INSERT INTO `sys_timezone` VALUES (240, 'Ashgabat', 'Asia', 0);
INSERT INTO `sys_timezone` VALUES (241, 'Ashkhabad', 'Asia', 0);
INSERT INTO `sys_timezone` VALUES (242, 'Atyrau', 'Asia', 0);
INSERT INTO `sys_timezone` VALUES (243, 'Baghdad', 'Asia', 0);
INSERT INTO `sys_timezone` VALUES (244, 'Bahrain', 'Asia', 0);
INSERT INTO `sys_timezone` VALUES (245, 'Baku', 'Asia', 0);
INSERT INTO `sys_timezone` VALUES (246, 'Bangkok', 'Asia', 0);
INSERT INTO `sys_timezone` VALUES (247, 'Barnaul', 'Asia', 0);
INSERT INTO `sys_timezone` VALUES (248, 'Beirut', 'Asia', 0);
INSERT INTO `sys_timezone` VALUES (249, 'Bishkek', 'Asia', 0);
INSERT INTO `sys_timezone` VALUES (250, 'Brunei', 'Asia', 0);
INSERT INTO `sys_timezone` VALUES (251, 'Calcutta', 'Asia', 0);
INSERT INTO `sys_timezone` VALUES (252, 'Chita', 'Asia', 0);
INSERT INTO `sys_timezone` VALUES (253, 'Choibalsan', 'Asia', 0);
INSERT INTO `sys_timezone` VALUES (254, 'Chongqing', 'Asia', 0);
INSERT INTO `sys_timezone` VALUES (255, 'Chungking', 'Asia', 0);
INSERT INTO `sys_timezone` VALUES (256, 'Colombo', 'Asia', 0);
INSERT INTO `sys_timezone` VALUES (257, 'Dacca', 'Asia', 0);
INSERT INTO `sys_timezone` VALUES (258, 'Damascus', 'Asia', 0);
INSERT INTO `sys_timezone` VALUES (259, 'Dhaka', 'Asia', 0);
INSERT INTO `sys_timezone` VALUES (260, 'Dili', 'Asia', 0);
INSERT INTO `sys_timezone` VALUES (261, 'Dubai', 'Asia', 0);
INSERT INTO `sys_timezone` VALUES (262, 'Dushanbe', 'Asia', 0);
INSERT INTO `sys_timezone` VALUES (263, 'Famagusta', 'Asia', 0);
INSERT INTO `sys_timezone` VALUES (264, 'Gaza', 'Asia', 0);
INSERT INTO `sys_timezone` VALUES (265, 'Harbin', 'Asia', 0);
INSERT INTO `sys_timezone` VALUES (266, 'Hebron', 'Asia', 0);
INSERT INTO `sys_timezone` VALUES (267, 'Ho_Chi_Minh', 'Asia', 0);
INSERT INTO `sys_timezone` VALUES (268, 'Hong_Kong', 'Asia', 0);
INSERT INTO `sys_timezone` VALUES (269, 'Hovd', 'Asia', 0);
INSERT INTO `sys_timezone` VALUES (270, 'Irkutsk', 'Asia', 0);
INSERT INTO `sys_timezone` VALUES (271, 'Istanbul', 'Asia', 0);
INSERT INTO `sys_timezone` VALUES (272, 'Jakarta', 'Asia', 0);
INSERT INTO `sys_timezone` VALUES (273, 'Jayapura', 'Asia', 0);
INSERT INTO `sys_timezone` VALUES (274, 'Jerusalem', 'Asia', 0);
INSERT INTO `sys_timezone` VALUES (275, 'Kabul', 'Asia', 0);
INSERT INTO `sys_timezone` VALUES (276, 'Kamchatka', 'Asia', 0);
INSERT INTO `sys_timezone` VALUES (277, 'Karachi', 'Asia', 0);
INSERT INTO `sys_timezone` VALUES (278, 'Kashgar', 'Asia', 0);
INSERT INTO `sys_timezone` VALUES (279, 'Kathmandu', 'Asia', 0);
INSERT INTO `sys_timezone` VALUES (280, 'Katmandu', 'Asia', 0);
INSERT INTO `sys_timezone` VALUES (281, 'Khandyga', 'Asia', 0);
INSERT INTO `sys_timezone` VALUES (282, 'Kolkata', 'Asia', 0);
INSERT INTO `sys_timezone` VALUES (283, 'Krasnoyarsk', 'Asia', 0);
INSERT INTO `sys_timezone` VALUES (284, 'Kuala_Lumpur', 'Asia', 0);
INSERT INTO `sys_timezone` VALUES (285, 'Kuching', 'Asia', 0);
INSERT INTO `sys_timezone` VALUES (286, 'Kuwait', 'Asia', 0);
INSERT INTO `sys_timezone` VALUES (287, 'Macao', 'Asia', 0);
INSERT INTO `sys_timezone` VALUES (288, 'Macau', 'Asia', 0);
INSERT INTO `sys_timezone` VALUES (289, 'Magadan', 'Asia', 0);
INSERT INTO `sys_timezone` VALUES (290, 'Makassar', 'Asia', 0);
INSERT INTO `sys_timezone` VALUES (291, 'Manila', 'Asia', 0);
INSERT INTO `sys_timezone` VALUES (292, 'Muscat', 'Asia', 0);
INSERT INTO `sys_timezone` VALUES (293, 'Nicosia', 'Asia', 0);
INSERT INTO `sys_timezone` VALUES (294, 'Novokuznetsk', 'Asia', 0);
INSERT INTO `sys_timezone` VALUES (295, 'Novosibirsk', 'Asia', 0);
INSERT INTO `sys_timezone` VALUES (296, 'Omsk', 'Asia', 0);
INSERT INTO `sys_timezone` VALUES (297, 'Oral', 'Asia', 0);
INSERT INTO `sys_timezone` VALUES (298, 'Phnom_Penh', 'Asia', 0);
INSERT INTO `sys_timezone` VALUES (299, 'Pontianak', 'Asia', 0);
INSERT INTO `sys_timezone` VALUES (300, 'Pyongyang', 'Asia', 0);
INSERT INTO `sys_timezone` VALUES (301, 'Qatar', 'Asia', 0);
INSERT INTO `sys_timezone` VALUES (302, 'Qyzylorda', 'Asia', 0);
INSERT INTO `sys_timezone` VALUES (303, 'Rangoon', 'Asia', 0);
INSERT INTO `sys_timezone` VALUES (304, 'Riyadh', 'Asia', 0);
INSERT INTO `sys_timezone` VALUES (305, 'Saigon', 'Asia', 0);
INSERT INTO `sys_timezone` VALUES (306, 'Sakhalin', 'Asia', 0);
INSERT INTO `sys_timezone` VALUES (307, 'Samarkand', 'Asia', 0);
INSERT INTO `sys_timezone` VALUES (308, 'Seoul', 'Asia', 0);
INSERT INTO `sys_timezone` VALUES (309, 'Shanghai', 'Asia', 1);
INSERT INTO `sys_timezone` VALUES (310, 'Singapore', 'Asia', 0);
INSERT INTO `sys_timezone` VALUES (311, 'Srednekolymsk', 'Asia', 0);
INSERT INTO `sys_timezone` VALUES (312, 'Taipei', 'Asia', 0);
INSERT INTO `sys_timezone` VALUES (313, 'Tashkent', 'Asia', 0);
INSERT INTO `sys_timezone` VALUES (314, 'Tbilisi', 'Asia', 0);
INSERT INTO `sys_timezone` VALUES (315, 'Tehran', 'Asia', 0);
INSERT INTO `sys_timezone` VALUES (316, 'Tel_Aviv', 'Asia', 0);
INSERT INTO `sys_timezone` VALUES (317, 'Thimbu', 'Asia', 0);
INSERT INTO `sys_timezone` VALUES (318, 'Thimphu', 'Asia', 0);
INSERT INTO `sys_timezone` VALUES (319, 'Tokyo', 'Asia', 0);
INSERT INTO `sys_timezone` VALUES (320, 'Tomsk', 'Asia', 0);
INSERT INTO `sys_timezone` VALUES (321, 'Ujung_Pandang', 'Asia', 0);
INSERT INTO `sys_timezone` VALUES (322, 'Ulaanbaatar', 'Asia', 0);
INSERT INTO `sys_timezone` VALUES (323, 'Ulan_Bator', 'Asia', 0);
INSERT INTO `sys_timezone` VALUES (324, 'Urumqi', 'Asia', 0);
INSERT INTO `sys_timezone` VALUES (325, 'Ust-Nera', 'Asia', 0);
INSERT INTO `sys_timezone` VALUES (326, 'Vientiane', 'Asia', 0);
INSERT INTO `sys_timezone` VALUES (327, 'Vladivostok', 'Asia', 0);
INSERT INTO `sys_timezone` VALUES (328, 'Yakutsk', 'Asia', 0);
INSERT INTO `sys_timezone` VALUES (329, 'Yangon', 'Asia', 0);
INSERT INTO `sys_timezone` VALUES (330, 'Yekaterinburg', 'Asia', 0);
INSERT INTO `sys_timezone` VALUES (331, 'Yerevan', 'Asia', 0);
INSERT INTO `sys_timezone` VALUES (332, 'Azores', 'Atlantic', 0);
INSERT INTO `sys_timezone` VALUES (333, 'Bermuda', 'Atlantic', 0);
INSERT INTO `sys_timezone` VALUES (334, 'Canary', 'Atlantic', 0);
INSERT INTO `sys_timezone` VALUES (335, 'Cape_Verde', 'Atlantic', 0);
INSERT INTO `sys_timezone` VALUES (336, 'Faeroe', 'Atlantic', 0);
INSERT INTO `sys_timezone` VALUES (337, 'Faroe', 'Atlantic', 0);
INSERT INTO `sys_timezone` VALUES (338, 'Jan_Mayen', 'Atlantic', 0);
INSERT INTO `sys_timezone` VALUES (339, 'Madeira', 'Atlantic', 0);
INSERT INTO `sys_timezone` VALUES (340, 'Reykjavik', 'Atlantic', 0);
INSERT INTO `sys_timezone` VALUES (341, 'South_Georgia', 'Atlantic', 0);
INSERT INTO `sys_timezone` VALUES (342, 'St_Helena', 'Atlantic', 0);
INSERT INTO `sys_timezone` VALUES (343, 'Stanley', 'Atlantic', 0);
INSERT INTO `sys_timezone` VALUES (344, 'ACT', 'Australia', 0);
INSERT INTO `sys_timezone` VALUES (345, 'Adelaide', 'Australia', 0);
INSERT INTO `sys_timezone` VALUES (346, 'Brisbane', 'Australia', 0);
INSERT INTO `sys_timezone` VALUES (347, 'Broken_Hill', 'Australia', 0);
INSERT INTO `sys_timezone` VALUES (348, 'Canberra', 'Australia', 0);
INSERT INTO `sys_timezone` VALUES (349, 'Currie', 'Australia', 0);
INSERT INTO `sys_timezone` VALUES (350, 'Darwin', 'Australia', 0);
INSERT INTO `sys_timezone` VALUES (351, 'Eucla', 'Australia', 0);
INSERT INTO `sys_timezone` VALUES (352, 'Hobart', 'Australia', 0);
INSERT INTO `sys_timezone` VALUES (353, 'LHI', 'Australia', 0);
INSERT INTO `sys_timezone` VALUES (354, 'Lindeman', 'Australia', 0);
INSERT INTO `sys_timezone` VALUES (355, 'Lord_Howe', 'Australia', 0);
INSERT INTO `sys_timezone` VALUES (356, 'Melbourne', 'Australia', 0);
INSERT INTO `sys_timezone` VALUES (357, 'NSW', 'Australia', 0);
INSERT INTO `sys_timezone` VALUES (358, 'North', 'Australia', 0);
INSERT INTO `sys_timezone` VALUES (359, 'Perth', 'Australia', 0);
INSERT INTO `sys_timezone` VALUES (360, 'Queensland', 'Australia', 0);
INSERT INTO `sys_timezone` VALUES (361, 'South', 'Australia', 0);
INSERT INTO `sys_timezone` VALUES (362, 'Sydney', 'Australia', 0);
INSERT INTO `sys_timezone` VALUES (363, 'Tasmania', 'Australia', 0);
INSERT INTO `sys_timezone` VALUES (364, 'Victoria', 'Australia', 0);
INSERT INTO `sys_timezone` VALUES (365, 'West', 'Australia', 0);
INSERT INTO `sys_timezone` VALUES (366, 'Yancowinna', 'Australia', 0);
INSERT INTO `sys_timezone` VALUES (367, 'Acre', 'Brazil', 0);
INSERT INTO `sys_timezone` VALUES (368, 'DeNoronha', 'Brazil', 0);
INSERT INTO `sys_timezone` VALUES (369, 'East', 'Brazil', 0);
INSERT INTO `sys_timezone` VALUES (370, 'West', 'Brazil', 0);
INSERT INTO `sys_timezone` VALUES (371, 'Atlantic', 'Canada', 0);
INSERT INTO `sys_timezone` VALUES (372, 'Central', 'Canada', 0);
INSERT INTO `sys_timezone` VALUES (373, 'East-Saskatchewan', 'Canada', 0);
INSERT INTO `sys_timezone` VALUES (374, 'Eastern', 'Canada', 0);
INSERT INTO `sys_timezone` VALUES (375, 'Mountain', 'Canada', 0);
INSERT INTO `sys_timezone` VALUES (376, 'Newfoundland', 'Canada', 0);
INSERT INTO `sys_timezone` VALUES (377, 'Pacific', 'Canada', 0);
INSERT INTO `sys_timezone` VALUES (378, 'Saskatchewan', 'Canada', 0);
INSERT INTO `sys_timezone` VALUES (379, 'Yukon', 'Canada', 0);
INSERT INTO `sys_timezone` VALUES (380, 'Continental', 'Chile', 0);
INSERT INTO `sys_timezone` VALUES (381, 'EasterIsland', 'Chile', 0);
INSERT INTO `sys_timezone` VALUES (382, 'GMT', 'Etc', 0);
INSERT INTO `sys_timezone` VALUES (383, 'GMT+0', 'Etc', 0);
INSERT INTO `sys_timezone` VALUES (384, 'GMT+1', 'Etc', 0);
INSERT INTO `sys_timezone` VALUES (385, 'GMT+10', 'Etc', 0);
INSERT INTO `sys_timezone` VALUES (386, 'GMT+11', 'Etc', 0);
INSERT INTO `sys_timezone` VALUES (387, 'GMT+12', 'Etc', 0);
INSERT INTO `sys_timezone` VALUES (388, 'GMT+2', 'Etc', 0);
INSERT INTO `sys_timezone` VALUES (389, 'GMT+3', 'Etc', 0);
INSERT INTO `sys_timezone` VALUES (390, 'GMT+4', 'Etc', 0);
INSERT INTO `sys_timezone` VALUES (391, 'GMT+5', 'Etc', 0);
INSERT INTO `sys_timezone` VALUES (392, 'GMT+6', 'Etc', 0);
INSERT INTO `sys_timezone` VALUES (393, 'GMT+7', 'Etc', 0);
INSERT INTO `sys_timezone` VALUES (394, 'GMT+8', 'Etc', 0);
INSERT INTO `sys_timezone` VALUES (395, 'GMT+9', 'Etc', 0);
INSERT INTO `sys_timezone` VALUES (396, 'GMT-0', 'Etc', 0);
INSERT INTO `sys_timezone` VALUES (397, 'GMT-1', 'Etc', 0);
INSERT INTO `sys_timezone` VALUES (398, 'GMT-10', 'Etc', 0);
INSERT INTO `sys_timezone` VALUES (399, 'GMT-11', 'Etc', 0);
INSERT INTO `sys_timezone` VALUES (400, 'GMT-12', 'Etc', 0);
INSERT INTO `sys_timezone` VALUES (401, 'GMT-13', 'Etc', 0);
INSERT INTO `sys_timezone` VALUES (402, 'GMT-14', 'Etc', 0);
INSERT INTO `sys_timezone` VALUES (403, 'GMT-2', 'Etc', 0);
INSERT INTO `sys_timezone` VALUES (404, 'GMT-3', 'Etc', 0);
INSERT INTO `sys_timezone` VALUES (405, 'GMT-4', 'Etc', 0);
INSERT INTO `sys_timezone` VALUES (406, 'GMT-5', 'Etc', 0);
INSERT INTO `sys_timezone` VALUES (407, 'GMT-6', 'Etc', 0);
INSERT INTO `sys_timezone` VALUES (408, 'GMT-7', 'Etc', 0);
INSERT INTO `sys_timezone` VALUES (409, 'GMT-8', 'Etc', 0);
INSERT INTO `sys_timezone` VALUES (410, 'GMT-9', 'Etc', 0);
INSERT INTO `sys_timezone` VALUES (411, 'GMT0', 'Etc', 0);
INSERT INTO `sys_timezone` VALUES (412, 'Greenwich', 'Etc', 0);
INSERT INTO `sys_timezone` VALUES (413, 'UCT', 'Etc', 0);
INSERT INTO `sys_timezone` VALUES (414, 'UTC', 'Etc', 0);
INSERT INTO `sys_timezone` VALUES (415, 'Universal', 'Etc', 0);
INSERT INTO `sys_timezone` VALUES (416, 'Zulu', 'Etc', 0);
INSERT INTO `sys_timezone` VALUES (417, 'Amsterdam', 'Europe', 0);
INSERT INTO `sys_timezone` VALUES (418, 'Andorra', 'Europe', 0);
INSERT INTO `sys_timezone` VALUES (419, 'Astrakhan', 'Europe', 0);
INSERT INTO `sys_timezone` VALUES (420, 'Athens', 'Europe', 0);
INSERT INTO `sys_timezone` VALUES (421, 'Belfast', 'Europe', 0);
INSERT INTO `sys_timezone` VALUES (422, 'Belgrade', 'Europe', 0);
INSERT INTO `sys_timezone` VALUES (423, 'Berlin', 'Europe', 0);
INSERT INTO `sys_timezone` VALUES (424, 'Bratislava', 'Europe', 0);
INSERT INTO `sys_timezone` VALUES (425, 'Brussels', 'Europe', 0);
INSERT INTO `sys_timezone` VALUES (426, 'Bucharest', 'Europe', 0);
INSERT INTO `sys_timezone` VALUES (427, 'Budapest', 'Europe', 0);
INSERT INTO `sys_timezone` VALUES (428, 'Busingen', 'Europe', 0);
INSERT INTO `sys_timezone` VALUES (429, 'Chisinau', 'Europe', 0);
INSERT INTO `sys_timezone` VALUES (430, 'Copenhagen', 'Europe', 0);
INSERT INTO `sys_timezone` VALUES (431, 'Dublin', 'Europe', 0);
INSERT INTO `sys_timezone` VALUES (432, 'Gibraltar', 'Europe', 0);
INSERT INTO `sys_timezone` VALUES (433, 'Guernsey', 'Europe', 0);
INSERT INTO `sys_timezone` VALUES (434, 'Helsinki', 'Europe', 0);
INSERT INTO `sys_timezone` VALUES (435, 'Isle_of_Man', 'Europe', 0);
INSERT INTO `sys_timezone` VALUES (436, 'Istanbul', 'Europe', 0);
INSERT INTO `sys_timezone` VALUES (437, 'Jersey', 'Europe', 0);
INSERT INTO `sys_timezone` VALUES (438, 'Kaliningrad', 'Europe', 0);
INSERT INTO `sys_timezone` VALUES (439, 'Kiev', 'Europe', 0);
INSERT INTO `sys_timezone` VALUES (440, 'Kirov', 'Europe', 0);
INSERT INTO `sys_timezone` VALUES (441, 'Lisbon', 'Europe', 0);
INSERT INTO `sys_timezone` VALUES (442, 'Ljubljana', 'Europe', 0);
INSERT INTO `sys_timezone` VALUES (443, 'London', 'Europe', 0);
INSERT INTO `sys_timezone` VALUES (444, 'Luxembourg', 'Europe', 0);
INSERT INTO `sys_timezone` VALUES (445, 'Madrid', 'Europe', 0);
INSERT INTO `sys_timezone` VALUES (446, 'Malta', 'Europe', 0);
INSERT INTO `sys_timezone` VALUES (447, 'Mariehamn', 'Europe', 0);
INSERT INTO `sys_timezone` VALUES (448, 'Minsk', 'Europe', 0);
INSERT INTO `sys_timezone` VALUES (449, 'Monaco', 'Europe', 0);
INSERT INTO `sys_timezone` VALUES (450, 'Moscow', 'Europe', 0);
INSERT INTO `sys_timezone` VALUES (451, 'Nicosia', 'Europe', 0);
INSERT INTO `sys_timezone` VALUES (452, 'Oslo', 'Europe', 0);
INSERT INTO `sys_timezone` VALUES (453, 'Paris', 'Europe', 0);
INSERT INTO `sys_timezone` VALUES (454, 'Podgorica', 'Europe', 0);
INSERT INTO `sys_timezone` VALUES (455, 'Prague', 'Europe', 0);
INSERT INTO `sys_timezone` VALUES (456, 'Riga', 'Europe', 0);
INSERT INTO `sys_timezone` VALUES (457, 'Rome', 'Europe', 0);
INSERT INTO `sys_timezone` VALUES (458, 'Samara', 'Europe', 0);
INSERT INTO `sys_timezone` VALUES (459, 'San_Marino', 'Europe', 0);
INSERT INTO `sys_timezone` VALUES (460, 'Sarajevo', 'Europe', 0);
INSERT INTO `sys_timezone` VALUES (461, 'Saratov', 'Europe', 0);
INSERT INTO `sys_timezone` VALUES (462, 'Simferopol', 'Europe', 0);
INSERT INTO `sys_timezone` VALUES (463, 'Skopje', 'Europe', 0);
INSERT INTO `sys_timezone` VALUES (464, 'Sofia', 'Europe', 0);
INSERT INTO `sys_timezone` VALUES (465, 'Stockholm', 'Europe', 0);
INSERT INTO `sys_timezone` VALUES (466, 'Tallinn', 'Europe', 0);
INSERT INTO `sys_timezone` VALUES (467, 'Tirane', 'Europe', 0);
INSERT INTO `sys_timezone` VALUES (468, 'Tiraspol', 'Europe', 0);
INSERT INTO `sys_timezone` VALUES (469, 'Ulyanovsk', 'Europe', 0);
INSERT INTO `sys_timezone` VALUES (470, 'Uzhgorod', 'Europe', 0);
INSERT INTO `sys_timezone` VALUES (471, 'Vaduz', 'Europe', 0);
INSERT INTO `sys_timezone` VALUES (472, 'Vatican', 'Europe', 0);
INSERT INTO `sys_timezone` VALUES (473, 'Vienna', 'Europe', 0);
INSERT INTO `sys_timezone` VALUES (474, 'Vilnius', 'Europe', 0);
INSERT INTO `sys_timezone` VALUES (475, 'Volgograd', 'Europe', 0);
INSERT INTO `sys_timezone` VALUES (476, 'Warsaw', 'Europe', 0);
INSERT INTO `sys_timezone` VALUES (477, 'Zagreb', 'Europe', 0);
INSERT INTO `sys_timezone` VALUES (478, 'Zaporozhye', 'Europe', 0);
INSERT INTO `sys_timezone` VALUES (479, 'Zurich', 'Europe', 0);
INSERT INTO `sys_timezone` VALUES (480, 'Antananarivo', 'Indian', 0);
INSERT INTO `sys_timezone` VALUES (481, 'Chagos', 'Indian', 0);
INSERT INTO `sys_timezone` VALUES (482, 'Christmas', 'Indian', 0);
INSERT INTO `sys_timezone` VALUES (483, 'Cocos', 'Indian', 0);
INSERT INTO `sys_timezone` VALUES (484, 'Comoro', 'Indian', 0);
INSERT INTO `sys_timezone` VALUES (485, 'Kerguelen', 'Indian', 0);
INSERT INTO `sys_timezone` VALUES (486, 'Mahe', 'Indian', 0);
INSERT INTO `sys_timezone` VALUES (487, 'Maldives', 'Indian', 0);
INSERT INTO `sys_timezone` VALUES (488, 'Mauritius', 'Indian', 0);
INSERT INTO `sys_timezone` VALUES (489, 'Mayotte', 'Indian', 0);
INSERT INTO `sys_timezone` VALUES (490, 'Reunion', 'Indian', 0);
INSERT INTO `sys_timezone` VALUES (491, 'BajaNorte', 'Mexico', 0);
INSERT INTO `sys_timezone` VALUES (492, 'BajaSur', 'Mexico', 0);
INSERT INTO `sys_timezone` VALUES (493, 'General', 'Mexico', 0);
INSERT INTO `sys_timezone` VALUES (494, 'Apia', 'Pacific', 0);
INSERT INTO `sys_timezone` VALUES (495, 'Auckland', 'Pacific', 0);
INSERT INTO `sys_timezone` VALUES (496, 'Bougainville', 'Pacific', 0);
INSERT INTO `sys_timezone` VALUES (497, 'Chatham', 'Pacific', 0);
INSERT INTO `sys_timezone` VALUES (498, 'Chuuk', 'Pacific', 0);
INSERT INTO `sys_timezone` VALUES (499, 'Easter', 'Pacific', 0);
INSERT INTO `sys_timezone` VALUES (500, 'Efate', 'Pacific', 0);
INSERT INTO `sys_timezone` VALUES (501, 'Enderbury', 'Pacific', 0);
INSERT INTO `sys_timezone` VALUES (502, 'Fakaofo', 'Pacific', 0);
INSERT INTO `sys_timezone` VALUES (503, 'Fiji', 'Pacific', 0);
INSERT INTO `sys_timezone` VALUES (504, 'Funafuti', 'Pacific', 0);
INSERT INTO `sys_timezone` VALUES (505, 'Galapagos', 'Pacific', 0);
INSERT INTO `sys_timezone` VALUES (506, 'Gambier', 'Pacific', 0);
INSERT INTO `sys_timezone` VALUES (507, 'Guadalcanal', 'Pacific', 0);
INSERT INTO `sys_timezone` VALUES (508, 'Guam', 'Pacific', 0);
INSERT INTO `sys_timezone` VALUES (509, 'Honolulu', 'Pacific', 0);
INSERT INTO `sys_timezone` VALUES (510, 'Johnston', 'Pacific', 0);
INSERT INTO `sys_timezone` VALUES (511, 'Kiritimati', 'Pacific', 0);
INSERT INTO `sys_timezone` VALUES (512, 'Kosrae', 'Pacific', 0);
INSERT INTO `sys_timezone` VALUES (513, 'Kwajalein', 'Pacific', 0);
INSERT INTO `sys_timezone` VALUES (514, 'Majuro', 'Pacific', 0);
INSERT INTO `sys_timezone` VALUES (515, 'Marquesas', 'Pacific', 0);
INSERT INTO `sys_timezone` VALUES (516, 'Midway', 'Pacific', 0);
INSERT INTO `sys_timezone` VALUES (517, 'Nauru', 'Pacific', 0);
INSERT INTO `sys_timezone` VALUES (518, 'Niue', 'Pacific', 0);
INSERT INTO `sys_timezone` VALUES (519, 'Norfolk', 'Pacific', 0);
INSERT INTO `sys_timezone` VALUES (520, 'Noumea', 'Pacific', 0);
INSERT INTO `sys_timezone` VALUES (521, 'Pago_Pago', 'Pacific', 0);
INSERT INTO `sys_timezone` VALUES (522, 'Palau', 'Pacific', 0);
INSERT INTO `sys_timezone` VALUES (523, 'Pitcairn', 'Pacific', 0);
INSERT INTO `sys_timezone` VALUES (524, 'Pohnpei', 'Pacific', 0);
INSERT INTO `sys_timezone` VALUES (525, 'Ponape', 'Pacific', 0);
INSERT INTO `sys_timezone` VALUES (526, 'Port_Moresby', 'Pacific', 0);
INSERT INTO `sys_timezone` VALUES (527, 'Rarotonga', 'Pacific', 0);
INSERT INTO `sys_timezone` VALUES (528, 'Saipan', 'Pacific', 0);
INSERT INTO `sys_timezone` VALUES (529, 'Samoa', 'Pacific', 0);
INSERT INTO `sys_timezone` VALUES (530, 'Tahiti', 'Pacific', 0);
INSERT INTO `sys_timezone` VALUES (531, 'Tarawa', 'Pacific', 0);
INSERT INTO `sys_timezone` VALUES (532, 'Tongatapu', 'Pacific', 0);
INSERT INTO `sys_timezone` VALUES (533, 'Truk', 'Pacific', 0);
INSERT INTO `sys_timezone` VALUES (534, 'Wake', 'Pacific', 0);
INSERT INTO `sys_timezone` VALUES (535, 'Wallis', 'Pacific', 0);
INSERT INTO `sys_timezone` VALUES (536, 'Yap', 'Pacific', 0);
INSERT INTO `sys_timezone` VALUES (537, 'AST4', 'SystemV', 0);
INSERT INTO `sys_timezone` VALUES (538, 'AST4ADT', 'SystemV', 0);
INSERT INTO `sys_timezone` VALUES (539, 'CST6', 'SystemV', 0);
INSERT INTO `sys_timezone` VALUES (540, 'CST6CDT', 'SystemV', 0);
INSERT INTO `sys_timezone` VALUES (541, 'EST5', 'SystemV', 0);
INSERT INTO `sys_timezone` VALUES (542, 'EST5EDT', 'SystemV', 0);
INSERT INTO `sys_timezone` VALUES (543, 'HST10', 'SystemV', 0);
INSERT INTO `sys_timezone` VALUES (544, 'MST7', 'SystemV', 0);
INSERT INTO `sys_timezone` VALUES (545, 'MST7MDT', 'SystemV', 0);
INSERT INTO `sys_timezone` VALUES (546, 'PST8', 'SystemV', 0);
INSERT INTO `sys_timezone` VALUES (547, 'PST8PDT', 'SystemV', 0);
INSERT INTO `sys_timezone` VALUES (548, 'YST9', 'SystemV', 0);
INSERT INTO `sys_timezone` VALUES (549, 'YST9YDT', 'SystemV', 0);
INSERT INTO `sys_timezone` VALUES (550, 'Alaska', 'US', 0);
INSERT INTO `sys_timezone` VALUES (551, 'Aleutian', 'US', 0);
INSERT INTO `sys_timezone` VALUES (552, 'Arizona', 'US', 0);
INSERT INTO `sys_timezone` VALUES (553, 'Central', 'US', 0);
INSERT INTO `sys_timezone` VALUES (554, 'East-Indiana', 'US', 0);
INSERT INTO `sys_timezone` VALUES (555, 'Eastern', 'US', 0);
INSERT INTO `sys_timezone` VALUES (556, 'Hawaii', 'US', 0);
INSERT INTO `sys_timezone` VALUES (557, 'Indiana-Starke', 'US', 0);
INSERT INTO `sys_timezone` VALUES (558, 'Michigan', 'US', 0);
INSERT INTO `sys_timezone` VALUES (559, 'Mountain', 'US', 0);
INSERT INTO `sys_timezone` VALUES (560, 'Pacific', 'US', 0);
INSERT INTO `sys_timezone` VALUES (561, 'Pacific-New', 'US', 0);
INSERT INTO `sys_timezone` VALUES (562, 'Samoa', 'US', 0);

-- ----------------------------
-- Table structure for sys_user
-- ----------------------------
DROP TABLE IF EXISTS `sys_user`;
CREATE TABLE `sys_user`  (
  `user_id` bigint NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `dept_id` bigint NULL DEFAULT NULL COMMENT '部门ID',
  `user_name` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '用户账号',
  `nick_name` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '用户昵称',
  `user_type` varchar(2) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '0' COMMENT '用户类型（00系统用户,01新增用户）',
  `email` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '用户邮箱',
  `phonenumber` varchar(11) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '手机号码',
  `sex` char(1) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '0' COMMENT '用户性别（0男 1女 2未知）',
  `avatar` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '头像地址',
  `password` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '密码',
  `status` char(1) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '0' COMMENT '帐号状态（0正常 1停用）',
  `del_flag` char(1) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `login_ip` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '最后登录IP',
  `login_date` datetime NULL DEFAULT NULL COMMENT '最后登录时间',
  `create_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '备注',
  `description` varchar(2000) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '描述',
  `password_period_day` int NULL DEFAULT 0 COMMENT '密码周期-天',
  `password_period_hour` int NULL DEFAULT 0 COMMENT '密码周期-小时',
  `password_period_minute` int NULL DEFAULT 0 COMMENT '密码周期-分钟',
  `group_id` int NULL DEFAULT NULL COMMENT '用户组ID',
  `telephone_number` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '电话号码',
  `token_account` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '令牌账户',
  `job_number` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '工号',
  `login_flag` char(1) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '登录标识（1-首次登录2-多次登录）',
  PRIMARY KEY (`user_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 137 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '用户信息表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_user
-- ----------------------------
INSERT INTO `sys_user` VALUES (103, NULL, 'sysauditor', '安全审计员', '1', '<EMAIL>', '***********', '0', '', '$2a$10$lltwULVChjBEj4pKhB7RqewxzvCghRD7kfPWwpu/HwUMGtBvUtvDK', '0', '0', NULL, NULL, '', '2024-12-25 15:13:04', 'admin', '2024-12-25 09:10:30', NULL, NULL, NULL, NULL, NULL, 4, '0311-********', NULL, NULL, NULL);
INSERT INTO `sys_user` VALUES (104, NULL, 'syssecurity', '安全管理员', '1', '<EMAIL>', '13311111112', '0', '', '$2a$10$8W9Jc7Hi5FqzqcDdTiMBYOICVphQaLW42AdZ70QM5Px.bZ4c38S7W', '0', '0', NULL, NULL, '', '2024-12-25 15:13:04', NULL, '2024-12-24 14:39:40', NULL, NULL, 0, 0, 0, 3, '0311-12345677', NULL, NULL, NULL);
INSERT INTO `sys_user` VALUES (105, NULL, 'system', '系统管理员', '1', '<EMAIL>', '13311111113', '0', '', '$2a$10$HFc1EOdbCsvfDaWmZwCw1.REWesLjK0KsUcCYMkw9pQQp4egpqFje', '0', '0', NULL, NULL, '', '2024-12-25 15:13:04', NULL, '2024-12-25 15:24:26', NULL, NULL, 0, 0, 0, 2, '0311-12345676', NULL, NULL, NULL);
INSERT INTO `sys_user` VALUES (106, NULL, 'sysadmin', '超级管理员', '1', '<EMAIL>', '13311111114', '0', '', '$2a$10$7a5Rtghktv.p.1YfgFEHrOeQnwuvG/xFJmOcJSSdHAX4E8TXJNV7K', '0', '0', NULL, NULL, '', '2024-12-25 15:13:04', NULL, '2024-12-25 15:43:01', NULL, NULL, 0, 0, 0, 1, '0311-12345675', NULL, NULL, NULL);

-- ----------------------------
-- Table structure for sys_user_group
-- ----------------------------
DROP TABLE IF EXISTS `sys_user_group`;
CREATE TABLE `sys_user_group`  (
  `group_id` bigint NOT NULL AUTO_INCREMENT COMMENT '用户组id',
  `group_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '用户组名称',
  `description` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '用户组描述',
  `auth_role_ids` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '授权角色',
  `secure_object_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '安全对象类别（0-全部1-选择）',
  `secure_objects` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '安全对象',
  `sys_default` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '系统默认（0-否1-是）',
  `create_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '更新人',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`group_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 27 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '用户组管理' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_user_group
-- ----------------------------
INSERT INTO `sys_user_group` VALUES (1, '超级管理员组', '超级管理员组中的成员可以对系统中所有菜单及安全对象进行操作', '103,104,105', '0', NULL, '1', 'sysadmin', '2024-09-10 02:16:04', 'sysadmin', '2024-09-10 02:16:06', NULL);
INSERT INTO `sys_user_group` VALUES (2, '系统管理员组', '系统管理员组中的成员只具有系统管理菜单操作权限，且无安全对象访问权限', '105', '0', NULL, '1', 'sysadmin', '2024-09-10 02:16:16', 'sysadmin', '2024-09-10 02:16:23', NULL);
INSERT INTO `sys_user_group` VALUES (3, '安全管理员组', '安全管理员组中的成员对除系统管理以外的所有菜单进行了功能授权，且可以对所有安全对象有访问权限。', '104', '1', NULL, '1', 'sysadmin', '2024-09-10 02:16:18', 'sysadmin', '2024-09-10 02:16:26', NULL);
INSERT INTO `sys_user_group` VALUES (4, '安全审计员组', '安全审计员组中的成员只具有日志管理菜单操作权限，且无安全对象访问权限', '103', '0', NULL, '1', 'sysadmin', '2024-09-10 02:16:21', 'sysadmin', '2024-09-10 02:16:28', NULL);

-- ----------------------------
-- Table structure for sys_user_ip
-- ----------------------------
DROP TABLE IF EXISTS `sys_user_ip`;
CREATE TABLE `sys_user_ip`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
  `user_id` bigint NULL DEFAULT NULL COMMENT '用户id',
  `ip_type` varchar(1) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '0' COMMENT '(0-IPv4;1-IPv6)',
  `ip_address` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'ip地址',
  `mac_address` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'mac地址',
  `create_by` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建人',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '更新人',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 19 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '系统用户关联ipmac地址表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_user_ip
-- ----------------------------

-- ----------------------------
-- Table structure for sys_user_post
-- ----------------------------
DROP TABLE IF EXISTS `sys_user_post`;
CREATE TABLE `sys_user_post`  (
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `post_id` bigint NOT NULL COMMENT '岗位ID',
  PRIMARY KEY (`user_id`, `post_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '用户与岗位关联表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_user_post
-- ----------------------------

-- ----------------------------
-- Table structure for sys_user_role
-- ----------------------------
DROP TABLE IF EXISTS `sys_user_role`;
CREATE TABLE `sys_user_role`  (
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `role_id` bigint NOT NULL COMMENT '角色ID',
  PRIMARY KEY (`user_id`, `role_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '用户和角色关联表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_user_role
-- ----------------------------
INSERT INTO `sys_user_role` VALUES (100, 100);
INSERT INTO `sys_user_role` VALUES (101, 101);
INSERT INTO `sys_user_role` VALUES (102, 102);
INSERT INTO `sys_user_role` VALUES (103, 103);
INSERT INTO `sys_user_role` VALUES (104, 104);
INSERT INTO `sys_user_role` VALUES (105, 105);
INSERT INTO `sys_user_role` VALUES (106, 106);

-- ----------------------------
-- Table structure for system_business_net
-- ----------------------------
DROP TABLE IF EXISTS `system_business_net`;
CREATE TABLE `system_business_net`  (
  `id` int NOT NULL COMMENT 'id',
  `business_ip` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '业务口IP',
  `gateway` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '网关',
  `dns` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT 'DNS',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of system_business_net
-- ----------------------------
INSERT INTO `system_business_net` VALUES (1, '***********00', '***********', '*******');

-- ----------------------------
-- Table structure for system_info
-- ----------------------------
DROP TABLE IF EXISTS `system_info`;
CREATE TABLE `system_info`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `device_model` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '设备型号',
  `hard_ware_logo` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '硬件标识',
  `software_version` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '软件版本号',
  `software_serial_number` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '软件序列号',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of system_info
-- ----------------------------
INSERT INTO `system_info` VALUES (1, NULL, NULL, NULL, NULL);

-- ----------------------------
-- Table structure for system_log
-- ----------------------------
DROP TABLE IF EXISTS `system_log`;
CREATE TABLE `system_log`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `module_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `info` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '信息',
  `level` int NOT NULL COMMENT '级别 告警=1 警示=2 通知=3 信息=4',
  `timestamp` timestamp NULL DEFAULT NULL COMMENT '时间',
  `type` int NULL DEFAULT NULL COMMENT '类型：1、系统事件 2、接口信息 3、告警事件',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 47 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '系统日志' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of system_log
-- ----------------------------

-- ----------------------------
-- Table structure for system_network_config
-- ----------------------------
DROP TABLE IF EXISTS `system_network_config`;
CREATE TABLE `system_network_config`  (
  `ens_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '网口名称',
  `address_pattern` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '地址模式（0-手动1自动）',
  `mac` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT 'MAC',
  `ip` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT 'IP地址',
  `netmask` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '掩码'
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of system_network_config
-- ----------------------------

-- ----------------------------
-- Table structure for system_parameter
-- ----------------------------
DROP TABLE IF EXISTS `system_parameter`;
CREATE TABLE `system_parameter`  (
  `id` varchar(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `udp_main` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT 'sysLog采集主端口',
  `tcp_main` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT 'snmp trap采集主端口',
  `web_timeout` int NULL DEFAULT NULL COMMENT 'web应用超时时间设置(分钟)',
  `out_switch` int NULL DEFAULT NULL COMMENT '外发开关：1：开，0：关',
  `out_ip` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '外发IP',
  `out_port` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '外发端口号',
  `out_interval` int NULL DEFAULT NULL COMMENT '外发间隔时间',
  `ssh_switch` int NULL DEFAULT NULL COMMENT 'SSH端口开关：1：开，0：关',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '系统参数配置表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of system_parameter
-- ----------------------------
INSERT INTO `system_parameter` VALUES ('1', '514', '162', -1, 0, '', '514', 1,0);

-- ----------------------------
-- Table structure for system_static_route
-- ----------------------------
DROP TABLE IF EXISTS `system_static_route`;
CREATE TABLE `system_static_route`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `connector` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '接口',
  `dest_ip` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '目的ip',
  `gateway` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '网关',
  `mask` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '掩码',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 35 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '静态路由' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of system_static_route
-- ----------------------------

-- ----------------------------
-- Table structure for upgrade_manage
-- ----------------------------
DROP TABLE IF EXISTS `upgrade_manage`;
CREATE TABLE `upgrade_manage`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '升级列表ID',
  `type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '升级类型 1：标准化库 2：基础库',
  `content` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '升级内容',
  `old_version` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '升级前版本',
  `new_version` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '升级后版本',
  `result` int NULL DEFAULT NULL COMMENT '升级结果  0：升级中 1：失败 2：成功',
  `create_time` datetime NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '升级时间',
  `create_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '操作人',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 37 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '升级管理' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of upgrade_manage
-- ----------------------------

-- ----------------------------
-- Table structure for warn_category
-- ----------------------------
DROP TABLE IF EXISTS `warn_category`;
CREATE TABLE `warn_category`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT 'id',
  `warn_id` int NULL DEFAULT NULL COMMENT '告警Id',
  `warn_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '告警名称',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 9 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '告警分类' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of warn_category
-- ----------------------------
INSERT INTO `warn_category` VALUES (1, 1, '恶意软件');
INSERT INTO `warn_category` VALUES (2, 2, '网络攻击');
INSERT INTO `warn_category` VALUES (3, 3, '设备故障');
INSERT INTO `warn_category` VALUES (4, 4, '策略管理');
INSERT INTO `warn_category` VALUES (5, 5, '配置状态');
INSERT INTO `warn_category` VALUES (6, 6, '连接');
INSERT INTO `warn_category` VALUES (7, 7, '审计');

-- ----------------------------
-- Table structure for warn_sub_category
-- ----------------------------
DROP TABLE IF EXISTS `warn_sub_category`;
CREATE TABLE `warn_sub_category`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT 'id',
  `warn_id` int NULL DEFAULT NULL COMMENT '告警id',
  `warn_sub_id` int NULL DEFAULT NULL COMMENT '告警子类id',
  `warn_sub_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '告警子类名称',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 36 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '告警子类' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of warn_sub_category
-- ----------------------------
INSERT INTO `warn_sub_category` VALUES (1, 1, 1, '计算机病毒');
INSERT INTO `warn_sub_category` VALUES (2, 1, 2, '网页病毒');
INSERT INTO `warn_sub_category` VALUES (3, 1, 3, '木马');
INSERT INTO `warn_sub_category` VALUES (4, 1, 4, '漏洞');
INSERT INTO `warn_sub_category` VALUES (5, 1, 5, '蠕虫');
INSERT INTO `warn_sub_category` VALUES (6, 1, 6, '僵尸软件');
INSERT INTO `warn_sub_category` VALUES (7, 1, 7, '其它');
INSERT INTO `warn_sub_category` VALUES (8, 2, 8, '拒绝服务攻击');
INSERT INTO `warn_sub_category` VALUES (9, 2, 9, '后门攻击');
INSERT INTO `warn_sub_category` VALUES (10, 2, 10, '漏洞攻击');
INSERT INTO `warn_sub_category` VALUES (11, 2, 11, '网络扫描探测');
INSERT INTO `warn_sub_category` VALUES (12, 2, 12, '非法访问');
INSERT INTO `warn_sub_category` VALUES (13, 2, 13, '逃避');
INSERT INTO `warn_sub_category` VALUES (14, 2, 14, '欺骗攻击');
INSERT INTO `warn_sub_category` VALUES (15, 2, 15, '流量异常');
INSERT INTO `warn_sub_category` VALUES (16, 3, 17, '硬件故障');
INSERT INTO `warn_sub_category` VALUES (17, 3, 18, '软件故障');
INSERT INTO `warn_sub_category` VALUES (18, 3, 19, '外围设备设施故障');
INSERT INTO `warn_sub_category` VALUES (19, 3, 20, '网络链路故障');
INSERT INTO `warn_sub_category` VALUES (20, 3, 21, '资源告警');
INSERT INTO `warn_sub_category` VALUES (21, 3, 22, '人为破坏');
INSERT INTO `warn_sub_category` VALUES (22, 3, 23, '其它');
INSERT INTO `warn_sub_category` VALUES (23, 4, 33, '策略失效');
INSERT INTO `warn_sub_category` VALUES (24, 4, 34, '其它');
INSERT INTO `warn_sub_category` VALUES (25, 5, 35, '资源调用');
INSERT INTO `warn_sub_category` VALUES (26, 5, 36, '其它');
INSERT INTO `warn_sub_category` VALUES (27, 6, 37, '连接拒绝');
INSERT INTO `warn_sub_category` VALUES (28, 7, 47, '用户行为审计');
INSERT INTO `warn_sub_category` VALUES (29, 7, 49, '网络行为审计');
INSERT INTO `warn_sub_category` VALUES (30, 7, 50, '主机行为审计');
INSERT INTO `warn_sub_category` VALUES (31, 7, 51, '数据库审计');
INSERT INTO `warn_sub_category` VALUES (32, 7, 52, '安全告警审计');
INSERT INTO `warn_sub_category` VALUES (33, 7, 53, '应用系统审计');
INSERT INTO `warn_sub_category` VALUES (34, 7, 54, '访问存取审计');
INSERT INTO `warn_sub_category` VALUES (35, 7, 55, '其它');

-- ----------------------------
-- Table structure for temporary_authorization
-- ----------------------------
DROP TABLE IF EXISTS `temporary_authorization`;
CREATE TABLE `temporary_authorization`  (
  `id` int NOT NULL,
  `start_date` date NULL DEFAULT NULL,
  `days` int NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '临时授权表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of temporary_authorization
-- ----------------------------
INSERT INTO `temporary_authorization` VALUES (1, NULL, 30);

-- ----------------------------
-- View structure for v_sys_user_group
-- ----------------------------
DROP VIEW IF EXISTS `v_sys_user_group`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `v_sys_user_group` AS select `sug`.`group_id` AS `group_id`,substring_index(substring_index(`sug`.`auth_role_ids`,',',(`b`.`help_topic_id` + 1)),',',-(1)) AS `auth_role_id`,`sug`.`group_name` AS `group_name`,`sug`.`description` AS `description`,`sug`.`secure_object_type` AS `secure_object_type`,`sug`.`sys_default` AS `sys_default` from (`sys_user_group` `sug` join `mysql`.`help_topic` `b` on((`b`.`help_topic_id` < ((length(`sug`.`auth_role_ids`) - length(replace(`sug`.`auth_role_ids`,',',''))) + 1)))) group by `sug`.`group_name`,substring_index(substring_index(`sug`.`auth_role_ids`,',',(`b`.`help_topic_id` + 1)),',',-(1));

-- ----------------------------
-- Function structure for func_next_seq
-- ----------------------------
DROP FUNCTION IF EXISTS `func_next_seq`;
delimiter ;;
CREATE DEFINER=`rzsj`@`%` FUNCTION `func_next_seq`(`v_seq_name` varchar(50)) RETURNS int
    DETERMINISTIC
BEGIN
	DECLARE reVal INT;
	UPDATE seq_table
  SET current_val = current_val + 1
  WHERE seq_name = v_seq_name;
  SELECT max(current_val) into reVal from seq_table WHERE seq_name = v_seq_name;
	return reVal;
END
;;
delimiter ;

SET FOREIGN_KEY_CHECKS = 1;
