services:

  # The 'setup' service runs a one-off script which initializes users inside
  # Elasticsearch — such as 'logstash_internal' and 'kibana_system' — with the
  # values of the passwords defined in the '.env' file. It also creates the
  # roles required by some of these users.
  #
  # This task only needs to be performed once, during the *initial* startup of
  # the stack. Any subsequent run will reset the passwords of existing users to
  # the values defined inside the '.env' file, and the built-in roles to their
  # default permissions.
  #
  # By default, it is excluded from the services started by 'docker compose up'
  # due to the non-default profile it belongs to. To run it, either provide the
  # '--profile=setup' CLI flag to Compose commands, or "up" the service by name
  # such as 'docker compose up setup'.
  setup:
    profiles:
      - setup
    image: docker-elk-setup:arm64
    init: true
    volumes:
      - ./setup/entrypoint.sh:/entrypoint.sh:ro,Z
      - ./setup/lib.sh:/lib.sh:ro,Z
      - ./setup/roles:/roles:ro,Z
    environment:
      ELASTIC_PASSWORD: ${ELASTIC_PASSWORD:-}
      LOGSTASH_INTERNAL_PASSWORD: ${LOGSTASH_INTERNAL_PASSWORD:-}
      KIBANA_SYSTEM_PASSWORD: ${KIBANA_SYSTEM_PASSWORD:-}
      METRICBEAT_INTERNAL_PASSWORD: ${METRICBEAT_INTERNAL_PASSWORD:-}
      FILEBEAT_INTERNAL_PASSWORD: ${FILEBEAT_INTERNAL_PASSWORD:-}
      HEARTBEAT_INTERNAL_PASSWORD: ${HEARTBEAT_INTERNAL_PASSWORD:-}
      MONITORING_INTERNAL_PASSWORD: ${MONITORING_INTERNAL_PASSWORD:-}
      BEATS_SYSTEM_PASSWORD: ${BEATS_SYSTEM_PASSWORD:-}
    networks:
      - elk
    depends_on:
      elasticsearch:
        condition: service_healthy

  elasticsearch:
    image: docker-elk-elasticsearch:arm64
    volumes:
      - ./elasticsearch/config/elasticsearch.yml:/usr/share/elasticsearch/config/elasticsearch.yml:ro,Z
      - /data:/usr/share/elasticsearch/data:Z
      - /etc/timezone:/etc/timezone
      - /etc/localtime:/etc/localtime
    ports:
      - 127.0.0.1:9200:9200
      - 127.0.0.1:9300:9300
    environment:
      node.name: elasticsearch
      ES_JAVA_OPTS: -Xms1g -Xmx1g
      # Bootstrap password.
      # Used to initialize the keystore during the initial startup of
      # Elasticsearch. Ignored on subsequent runs.
      ELASTIC_PASSWORD: ${ELASTIC_PASSWORD:-}
      # Use single node discovery in order to disable production mode and avoid bootstrap checks.
      # see: https://www.elastic.co/guide/en/elasticsearch/reference/current/bootstrap-checks.html
      discovery.type: single-node
    healthcheck:
      test: ["CMD-SHELL", "curl -s http://localhost:9200 >/dev/null || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 50
    networks:
      - elk
    restart: always

  logstash:
    image: docker-elk-logstash:arm64
    volumes:
      - ./logstash/config/logstash.yml:/usr/share/logstash/config/logstash.yml:ro,Z
      - ./logstash/config/pipelines.yml:/usr/share/logstash/config/pipelines.yml:ro,Z
      - ./logstash/config/conf.d:/usr/share/logstash/config/conf.d:ro,Z
      - ./logstash/pipeline:/usr/share/logstash/pipeline:ro,Z
      - ./logstash/last_run_metadata:/usr/share/logstash/last_run_metadata:rw,Z
      - ./logstash/mibs:/usr/share/logstash/mibs:ro,Z
      - ./logstash/jdbc:/usr/share/logstash/jdbc:ro,Z
      - /etc/timezone:/etc/timezone
      - /etc/localtime:/etc/localtime
    ports:
      - 127.0.0.1:5044:5044
      - 162:162/udp
      - 127.0.0.1:9600:9600
    environment:
      LS_JAVA_OPTS: -Xms1g -Xmx1g
      LOGSTASH_INTERNAL_PASSWORD: ${LOGSTASH_INTERNAL_PASSWORD:-}
    networks:
      - elk
    depends_on:
      - elasticsearch
    restart: always

  kibana:
    image: docker-elk-kibana:arm64
    volumes:
      - ./kibana/config/kibana.yml:/usr/share/kibana/config/kibana.yml:ro,Z
      - /etc/timezone:/etc/timezone
      - /etc/localtime:/etc/localtime
    ports:
      - 5601:5601
    environment:
      KIBANA_SYSTEM_PASSWORD: ${KIBANA_SYSTEM_PASSWORD:-}
    networks:
      - elk
    depends_on:
      - elasticsearch
    profiles:
      - kibana

  elastalert:
    image: docker-elk-elastalert:arm64
    volumes:
      - ./elastalert/config/elastalert.yaml:/opt/elastalert/config.yaml:ro,Z
      - ./elastalert/config/rules:/opt/elastalert/rules:ro,Z
      - /etc/timezone:/etc/timezone
      - /etc/localtime:/etc/localtime
    environment:
      ES_USERNAME: elastic
      ES_PASSWORD: ${ELASTIC_PASSWORD:-}
    networks:
      - elk
    extra_hosts:
      - "host.docker.internal:host-gateway"
    depends_on:
      elasticsearch:
        condition: service_healthy
    restart: always
  mysql:
    image: mysql:8.0.39
    volumes:
      - /var/lib/mysql:/var/lib/mysql
      - ./mysql/my.cnf:/etc/my.cnf:ro
      - ./mysql/mydir:/mydir:ro
      - ./mysql/source:/docker-entrypoint-initdb.d:ro
      - /etc/timezone:/etc/timezone
      - /etc/localtime:/etc/localtime
    environment:
      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD}
    ports:
      - 127.0.0.1:3306:3306
    networks:
      - elk
    restart: always
  docker-audit:
    image: docker-elk-docker-audit:arm64
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD}
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
      - /etc/timezone:/etc/timezone
      - /etc/localtime:/etc/localtime
    depends_on:
      - mysql
    networks:
      - elk
networks:
  elk:
    driver: bridge
    name: elk_network
