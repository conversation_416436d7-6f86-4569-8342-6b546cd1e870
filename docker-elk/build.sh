#!/bin/bash

# 确保脚本在错误时退出
set -e

# 设置目标平台参数
PLATFORM=${PLATFORM:-"linux/amd64"}  # 默认为 amd64，可以通过环境变量指定 linux/arm64

# 获取架构标签
if [[ "${PLATFORM}" == "linux/amd64" ]]; then
    ARCH="amd64"
elif [[ "${PLATFORM}" == "linux/arm64" ]]; then
    ARCH="arm64"
fi

# 删除现有的 builder（如果存在）
docker buildx rm multiarch-builder || true

# 创建新的支持多架构的 builder
docker buildx create --name multiarch-builder \
    --driver docker-container \
    --platform linux/amd64,linux/arm64 \
    --use

# 确保 builder 已启动并更新
docker buildx inspect multiarch-builder --bootstrap

# 构建所有服务的镜像
services=("setup" "elasticsearch" "logstash" "kibana" "elastalert" "docker-audit" "extensions/curator" "extensions/filebeat")

for service in "${services[@]}"; do
    echo "Building $service for ${PLATFORM}..."
    
    # 获取服务的基本名称（去除路径）
    service_name=$(basename ${service})
    
    # 使用 buildx 构建镜像，架构信息放在tag中
    docker buildx build \
        --platform ${PLATFORM} \
        --load \
        --tag "docker-elk-${service_name}:${ARCH}" \
        ./${service}/

    echo "$service build completed for ${PLATFORM}"
done

echo "All images have been built successfully for ${PLATFORM}"

# 显示构建的镜像
docker images | grep docker-elk 