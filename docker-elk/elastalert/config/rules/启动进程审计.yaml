name: 启动进程审计
type: frequency
index: logstashAlias
is_enabled: false
num_events: 1
realert:
  minutes: 0
timestamp_field: received_time
timeframe:
  minutes: 5
filter:
- query:
    query_string:
      query: +event_type:1000026 +event_subtype:**********
- query:
    query_string:
      query: (+action:create process +action_result:0)
alert:
- post2:
    http_post2_url: http://host.docker.internal:8080/system/analysis/add
    http_post2_payload:
      rule_id: '65'
      rule_type: '2'
      rule_name: 启动进程审计
      event_name: 启动进程审计
      audit_level: '0'
    http_post2_raw_fields:
      event_id: _id
      related_object_name: related_object_name
      related_object_address: related_object_address
      related_object_account: related_object_account
      action_address: action_address
      device_name: device_name
      device_address: device_address
      device_type: device_type
      action_details: action_details
      action: action
      action_result: action_result
    http_post2_headers:
      myauth: f336d96a-4b82-41ca-ae4e-599e00421851
