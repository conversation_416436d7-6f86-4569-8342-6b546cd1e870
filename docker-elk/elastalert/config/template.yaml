name: "" 
type: frequency
index: logstash<PERSON>lias
is_enabled: ${status}
num_events: 1
realert:
  minutes: 0
timestamp_field: received_time
timeframe:
  minutes: 5
filter:
alert:
  - post2:
      jinja_root_name: _new_root
      http_post2_url: "http://host.docker.internal:8080/system/monitoring/add"
      http_post2_payload: |
        {
          "rule_id" : "${strategyId}",
          "detail": "事件ID: {{ _new_root["_id"] }} \n 事件名称：{{ _new_root["event_name"] }} \n 严重级别：{{ _new_root["severity_level"]}} \n 类别：{{ _new_root["event_type"]}} \n 子类：{{ _new_root["event_subtype"]}} \n 源地址：{{ _new_root["source_address"]}} \n 目的地址：{{ _new_root["dest_address"]}} \n 时间：{{ _new_root["@timestamp"]}} "
        }
      http_post2_raw_fields:
        event_id: _id
        action_object_name: device_name
        action_object_address: device_address
        source_ip: source_address
        source_city: source_city
        source_country: source_country
        dest_ip: dest_address
      http_post2_headers:
        myauth: "f336d96a-4b82-41ca-ae4e-599e00421851"
  - post2:
      http_post2_url: "http://host.docker.internal:8080/system/analysis/add"
      http_post2_payload:
        rule_id : "${strategyId}"
        rule_type: "${auditType}"
        rule_name: "${strategyName}"
        event_name: "${auditName}"
        audit_level: "${auditGrade}"
      http_post2_raw_fields:
        event_id: _id
        related_object_name: related_object_name
        related_object_address: related_object_address
        related_object_account: related_object_account
        action_address: action_address
        device_name: device_name
        device_address: device_address
        device_type: device_type
        action_details: action_details
        action: action
        action_result: action_result
      http_post2_headers:
        myauth: "f336d96a-4b82-41ca-ae4e-599e00421851"
