FROM jertel/elastalert2:2.20.0

USER root

# 修改 /opt/elastalert/run.sh 脚本，添加等待 Elasticsearch 启动的逻辑，并设置超时时间为 300 秒
RUN sed -i '1 a # wait for Elasticsearch to start\nSTART_TIME=$(date +%s)\nTIMEOUT=300\nuntil curl -s http://elasticsearch:9200 > /dev/null; do\n  CURRENT_TIME=$(date +%s)\n  ELAPSED_TIME=$((CURRENT_TIME - START_TIME))\n  if [ $ELAPSED_TIME -ge $TIMEOUT ]; then\n    echo "Elasticsearch startup timeout"\n    exit 1\n  fi\n  echo "waiting for Elasticsearch ..."\n  sleep 5\ndone\n' /opt/elastalert/run.sh

USER elastalert