## Metricbeat configuration
## https://github.com/elastic/beats/blob/main/deploy/docker/metricbeat.docker.yml
#

name: metricbeat

metricbeat.config:
  modules:
    path: ${path.config}/modules.d/*.yml
    # Reload module configs as they change:
    reload.enabled: false

metricbeat.autodiscover:
  providers:
    - type: docker
      hints.enabled: true

metricbeat.modules:
- module: elasticsearch
  hosts: [ http://elasticsearch:9200 ]
  username: monitoring_internal
  password: ${MONITORING_INTERNAL_PASSWORD}
  xpack.enabled: true
  period: 10s
  enabled: true
- module: logstash
  hosts: [ http://logstash:9600 ]
  xpack.enabled: true
  period: 10s
  enabled: true
- module: kibana
  hosts: [ http://kibana:5601 ]
  username: monitoring_internal
  password: ${MONITORING_INTERNAL_PASSWORD}
  xpack.enabled: true
  period: 10s
  enabled: true
- module: docker
  metricsets:
    - container
    - cpu
    - diskio
    - healthcheck
    - info
    #- image
    - memory
    - network
  hosts: [ unix:///var/run/docker.sock ]
  period: 10s
  enabled: true

processors:
  - add_cloud_metadata: ~

monitoring:
  enabled: true
  elasticsearch:
    username: beats_system
    password: ${BEATS_SYSTEM_PASSWORD}

output.elasticsearch:
  hosts: [ http://elasticsearch:9200 ]
  username: metricbeat_internal
  password: ${METRICBEAT_INTERNAL_PASSWORD}

## HTTP endpoint for health checking
## https://www.elastic.co/guide/en/beats/metricbeat/current/http-endpoint.html
#

http:
  enabled: true
  host: 0.0.0.0
