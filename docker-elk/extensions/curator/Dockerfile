FROM untergeek/curator:8.0.16

USER root

## 从宿主机复制dick_check二进制文件到容器
COPY ./disk_check /curator/disk_check

## 可执行权限
RUN chmod +x /curator/disk_check

RUN chown nobody:nobody /curator/disk_check

RUN >>/var/spool/cron/crontabs/nobody \
    echo '*/5 * * * * /curator/curator /.curator/delete_log_files_curator.yml'
RUN >>/var/spool/cron/crontabs/nobody \
    echo '*/5 * * * * /curator/disk_check'

ENTRYPOINT ["crond"]
CMD ["-f", "-d8"]
