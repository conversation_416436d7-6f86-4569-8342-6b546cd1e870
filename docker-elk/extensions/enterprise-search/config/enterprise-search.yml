---
## Enterprise Search core configuration
## https://www.elastic.co/guide/en/enterprise-search/current/configuration.html
#

## --------------------- REQUIRED ---------------------

# Encryption keys to protect application secrets.
secret_management.encryption_keys:
  # example:
  #- 680f94e568c90364bedf927b2f0f49609702d3eab9098688585a375b14274546

## ----------------------------------------------------

# IP address Enterprise Search listens on
ent_search.listen_host: 0.0.0.0

# URL at which users reach Enterprise Search / Kibana
ent_search.external_url: http://localhost:3002
kibana.host: http://localhost:5601

# Elasticsearch URL and credentials
elasticsearch.host: http://elasticsearch:9200
elasticsearch.username: elastic
elasticsearch.password: ${ELASTIC_PASSWORD}

# Allow Enterprise Search to modify Elasticsearch settings. Used to enable auto-creation of Elasticsearch indexes.
allow_es_settings_modification: true
