services:
  enterprise-search:
    build:
      context: extensions/enterprise-search/
      args:
        ELASTIC_VERSION: ${ELASTIC_VERSION}
    volumes:
      - ./extensions/enterprise-search/config/enterprise-search.yml:/usr/share/enterprise-search/config/enterprise-search.yml:ro,Z
    environment:
      JAVA_OPTS: -Xms2g -Xmx2g
      ENT_SEARCH_DEFAULT_PASSWORD: 'changeme'
      ELASTIC_PASSWORD: ${ELASTIC_PASSWORD:-}
    ports:
      - 3002:3002
    networks:
      - elk
    depends_on:
      - elasticsearch
