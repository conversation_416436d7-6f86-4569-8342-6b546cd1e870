services:
  fleet-server:
    build:
      context: extensions/fleet/
      args:
        ELASTIC_VERSION: ${ELASTIC_VERSION}
    # Run as 'root' instead of 'elastic-agent' (uid 1000) to allow reading
    # 'docker.sock' and the host's filesystem.
    user: root
    volumes:
      - fleet-server:/usr/share/elastic-agent/state:Z
      - type: bind
        source: /var/lib/docker/containers
        target: /var/lib/docker/containers
        read_only: true
      - type: bind
        source: /var/run/docker.sock
        target: /var/run/docker.sock
        read_only: true
    environment:
      FLEET_SERVER_ENABLE: '1'
      FLEET_SERVER_INSECURE_HTTP: '1'
      FLEET_SERVER_HOST: 0.0.0.0
      FLEET_SERVER_POLICY_ID: fleet-server-policy
      # Fleet plugin in Kibana
      KIBANA_FLEET_SETUP: '1'
      # Enrollment.
      # (a) Auto-enroll using basic authentication
      ELASTICSEARCH_USERNAME: elastic
      ELASTICSEARCH_PASSWORD: ${ELASTIC_PASSWORD:-}
      # (b) Enroll using a pre-generated service token
      #FLEET_SERVER_SERVICE_TOKEN: <service_token>
    ports:
      - 8220:8220
    hostname: fleet-server
    # <PERSON>astic Agent does not retry failed connections to Kibana upon the initial enrollment phase.
    restart: on-failure
    networks:
      - elk
    depends_on:
      - elasticsearch
      - kibana

volumes:
  fleet-server:
