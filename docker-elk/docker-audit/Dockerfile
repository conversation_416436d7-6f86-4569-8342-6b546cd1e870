# 使用轻量级基础镜像
FROM alpine:3.18

RUN echo "http://dl-cdn.alpinelinux.org/alpine/v3.18/main" > /etc/apk/repositories && \
    echo "http://dl-cdn.alpinelinux.org/alpine/v3.18/community" >> /etc/apk/repositories

# 使用不同的镜像源
RUN sed -i 's/dl-cdn.alpinelinux.org/mirrors.aliyun.com/g' /etc/apk/repositories

# 安装必要的工具，带重试机制
RUN apk update && \
    (apk add --no-cache ca-certificates || \
    (sleep 5 && apk update && apk add --no-cache ca-certificates) || \
    (sleep 10 && apk update && apk add --no-cache ca-certificates))

# 设置工作目录
WORKDIR /app

# 复制预编译的二进制文件
COPY docker_audit .

# 设置执行权限
RUN chmod +x docker_audit

# 暴露必要的卷
VOLUME ["/var/run/docker.sock"]

# 设置默认命令
CMD ["./docker_audit"]